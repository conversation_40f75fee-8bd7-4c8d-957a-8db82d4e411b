{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26084, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26084, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26084, "tid": 424, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26084, "tid": 424, "ts": 1749611355685131, "dur": 562, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26084, "tid": 424, "ts": 1749611355688510, "dur": 782, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26084, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26084, "tid": 1, "ts": 1749611355275966, "dur": 3561, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749611355279533, "dur": 16985, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749611355296525, "dur": 20764, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26084, "tid": 424, "ts": 1749611355689296, "dur": 16, "ph": "X", "name": "", "args": {}}, {"pid": 26084, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355274816, "dur": 26101, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355300919, "dur": 378812, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355301582, "dur": 1663, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355303249, "dur": 911, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304163, "dur": 172, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304336, "dur": 8, "ph": "X", "name": "ProcessMessages 20522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304345, "dur": 27, "ph": "X", "name": "ReadAsync 20522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304377, "dur": 1, "ph": "X", "name": "ProcessMessages 1016", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304378, "dur": 17, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304401, "dur": 31, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304438, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304459, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304483, "dur": 2, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304487, "dur": 18, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304507, "dur": 3, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304512, "dur": 20, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304534, "dur": 4, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304539, "dur": 18, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304559, "dur": 3, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304564, "dur": 22, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304587, "dur": 3, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304591, "dur": 15, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304608, "dur": 3, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304612, "dur": 15, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304629, "dur": 3, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304633, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304652, "dur": 3, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304656, "dur": 18, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304676, "dur": 3, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304680, "dur": 18, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304700, "dur": 3, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304704, "dur": 18, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304723, "dur": 3, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304727, "dur": 19, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304748, "dur": 3, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304752, "dur": 19, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304772, "dur": 3, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304776, "dur": 16, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304794, "dur": 3, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304798, "dur": 14, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304814, "dur": 2, "ph": "X", "name": "ProcessMessages 119", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304817, "dur": 16, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304842, "dur": 22, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304866, "dur": 3, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304869, "dur": 19, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304891, "dur": 3, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304895, "dur": 16, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304913, "dur": 3, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304917, "dur": 18, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304936, "dur": 3, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304940, "dur": 19, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304960, "dur": 3, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304964, "dur": 17, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304984, "dur": 3, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355304988, "dur": 15, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305005, "dur": 2, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305008, "dur": 18, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305028, "dur": 3, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305032, "dur": 19, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305053, "dur": 3, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305057, "dur": 19, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305078, "dur": 3, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305082, "dur": 19, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305103, "dur": 3, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305107, "dur": 33, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305142, "dur": 3, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305146, "dur": 14, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305162, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305165, "dur": 18, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305185, "dur": 3, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305190, "dur": 51, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305246, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305285, "dur": 1, "ph": "X", "name": "ProcessMessages 1101", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305287, "dur": 25, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305314, "dur": 15, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305331, "dur": 26, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305361, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305405, "dur": 1, "ph": "X", "name": "ProcessMessages 1248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305407, "dur": 30, "ph": "X", "name": "ReadAsync 1248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305439, "dur": 18, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305459, "dur": 3, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305463, "dur": 17, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305486, "dur": 24, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305512, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305516, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305538, "dur": 3, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305541, "dur": 20, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305563, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305565, "dur": 27, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305594, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305596, "dur": 35, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305633, "dur": 1, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305634, "dur": 25, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305664, "dur": 27, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305692, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305694, "dur": 27, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305725, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305762, "dur": 25, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305788, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305790, "dur": 23, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305816, "dur": 23, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305842, "dur": 27, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305872, "dur": 25, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305898, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305900, "dur": 21, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305925, "dur": 23, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305951, "dur": 24, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355305978, "dur": 64, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306051, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306097, "dur": 1, "ph": "X", "name": "ProcessMessages 1468", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306098, "dur": 18, "ph": "X", "name": "ReadAsync 1468", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306118, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306120, "dur": 22, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306145, "dur": 29, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306177, "dur": 29, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306209, "dur": 28, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306239, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306242, "dur": 21, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306267, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306291, "dur": 63, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306357, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306388, "dur": 25, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306417, "dur": 23, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306443, "dur": 28, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306474, "dur": 27, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306504, "dur": 23, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306530, "dur": 23, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306556, "dur": 27, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355306588, "dur": 591, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307183, "dur": 1, "ph": "X", "name": "ProcessMessages 1277", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307185, "dur": 113, "ph": "X", "name": "ReadAsync 1277", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307300, "dur": 5, "ph": "X", "name": "ProcessMessages 11505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307306, "dur": 21, "ph": "X", "name": "ReadAsync 11505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307331, "dur": 59, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307393, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307445, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307447, "dur": 35, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307483, "dur": 1, "ph": "X", "name": "ProcessMessages 1133", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307486, "dur": 22, "ph": "X", "name": "ReadAsync 1133", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307512, "dur": 29, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307545, "dur": 30, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307576, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307580, "dur": 28, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307612, "dur": 22, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307637, "dur": 21, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307661, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307683, "dur": 29, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307714, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307716, "dur": 32, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307752, "dur": 26, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307782, "dur": 25, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307810, "dur": 22, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307836, "dur": 30, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307874, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307876, "dur": 28, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307905, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307907, "dur": 21, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307931, "dur": 18, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307953, "dur": 29, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307984, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355307986, "dur": 30, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308018, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308020, "dur": 32, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308053, "dur": 1, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308054, "dur": 19, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308077, "dur": 27, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308108, "dur": 23, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308134, "dur": 28, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308164, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308166, "dur": 22, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308191, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308214, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308239, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308270, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308272, "dur": 25, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308299, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308300, "dur": 28, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308340, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308341, "dur": 22, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308367, "dur": 31, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308399, "dur": 1, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308401, "dur": 27, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308431, "dur": 23, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308457, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308459, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308483, "dur": 30, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308516, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308518, "dur": 25, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308546, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308547, "dur": 30, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308579, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308581, "dur": 24, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308608, "dur": 32, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308642, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308644, "dur": 28, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308676, "dur": 20, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308697, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308700, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308725, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308727, "dur": 37, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308767, "dur": 1, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308768, "dur": 21, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308792, "dur": 20, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308815, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308839, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308841, "dur": 26, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308870, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308873, "dur": 32, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308907, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308910, "dur": 29, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308942, "dur": 29, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308973, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355308975, "dur": 28, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309004, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309006, "dur": 28, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309036, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309037, "dur": 29, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309069, "dur": 31, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309103, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309105, "dur": 26, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309135, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309137, "dur": 31, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309171, "dur": 17, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309192, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309213, "dur": 1, "ph": "X", "name": "ProcessMessages 117", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309215, "dur": 29, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309247, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309248, "dur": 27, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309280, "dur": 19, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309301, "dur": 18, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309323, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309346, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309348, "dur": 27, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309377, "dur": 16, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309396, "dur": 27, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309425, "dur": 2, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309428, "dur": 23, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309455, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309501, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309502, "dur": 29, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309534, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309536, "dur": 32, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309570, "dur": 17, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309594, "dur": 30, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309627, "dur": 21, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309687, "dur": 27, "ph": "X", "name": "ReadAsync 7", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309716, "dur": 2, "ph": "X", "name": "ProcessMessages 1671", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309719, "dur": 24, "ph": "X", "name": "ReadAsync 1671", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309746, "dur": 30, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309779, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309781, "dur": 27, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309811, "dur": 26, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309840, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309842, "dur": 34, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309878, "dur": 1, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309879, "dur": 25, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309909, "dur": 28, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309939, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309941, "dur": 36, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355309982, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310021, "dur": 1, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310023, "dur": 30, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310055, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310056, "dur": 29, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310088, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310089, "dur": 31, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310122, "dur": 1, "ph": "X", "name": "ProcessMessages 1192", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310124, "dur": 25, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310153, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310180, "dur": 14, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310198, "dur": 12, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310212, "dur": 26, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310241, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310243, "dur": 31, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310277, "dur": 1, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310280, "dur": 28, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310309, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310311, "dur": 28, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310343, "dur": 31, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310376, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310377, "dur": 25, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310405, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310406, "dur": 61, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310471, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310499, "dur": 29, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310531, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310534, "dur": 26, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310562, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310565, "dur": 30, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310596, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310598, "dur": 26, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310629, "dur": 26, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310659, "dur": 25, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310687, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310689, "dur": 53, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310744, "dur": 1, "ph": "X", "name": "ProcessMessages 1115", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310745, "dur": 27, "ph": "X", "name": "ReadAsync 1115", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310774, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310776, "dur": 62, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310840, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310867, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310869, "dur": 24, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310895, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310897, "dur": 22, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310920, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310922, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310942, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355310977, "dur": 24, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311004, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311007, "dur": 39, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311049, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311085, "dur": 4, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311092, "dur": 21, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311117, "dur": 28, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311147, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311173, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311175, "dur": 16, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311193, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311195, "dur": 42, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311240, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311259, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311262, "dur": 29, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311293, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311301, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311338, "dur": 1, "ph": "X", "name": "ProcessMessages 985", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311340, "dur": 21, "ph": "X", "name": "ReadAsync 985", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311365, "dur": 67, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311436, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311470, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311472, "dur": 29, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311503, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311505, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311526, "dur": 53, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311585, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311611, "dur": 3, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311615, "dur": 19, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311642, "dur": 26, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311669, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311673, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311703, "dur": 18, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311723, "dur": 3, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311727, "dur": 125, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311854, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311858, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311882, "dur": 3, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311886, "dur": 19, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311907, "dur": 3, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311911, "dur": 35, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311947, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311951, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311972, "dur": 3, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311976, "dur": 19, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355311997, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312001, "dur": 34, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312037, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312041, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312062, "dur": 3, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312066, "dur": 19, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312089, "dur": 3, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312094, "dur": 31, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312127, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312131, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312156, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312181, "dur": 86, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312269, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312272, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312293, "dur": 3, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312297, "dur": 18, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312322, "dur": 24, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312348, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312351, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312379, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312381, "dur": 26, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312410, "dur": 23, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312436, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312461, "dur": 3, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312465, "dur": 17, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312485, "dur": 3, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312489, "dur": 18, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312509, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312513, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312536, "dur": 3, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312540, "dur": 15, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312558, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312561, "dur": 28, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312594, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312613, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312615, "dur": 35, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312657, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312690, "dur": 1, "ph": "X", "name": "ProcessMessages 1070", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312692, "dur": 16, "ph": "X", "name": "ReadAsync 1070", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312714, "dur": 143, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312858, "dur": 32, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312892, "dur": 3, "ph": "X", "name": "ProcessMessages 1943", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312897, "dur": 20, "ph": "X", "name": "ReadAsync 1943", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312922, "dur": 16, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312940, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355312944, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313021, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313042, "dur": 3, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313046, "dur": 17, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313068, "dur": 24, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313099, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313129, "dur": 16, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313152, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313174, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313194, "dur": 3, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313198, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313217, "dur": 2, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313221, "dur": 23, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313246, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313250, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313269, "dur": 3, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313273, "dur": 17, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313292, "dur": 3, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313296, "dur": 46, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313344, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313347, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313368, "dur": 3, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313372, "dur": 17, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313391, "dur": 3, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313395, "dur": 24, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313420, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313424, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313447, "dur": 3, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313450, "dur": 19, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313476, "dur": 17, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313494, "dur": 3, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313498, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313522, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313541, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313545, "dur": 19, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313566, "dur": 2, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313570, "dur": 17, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313588, "dur": 2, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313592, "dur": 19, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313613, "dur": 2, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313616, "dur": 21, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313639, "dur": 3, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313643, "dur": 17, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313661, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313665, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313687, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313691, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313706, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313727, "dur": 3, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313731, "dur": 17, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313755, "dur": 15, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313772, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313776, "dur": 19, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313796, "dur": 2, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313800, "dur": 16, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313818, "dur": 3, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313822, "dur": 22, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313846, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313850, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313874, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313895, "dur": 3, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313898, "dur": 45, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313945, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313948, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313968, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313972, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313992, "dur": 3, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355313996, "dur": 22, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314020, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314024, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314044, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314048, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314067, "dur": 2, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314071, "dur": 23, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314096, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314100, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314120, "dur": 3, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314123, "dur": 17, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314143, "dur": 3, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314146, "dur": 24, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314176, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314196, "dur": 3, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314200, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314219, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314223, "dur": 46, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314270, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314274, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314294, "dur": 3, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314298, "dur": 17, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314317, "dur": 3, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314321, "dur": 25, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314348, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314352, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314371, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314375, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314394, "dur": 3, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314398, "dur": 22, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314422, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314426, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314446, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314450, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314470, "dur": 3, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314473, "dur": 23, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314499, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314502, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314525, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314529, "dur": 19, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314549, "dur": 3, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314553, "dur": 21, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314576, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314579, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314597, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314601, "dur": 17, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314620, "dur": 3, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314623, "dur": 18, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314647, "dur": 24, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314677, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314696, "dur": 3, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314700, "dur": 65, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314767, "dur": 3, "ph": "X", "name": "ProcessMessages 2371", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314772, "dur": 17, "ph": "X", "name": "ReadAsync 2371", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314790, "dur": 3, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314794, "dur": 25, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314821, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314825, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314843, "dur": 2, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314846, "dur": 15, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314864, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314867, "dur": 19, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314888, "dur": 3, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314892, "dur": 22, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314916, "dur": 3, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314920, "dur": 20, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314941, "dur": 3, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314945, "dur": 16, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314962, "dur": 3, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314966, "dur": 25, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314994, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355314997, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315016, "dur": 3, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315020, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315041, "dur": 3, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315045, "dur": 21, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315067, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315071, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315089, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315093, "dur": 17, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315112, "dur": 2, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315115, "dur": 18, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315135, "dur": 3, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315139, "dur": 23, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315163, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315167, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315187, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315190, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315215, "dur": 24, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315241, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315244, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315264, "dur": 3, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315268, "dur": 18, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315288, "dur": 3, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315292, "dur": 22, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315316, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315320, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315339, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315343, "dur": 17, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315362, "dur": 3, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315366, "dur": 24, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315392, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315395, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315412, "dur": 3, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315416, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315440, "dur": 17, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315459, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315464, "dur": 21, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315487, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315490, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315512, "dur": 3, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315516, "dur": 17, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315538, "dur": 24, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315563, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315567, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315599, "dur": 15, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315616, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315620, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315638, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315641, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315661, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315664, "dur": 18, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315689, "dur": 21, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315711, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315715, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315733, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315736, "dur": 14, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315752, "dur": 2, "ph": "X", "name": "ProcessMessages 99", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315756, "dur": 18, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315776, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315780, "dur": 23, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315805, "dur": 3, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315809, "dur": 18, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315829, "dur": 2, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315833, "dur": 15, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315850, "dur": 3, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315854, "dur": 14, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315870, "dur": 2, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315873, "dur": 18, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315893, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315897, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315917, "dur": 3, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315921, "dur": 26, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315949, "dur": 3, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315953, "dur": 15, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315970, "dur": 2, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315973, "dur": 15, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315990, "dur": 2, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355315994, "dur": 18, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316013, "dur": 3, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316017, "dur": 15, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316034, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316038, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316062, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316065, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316086, "dur": 89, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316179, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316203, "dur": 280, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316486, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316522, "dur": 3, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316525, "dur": 25, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316556, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316580, "dur": 12, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316594, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316596, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316611, "dur": 10, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316624, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316639, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316661, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316673, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316695, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316708, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316719, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316730, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316741, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316752, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316764, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316775, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316782, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316795, "dur": 12, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316810, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316825, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316841, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316856, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316871, "dur": 7, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316880, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316906, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316922, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316924, "dur": 11, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316937, "dur": 9, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316949, "dur": 12, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316964, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355316984, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317000, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317016, "dur": 10, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317029, "dur": 9, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317039, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317041, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317053, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317069, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317097, "dur": 12, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317112, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317125, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317138, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317151, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317164, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317166, "dur": 11, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317179, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317190, "dur": 8, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317201, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317212, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317239, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317255, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317257, "dur": 10, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317270, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317282, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317309, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317311, "dur": 37, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317350, "dur": 10, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317364, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317376, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317389, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355317423, "dur": 725, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318151, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318152, "dur": 60, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318215, "dur": 9, "ph": "X", "name": "ProcessMessages 1968", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318224, "dur": 11, "ph": "X", "name": "ReadAsync 1968", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318240, "dur": 9, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318252, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318273, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318285, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318305, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318315, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318351, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318361, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318379, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318400, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318421, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318437, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318448, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318461, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318478, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318497, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318509, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318524, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318533, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318545, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318561, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318575, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318586, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318597, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318614, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318624, "dur": 193, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318821, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318838, "dur": 61, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318901, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318920, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318938, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318954, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355318986, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355319008, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355319068, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355319083, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355319112, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355319126, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355319129, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355319173, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355319191, "dur": 2245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355321440, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355321464, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355321466, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355321482, "dur": 101, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355321586, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355321601, "dur": 3763, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325369, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325372, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325410, "dur": 96, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325513, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325540, "dur": 239, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325783, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325808, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325810, "dur": 139, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325953, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325970, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355325998, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326015, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326051, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326063, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326075, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326089, "dur": 343, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326434, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326448, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326460, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326475, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326490, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326503, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326535, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326547, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326569, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326571, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326582, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326606, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326619, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326671, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326682, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326695, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326710, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326721, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326813, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326825, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326856, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326865, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326897, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326909, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326971, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355326993, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327004, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327016, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327043, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327300, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327312, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327319, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327326, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327343, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327360, "dur": 9, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327372, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327384, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327396, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327407, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327456, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327465, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327479, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327541, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327551, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327564, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327567, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327611, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327619, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327650, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327662, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327709, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327723, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327804, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327816, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327888, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355327900, "dur": 102, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328004, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328019, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328088, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328099, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328110, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328144, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328155, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328174, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328189, "dur": 131, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328322, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328336, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328349, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328374, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328390, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328404, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328416, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328428, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328439, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328452, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328464, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328474, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328477, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328489, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328504, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328516, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328532, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328541, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328553, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328565, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328577, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328590, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328598, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328630, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328645, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328656, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328663, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328677, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328690, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328745, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328753, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328809, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328820, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328894, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328905, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328917, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328928, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328940, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328949, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328976, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355328993, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329055, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329064, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329173, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329185, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329197, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329240, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329243, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329258, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329275, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329285, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329300, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329318, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329327, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329344, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329355, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329374, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329389, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329406, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329415, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329431, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329445, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329459, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329534, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329545, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329587, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329599, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329693, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329705, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329716, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329731, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329776, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355329789, "dur": 615, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330407, "dur": 37, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330445, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330447, "dur": 271, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330725, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330751, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330753, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330773, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330795, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330797, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330827, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330868, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355330892, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331052, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331073, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331139, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331164, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331201, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331223, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331338, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331360, "dur": 483, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331847, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331865, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355331867, "dur": 89576, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355421464, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355421470, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355421515, "dur": 2102, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355423621, "dur": 919, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355424545, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355424557, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355424674, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355424688, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355424929, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355424947, "dur": 251, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355425203, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355425220, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355425302, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355425312, "dur": 284, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355425600, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355425615, "dur": 283, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355425902, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355425913, "dur": 389, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355426305, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355426317, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355426555, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355426565, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355426735, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355426752, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355426846, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355426861, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355427092, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355427102, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355427227, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355427250, "dur": 193, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355427446, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355427460, "dur": 190, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355427652, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355427664, "dur": 345, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355428013, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355428027, "dur": 539, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355428570, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355428581, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355428645, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355428656, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355428736, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355428747, "dur": 580, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355429331, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355429347, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355429385, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355429397, "dur": 426, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355429827, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355429840, "dur": 207, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430049, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430059, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430104, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430116, "dur": 317, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430435, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430445, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430473, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430488, "dur": 394, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430883, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430893, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355430997, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431009, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431020, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431030, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431041, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431070, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431087, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431106, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431108, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431125, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431137, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431154, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431166, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431189, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431199, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431211, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431223, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431233, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431245, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431257, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431268, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431281, "dur": 8, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431291, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431305, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431315, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431326, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431336, "dur": 7, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431347, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431358, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431369, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431378, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431380, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431394, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431407, "dur": 9, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431418, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431428, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431441, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431454, "dur": 9, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431465, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431475, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431489, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431503, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431515, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431526, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431535, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431550, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431566, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431575, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431586, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431637, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431652, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431664, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431673, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431794, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431809, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355431820, "dur": 259, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432082, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432097, "dur": 248, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432350, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432365, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432389, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432405, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432438, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432464, "dur": 124, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432592, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432618, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432622, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432651, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432679, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432680, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432711, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432730, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432741, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432765, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432769, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355432785, "dur": 51733, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355484530, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355484534, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355484582, "dur": 3086, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355487671, "dur": 20409, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355508096, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355508101, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355508130, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355508136, "dur": 138251, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355646403, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355646407, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355646452, "dur": 46, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355646501, "dur": 4276, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355650796, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355650800, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355650816, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355650821, "dur": 995, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355651822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355651824, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355651865, "dur": 43, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355651909, "dur": 19006, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355670927, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355670930, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355670949, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355670952, "dur": 447, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355671404, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355671406, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355671448, "dur": 58, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355671508, "dur": 688, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355672201, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355672235, "dur": 534, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749611355672772, "dur": 6716, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26084, "tid": 424, "ts": 1749611355689314, "dur": 1764, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26084, "tid": 8589934592, "ts": 1749611355272984, "dur": 44845, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": 1749611355317833, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": 1749611355317840, "dur": 1140, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26084, "tid": 424, "ts": 1749611355691081, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26084, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26084, "tid": 4294967296, "ts": 1749611355231267, "dur": 449158, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749611355233751, "dur": 34898, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749611355680439, "dur": 2709, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749611355682155, "dur": 65, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749611355683213, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26084, "tid": 424, "ts": 1749611355691090, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749611355299303, "dur": 1395, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749611355300705, "dur": 456, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749611355301305, "dur": 303, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749611355302248, "dur": 427, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_3AF52CA4203FDAC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749611355303377, "dur": 933, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749611355307184, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749611355301618, "dur": 14434, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749611355316062, "dur": 355371, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749611355671438, "dur": 352, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749611355671790, "dur": 69, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749611355671923, "dur": 72, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749611355672081, "dur": 56, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749611355672162, "dur": 1186, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749611355301938, "dur": 14155, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749611355316157, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1749611355316097, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_094A556A01D249DF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749611355316378, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355316439, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1749611355316377, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9C559E57E505F9F5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749611355316546, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355316545, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_C926F01FD99D4D2B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749611355316940, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749611355317118, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749611355317210, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1749611355317450, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749611355317775, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749611355318404, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749611355318809, "dur": 4280, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355323261, "dur": 1575, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355318575, "dur": 6297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749611355324872, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749611355325368, "dur": 1621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749611355326989, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749611355327106, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749611355327371, "dur": 788, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355328316, "dur": 665, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355327357, "dur": 1816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749611355329173, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749611355329232, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749611355329449, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749611355329570, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749611355329934, "dur": 93253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749611355423331, "dur": 324, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355423909, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355424140, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355424961, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355425130, "dur": 5956, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355431155, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355431542, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749611355423190, "dur": 9168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749611355432416, "dur": 239049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355301882, "dur": 14185, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355316088, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355316171, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4740E213CDF2FB27.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749611355316264, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_88B56BCE4990CA03.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749611355316448, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355316616, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1749611355316600, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_743DCD7F83EC4AAC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749611355316706, "dur": 559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355317265, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_743DCD7F83EC4AAC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749611355317336, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749611355317629, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749611355318501, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749611355318562, "dur": 280, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749611355318899, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749611355319372, "dur": 838, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749611355317427, "dur": 3923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749611355321350, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355321461, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749611355321631, "dur": 475, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749611355322161, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749611355322379, "dur": 393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749611355322797, "dur": 2038, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749611355321567, "dur": 3726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749611355325383, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749611355325485, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749611355325797, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749611355325950, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749611355326484, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749611355326595, "dur": 533, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355327128, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749611355327384, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749611355327739, "dur": 642, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355328387, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1749611355328982, "dur": 172, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355329771, "dur": 91606, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1749611355423169, "dur": 1279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749611355424449, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355424653, "dur": 1192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749611355425881, "dur": 1119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749611355427000, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355427076, "dur": 1074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749611355428151, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355429727, "dur": 341, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749611355428550, "dur": 2243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749611355430794, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355431028, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355431322, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355431382, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355431552, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355431652, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749611355432653, "dur": 238833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355301942, "dur": 14158, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355316115, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355316192, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1749611355316103, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_699CA9E1D209A267.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749611355316263, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355316505, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_9CA00EB8472AEF4E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749611355316592, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355316837, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355317307, "dur": 1658, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355318976, "dur": 6647, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355325624, "dur": 958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355326582, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749611355327129, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355327349, "dur": 410, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355328154, "dur": 718, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\UnsafeStream.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749611355327318, "dur": 1562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749611355328880, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355329447, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749611355329517, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749611355329850, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749611355330275, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749611355330467, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355330759, "dur": 92420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355424367, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-math-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355424708, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355425331, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355425797, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355426057, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355426329, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355426743, "dur": 1889, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355429223, "dur": 1945, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355423181, "dur": 8017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749611355431199, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355431541, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.InternalAPIEngineBridge.001.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355431541, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749611355431676, "dur": 1019, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749611355432697, "dur": 238782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355302061, "dur": 14074, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355316137, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7027E12F917228B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749611355316498, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BC5EF78C5461B6C8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749611355316608, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749611355316607, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_6C566B45F0C804AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749611355316780, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355317047, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355317230, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355317511, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355318309, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355318546, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355319418, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355319626, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355319843, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355320061, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355320470, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355320682, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355320979, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355321217, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355321627, "dur": 798, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Time\\WaitForNextFrameUnit.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749611355321452, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355322467, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355322664, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355323240, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355323481, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355323703, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355323923, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355324137, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355324716, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355325355, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355325805, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749611355325947, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749611355326063, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749611355326462, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355326525, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749611355326950, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355327003, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749611355327305, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355327386, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749611355327529, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749611355327899, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355328316, "dur": 607, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749611355328071, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749611355329179, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355329468, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355330217, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749611355330334, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749611355330629, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355330720, "dur": 92464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355423648, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749611355423948, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749611355424052, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749611355424413, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749611355425168, "dur": 1582, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Protocols.Json.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749611355423188, "dur": 4580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749611355427769, "dur": 852, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355429313, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749611355431079, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749611355428628, "dur": 2549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749611355431258, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749611355431416, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355431532, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355431624, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749611355432615, "dur": 238820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355302113, "dur": 14094, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355316381, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355316495, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B63D4BDCF9722657.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749611355316647, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355316805, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355317048, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355317287, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749611355317339, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355317931, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355318281, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355318411, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749611355318605, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355319211, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355319686, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355319924, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355320138, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355320347, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355320568, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355320839, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355321052, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355321306, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355321531, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355322236, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355322785, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355322998, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355323421, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355323651, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355324166, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355324722, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355325354, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355325819, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749611355325907, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355325988, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749611355326154, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355326984, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749611355326654, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749611355327446, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355327540, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749611355327694, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749611355327873, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749611355328155, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749611355327992, "dur": 1309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749611355329301, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355329393, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749611355329491, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355329583, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749611355330211, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749611355330322, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749611355330845, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749611355331045, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749611355331119, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749611355331315, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749611355331842, "dur": 152688, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749611355485462, "dur": 19382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749611355485461, "dur": 20436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749611355507692, "dur": 277, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749611355508031, "dur": 138308, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749611355650591, "dur": 20236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749611355650590, "dur": 20240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749611355670853, "dur": 521, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749611355302054, "dur": 14075, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355316174, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1749611355316131, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_006DC43AA5DD858A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749611355316264, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355316458, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355316522, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_264A36B967002A74.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749611355316628, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749611355316627, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2A9C520E2391C187.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749611355317212, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749611355317337, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749611355318213, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749611355318609, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749611355318907, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749611355319485, "dur": 736, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749611355317438, "dur": 3898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749611355321336, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355321467, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355322212, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355322479, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355322695, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355322899, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355323120, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355323340, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355323561, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355323789, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355324009, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355324235, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355324789, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355325350, "dur": 1315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355326667, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749611355326665, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749611355327261, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355327368, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749611355327733, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355328316, "dur": 615, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\inspectors\\IInspectorChangeHandler.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749611355327788, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749611355329327, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355329476, "dur": 93699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355423179, "dur": 1279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749611355425168, "dur": 1586, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipes.AccessControl.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749611355424511, "dur": 3076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749611355429588, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749611355427638, "dur": 2210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749611355429848, "dur": 602, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749611355431080, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749611355430458, "dur": 2086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749611355432610, "dur": 238829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355301907, "dur": 14174, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355316100, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355316217, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E5F938C6E667C796.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749611355316375, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355316508, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E5F938C6E667C796.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749611355316619, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1749611355316582, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_038A17E6D6B77576.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749611355316741, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355316944, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355317018, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355317231, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355317304, "dur": 736, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355318043, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749611355318194, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355318809, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355319214, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355319514, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355319732, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355319957, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355320172, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355320412, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355320621, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355320866, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355321083, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355321332, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355321623, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355322327, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355322710, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355322923, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355323145, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355323370, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355323590, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355323809, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355324019, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355324376, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355324599, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355324978, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355325349, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355325626, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355326672, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749611355326842, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749611355327001, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749611355327311, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749611355327991, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355328170, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355328417, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749611355328578, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749611355328949, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355329299, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355329464, "dur": 752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355330216, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749611355330337, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749611355330628, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355330723, "dur": 92459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355423185, "dur": 1839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749611355425024, "dur": 1802, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355427984, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\dbgshim.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749611355428771, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Transactions.Local.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749611355429031, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749611355429373, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749611355426834, "dur": 3139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749611355429974, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355431078, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749611355431215, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749611355431335, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749611355430037, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749611355432163, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355432341, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749611355432567, "dur": 238861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355301931, "dur": 14156, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355316108, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749611355316160, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1749611355316090, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749611355316246, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355316336, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355316536, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_3AF52CA4203FDAC9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749611355316654, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355316943, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355317140, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355317211, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749611355318308, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355318794, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355319249, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355319445, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355319664, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355319881, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355320109, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355320530, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355320776, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355320989, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355321225, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355321461, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355322159, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355322591, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355322789, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355322995, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355323198, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355323584, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355324310, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355324535, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355325111, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ****************, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355325802, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749611355325984, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749611355326453, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355326897, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355326992, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749611355327114, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355327261, "dur": 901, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749611355328163, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749611355328556, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355328625, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355328792, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749611355328900, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355329043, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749611355329550, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355329803, "dur": 95339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355425172, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749611355425812, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749611355426390, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749611355425145, "dur": 2792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749611355427938, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355429374, "dur": 755, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749611355428007, "dur": 3045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749611355431053, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355431127, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749611355431323, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355431392, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355431570, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749611355432359, "dur": 239104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355302013, "dur": 14105, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355316163, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1749611355316121, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0998A984E55471CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749611355316417, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_A6F00FC0DE1D2E86.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749611355316534, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_270C5DEE22A99EDB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749611355316713, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355317147, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355317336, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355317766, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355317887, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355318009, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749611355318188, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749611355318359, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749611355319209, "dur": 2175, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749611355321384, "dur": 1050, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.AccessControl.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749611355322572, "dur": 1808, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749611355318609, "dur": 5800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355324410, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355324654, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355324725, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355325351, "dur": 1315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355326667, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749611355326800, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749611355327304, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1749611355327302, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_371E356D6F5B6577.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749611355327356, "dur": 803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355328164, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749611355328315, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749611355328310, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749611355328904, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355329020, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355329472, "dur": 1575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355331048, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749611355331179, "dur": 94045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355426742, "dur": 1889, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749611355425227, "dur": 3825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749611355429053, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355431335, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749611355429305, "dur": 2109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749611355431500, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355431554, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355432066, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749611355432435, "dur": 239019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355301981, "dur": 14125, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355316125, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749611355316109, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21B473704285E949.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749611355316283, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1749611355316275, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_AC9CD2324DD83C0A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749611355316455, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355316508, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_B7F0DFA76BCD38A9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749611355316593, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355316716, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355317114, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355317237, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355317350, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749611355317402, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355317458, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355317691, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355317879, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355318381, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749611355318494, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355318555, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355319578, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355319722, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355319859, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355320003, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355320139, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355320393, "dur": 1753, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.State\\Plugin\\Changelogs\\Changelog_1_2_4.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749611355320272, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355322159, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355322293, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355322437, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355322569, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355322701, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355322832, "dur": 127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355322960, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355323396, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355323578, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355323743, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355323888, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355324038, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355324208, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355324500, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355324788, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355325357, "dur": 1121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355326482, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749611355326753, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355327000, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749611355327301, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749611355327822, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355328093, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749611355328377, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355328561, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355329182, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355329529, "dur": 95622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355425168, "dur": 1582, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749611355428626, "dur": 955, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749611355425156, "dur": 4738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749611355429895, "dur": 1241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355431208, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355431515, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355431571, "dur": 824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355432401, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749611355432582, "dur": 238893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355302004, "dur": 14108, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355316165, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1749611355316115, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749611355316430, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355316573, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749611355316572, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EE2E1C75DDFB128E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749611355316793, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355316865, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355317163, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355317734, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355318355, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749611355318526, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355319454, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355319679, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355319911, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355320292, "dur": 2131, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PDNWrapper\\Rectangle.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749611355320133, "dur": 2420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355322553, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355322694, "dur": 127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355322821, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355322954, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355323093, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355323226, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355323367, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355323523, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355323666, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355323822, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355323962, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355324106, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355324323, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355324512, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355324712, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355325363, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355326699, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749611355327039, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355327313, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749611355327799, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355327876, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749611355328255, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355328641, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355328950, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355329299, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355329488, "dur": 93684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355424623, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749611355425127, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749611355423173, "dur": 2365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749611355425582, "dur": 1106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749611355426742, "dur": 1890, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749611355428923, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749611355429222, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749611355426721, "dur": 3676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749611355430398, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355431214, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Drawing.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749611355430460, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749611355431967, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355432071, "dur": 218522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749611355650595, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749611355650595, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749611355650735, "dur": 1057, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749611355651801, "dur": 19657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355302031, "dur": 14093, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355316170, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1749611355316126, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5AA67F5B53C44BAD.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749611355316311, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355316491, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_42EEE2042AED6643.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749611355316603, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355317122, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355317194, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355317472, "dur": 938, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355318424, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6210570011482043853.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749611355318643, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355319214, "dur": 998, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\coreclr.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749611355320245, "dur": 2570, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\aspnetcorev2_inprocess.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749611355319176, "dur": 3841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355323018, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355323233, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355323474, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355323709, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355323920, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355324134, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355324719, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355325361, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355325966, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749611355326072, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355326661, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749611355326522, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749611355327257, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355327379, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749611355327455, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749611355327727, "dur": 791, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355328535, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355329188, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355329519, "dur": 95687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355425208, "dur": 1813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749611355427021, "dur": 969, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355427994, "dur": 1118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749611355429112, "dur": 2115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355431233, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355431400, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749611355431399, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749611355431516, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355431773, "dur": 860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749611355432649, "dur": 238792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355302064, "dur": 14086, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355316152, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B87B178D29F0F556.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749611355316272, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355316526, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_2A2A889EC1E06C22.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749611355316639, "dur": 258, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749611355316638, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_425C305CB5B8A325.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749611355316922, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_425C305CB5B8A325.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749611355317036, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355317226, "dur": 1166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355318392, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749611355318530, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355319393, "dur": 2716, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749611355318782, "dur": 3395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355322177, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355322411, "dur": 2227, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\WindowClose.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749611355322400, "dur": 2542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355324942, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355325352, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355325852, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355326035, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749611355326282, "dur": 984, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355327267, "dur": 1660, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749611355328962, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749611355329062, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355329269, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749611355329704, "dur": 98794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749611355428625, "dur": 728, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749611355429373, "dur": 1716, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749611355431155, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749611355431398, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749611355428499, "dur": 4152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749611355432689, "dur": 238761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355302086, "dur": 14101, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355316584, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355316856, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355317255, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749611355317715, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355317884, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749611355318415, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5521740788080646575.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749611355318640, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355319157, "dur": 1066, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Forms.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749611355320223, "dur": 3142, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749611355319101, "dur": 4509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355323610, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355323828, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355324036, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355324252, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355324858, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355325351, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355325803, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749611355325937, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749611355326060, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749611355326563, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355327026, "dur": 288, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749611355327348, "dur": 1139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749611355327343, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749611355328975, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355329751, "dur": 94286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355424507, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749611355425168, "dur": 3464, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749611355428743, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": 174961**********, "dur": 5114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749611355429153, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749611355429376, "dur": 1784, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749611355431214, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749611355431397, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749611355429374, "dur": 3340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749611355432757, "dur": 238709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355302107, "dur": 14093, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355316236, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355316560, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355316990, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749611355317150, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355317705, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355318318, "dur": 605, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": ****************, "dur": 4222, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Cors.dll"}}, {"pid": 12345, "tid": 15, "ts": ****************, "dur": 1031, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Localization.Routing.dll"}}, {"pid": 12345, "tid": 15, "ts": ****************, "dur": 5595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": ****************, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": ****************, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": ****************, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": ****************, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": ****************, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749611355326448, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355326556, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749611355326983, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749611355326677, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749611355327190, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355327635, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749611355327735, "dur": 811, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355328549, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749611355328844, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355329296, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749611355329414, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749611355329841, "dur": 93814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355423660, "dur": 1149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749611355424810, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355426547, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749611355426744, "dur": 2639, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Unity.AspNetCore.NamedPipeSupport.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749611355424913, "dur": 5461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749611355430374, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355431079, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749611355431334, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749611355431506, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749611355430873, "dur": 1705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749611355432579, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749611355432639, "dur": 238808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355302145, "dur": 14073, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355316220, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_F4A91D512CEBDB1F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749611355316419, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F45A17676A5D3A15.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749611355316518, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1487261004515A9D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749611355316618, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1749611355316606, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_66549C0209C53F09.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749611355316785, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355316933, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355317784, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749611355318192, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749611355318282, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355318453, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749611355318521, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355319515, "dur": 1719, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Debug.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749611355318806, "dur": 2437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355321244, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355321392, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355321535, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355322493, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355322699, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355322903, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355323123, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": ****************, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355323566, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355323796, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355324013, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355324233, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355324437, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355324581, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355325068, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355325364, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355326669, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749611355326766, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355327326, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749611355328038, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355328134, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355328501, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355329189, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355329509, "dur": 95705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355425487, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749611355426666, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749611355425217, "dur": 2742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749611355428625, "dur": 710, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749611355428012, "dur": 2145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749611355430158, "dur": 1086, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355431467, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355431567, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749611355432358, "dur": 239073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355302169, "dur": 14122, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355316454, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355316606, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355316684, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355317007, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749611355317143, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355317506, "dur": 708, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355318216, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7634377931854478575.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749611355318322, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355318903, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355319523, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355319670, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355319816, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355319963, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355320189, "dur": 1932, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PsdFile\\RleWriter.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749611355322136, "dur": 898, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PsdFile\\RleReader.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749611355323111, "dur": 1260, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PsdFile\\Layers\\LayerInfo\\LayerUnicodeName.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749611355320115, "dur": 4641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355324756, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355325360, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355325801, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749611355325981, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749611355326347, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355326465, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749611355326661, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749611355326590, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749611355327160, "dur": 1172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355328403, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355328496, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355329192, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355329502, "dur": 93667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355423172, "dur": 1921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749611355425093, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355425188, "dur": 1769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749611355426957, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355427234, "dur": 1138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749611355428373, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355428640, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749611355429809, "dur": 1153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749611355431140, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355431264, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355431543, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355432027, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749611355432760, "dur": 238685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355302188, "dur": 14216, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355316417, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355316526, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_A8B15C2CFD2ACA00.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749611355316631, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749611355316631, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_20B214415B84035D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749611355316747, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355317153, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355317293, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355317519, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355317668, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355317836, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355318179, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749611355318235, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9147286922948009921.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749611355318290, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355318541, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355319530, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355319761, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355319974, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355320186, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355320529, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355320666, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355320999, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355321222, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355321366, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355321603, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\PerSecond.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749611355321557, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355322408, "dur": 713, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\ValueExpression.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749611355322294, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355323175, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355323334, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355323490, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355323634, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355323784, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355323922, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355324070, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355324223, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355324388, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355324526, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355324997, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355325374, "dur": 1407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355326783, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749611355326885, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749611355326988, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749611355327132, "dur": 1022, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749611355328155, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749611355328155, "dur": 982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749611355329138, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355329350, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355329448, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749611355329518, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749611355329736, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355329798, "dur": 93485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355423285, "dur": 1932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749611355425218, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355425287, "dur": 1185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749611355426472, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749611355426743, "dur": 2498, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749611355429348, "dur": 1739, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749611355431396, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-process-l1-1-0.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749611355426543, "dur": 6032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749611355432647, "dur": 238815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355302211, "dur": 14304, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355316565, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A013EEC35583A814.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749611355316702, "dur": 258, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749611355316701, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749611355317141, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355317196, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1749611355317439, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355318212, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749611355318320, "dur": 733, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355319403, "dur": 921, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749611355320325, "dur": 2100, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.Common.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749611355319057, "dur": 3624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355322681, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355323068, "dur": 599, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Analytics\\OnPreprocessBuildAnalytics.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749611355322906, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355323714, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355323937, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355324162, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355324719, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355325360, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355325799, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749611355326662, "dur": 345, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnBecameInvisibleMessageListener.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749611355325953, "dur": 1348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749611355327301, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355327363, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355327790, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749611355327871, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749611355328593, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355328665, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749611355328732, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749611355328977, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355329281, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355329495, "dur": 93679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355423175, "dur": 1695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749611355424871, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355425176, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749611355426289, "dur": 1084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749611355427373, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355427426, "dur": 1073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749611355428499, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355429223, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.CommandLine.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749611355428721, "dur": 1644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749611355430366, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749611355430425, "dur": 1861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749611355432354, "dur": 239083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355302236, "dur": 14299, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355316598, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355316939, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355317145, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355317233, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355317336, "dur": 1026, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355318363, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749611355318523, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355318904, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355319672, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355319891, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355320117, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355320603, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355320984, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355321222, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355321441, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355322141, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355322276, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355322406, "dur": 1971, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\GenerateDocumentationWindow\\GenerateDocumentationPage.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749611355322406, "dur": 2181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355324587, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355325078, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355325361, "dur": 1305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355326668, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749611355326984, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749611355327304, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749611355327372, "dur": 949, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager@2.0.1\\Editor\\ISettingsRepository.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749611355326797, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749611355328337, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355328482, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355329355, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355329736, "dur": 95395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355425145, "dur": 1604, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749611355427927, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Rewrite.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749611355428627, "dur": 606, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749611355429341, "dur": 423, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749611355425142, "dur": 4895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749611355431079, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749611355431334, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749611355431503, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749611355430088, "dur": 1581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749611355431669, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355431804, "dur": 880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749611355432711, "dur": 238756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749611355676276, "dur": 2800, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26084, "tid": 424, "ts": 1749611355691621, "dur": 1421, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26084, "tid": 424, "ts": 1749611355693086, "dur": 2644, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26084, "tid": 424, "ts": 1749611355687329, "dur": 9009, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}