{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26084, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26084, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26084, "tid": 673, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26084, "tid": 673, "ts": **********850829, "dur": 490, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26084, "tid": 673, "ts": **********854460, "dur": 819, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26084, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26084, "tid": 1, "ts": **********461795, "dur": 3413, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": **********465211, "dur": 16895, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": **********482113, "dur": 20293, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26084, "tid": 673, "ts": **********855283, "dur": 15, "ph": "X", "name": "", "args": {}}, {"pid": 26084, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26084, "tid": 12884901888, "ts": **********460636, "dur": 35043, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********495682, "dur": 348104, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********496479, "dur": 1858, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********498342, "dur": 893, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499236, "dur": 172, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499416, "dur": 8, "ph": "X", "name": "ProcessMessages 20522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499425, "dur": 24, "ph": "X", "name": "ReadAsync 20522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499455, "dur": 2, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499459, "dur": 18, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499479, "dur": 34, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499515, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499526, "dur": 8, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499535, "dur": 9, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499546, "dur": 10, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499557, "dur": 25, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499583, "dur": 9, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499594, "dur": 10, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499606, "dur": 10, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499618, "dur": 11, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499630, "dur": 9, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499641, "dur": 36, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499679, "dur": 10, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499690, "dur": 23, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499715, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499725, "dur": 9, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499736, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499747, "dur": 10, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499758, "dur": 10, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499770, "dur": 10, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499781, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499782, "dur": 10, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499793, "dur": 10, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499805, "dur": 14, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499825, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499844, "dur": 9, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499855, "dur": 9, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499866, "dur": 9, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499877, "dur": 9, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499888, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499912, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499923, "dur": 9, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499934, "dur": 24, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********499964, "dur": 38, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500005, "dur": 7, "ph": "X", "name": "ReadAsync 1376", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500015, "dur": 11, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500027, "dur": 9, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500038, "dur": 15, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500054, "dur": 9, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500065, "dur": 10, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500076, "dur": 9, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500087, "dur": 7, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500096, "dur": 20, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500118, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500128, "dur": 8, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500138, "dur": 9, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500149, "dur": 9, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500160, "dur": 12, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500173, "dur": 9, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500184, "dur": 9, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500195, "dur": 36, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500233, "dur": 9, "ph": "X", "name": "ReadAsync 1094", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500244, "dur": 10, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500254, "dur": 2, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500257, "dur": 11, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500270, "dur": 7, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500278, "dur": 10, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500290, "dur": 15, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500307, "dur": 7, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500316, "dur": 16, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500333, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500366, "dur": 12, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500379, "dur": 10, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500391, "dur": 8, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500401, "dur": 14, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500418, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500435, "dur": 10, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500447, "dur": 10, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500458, "dur": 12, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500472, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500489, "dur": 10, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500500, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500521, "dur": 38, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500560, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500572, "dur": 10, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500584, "dur": 12, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500599, "dur": 14, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500614, "dur": 11, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500627, "dur": 9, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500637, "dur": 12, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500650, "dur": 25, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500677, "dur": 10, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500689, "dur": 10, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500701, "dur": 10, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500712, "dur": 10, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500724, "dur": 32, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500758, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500783, "dur": 17, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500803, "dur": 12, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500816, "dur": 10, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500827, "dur": 10, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500839, "dur": 39, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500880, "dur": 11, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500893, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500909, "dur": 15, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500925, "dur": 8, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500935, "dur": 31, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500968, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500981, "dur": 11, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********500994, "dur": 10, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501006, "dur": 38, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501046, "dur": 8, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501055, "dur": 12, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501069, "dur": 10, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501081, "dur": 10, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501093, "dur": 10, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501104, "dur": 10, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501116, "dur": 13, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501130, "dur": 9, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501141, "dur": 12, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501155, "dur": 8, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501164, "dur": 56, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501222, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501234, "dur": 10, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501245, "dur": 11, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501257, "dur": 10, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501269, "dur": 10, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501281, "dur": 10, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501292, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501309, "dur": 13, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501325, "dur": 12, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501339, "dur": 14, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501354, "dur": 13, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501368, "dur": 11, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501381, "dur": 8, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501390, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501416, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501431, "dur": 11, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501443, "dur": 16, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501461, "dur": 10, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501473, "dur": 10, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501485, "dur": 10, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501497, "dur": 11, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501509, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501563, "dur": 21, "ph": "X", "name": "ReadAsync 1577", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501586, "dur": 158, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501745, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501757, "dur": 10, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501769, "dur": 10, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501780, "dur": 11, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501793, "dur": 34, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501829, "dur": 11, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501841, "dur": 10, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501853, "dur": 10, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501864, "dur": 10, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501875, "dur": 10, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501887, "dur": 11, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501899, "dur": 61, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501964, "dur": 32, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********501999, "dur": 1, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********502002, "dur": 64, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********502069, "dur": 5, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********502075, "dur": 710, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********502788, "dur": 1, "ph": "X", "name": "ProcessMessages 1581", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********502790, "dur": 116, "ph": "X", "name": "ReadAsync 1581", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********502908, "dur": 5, "ph": "X", "name": "ProcessMessages 13430", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********502914, "dur": 19, "ph": "X", "name": "ReadAsync 13430", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********502935, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********502939, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********502971, "dur": 45, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503018, "dur": 4, "ph": "X", "name": "ProcessMessages 1140", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503023, "dur": 22, "ph": "X", "name": "ReadAsync 1140", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503047, "dur": 1, "ph": "X", "name": "ProcessMessages 1010", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503050, "dur": 34, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503085, "dur": 4, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503090, "dur": 16, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503109, "dur": 2, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503112, "dur": 14, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503128, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503132, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503149, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503153, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503186, "dur": 3, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503190, "dur": 19, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503211, "dur": 3, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503215, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503235, "dur": 3, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503239, "dur": 15, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503256, "dur": 3, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503259, "dur": 19, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503280, "dur": 3, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503284, "dur": 18, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503305, "dur": 3, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503309, "dur": 16, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503331, "dur": 16, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503349, "dur": 2, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503352, "dur": 15, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503370, "dur": 2, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503373, "dur": 19, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503394, "dur": 3, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503398, "dur": 17, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503417, "dur": 2, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503421, "dur": 22, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503444, "dur": 3, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503448, "dur": 20, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503469, "dur": 3, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503473, "dur": 17, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503492, "dur": 3, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503496, "dur": 17, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503515, "dur": 3, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503519, "dur": 18, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503538, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503542, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503561, "dur": 3, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503565, "dur": 19, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503585, "dur": 3, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503589, "dur": 18, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503609, "dur": 3, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503613, "dur": 16, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503630, "dur": 3, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503635, "dur": 19, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503656, "dur": 3, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503660, "dur": 22, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503685, "dur": 3, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503689, "dur": 16, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503706, "dur": 3, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503710, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503733, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503737, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503756, "dur": 3, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503760, "dur": 19, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503780, "dur": 3, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503784, "dur": 19, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503805, "dur": 3, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503809, "dur": 18, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503829, "dur": 2, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503832, "dur": 18, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503856, "dur": 19, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503877, "dur": 3, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503880, "dur": 15, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503898, "dur": 2, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503901, "dur": 13, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503916, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503919, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503939, "dur": 3, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503942, "dur": 19, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503964, "dur": 3, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503967, "dur": 20, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503989, "dur": 3, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********503993, "dur": 19, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504014, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504041, "dur": 22, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504065, "dur": 1, "ph": "X", "name": "ProcessMessages 1671", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504066, "dur": 16, "ph": "X", "name": "ReadAsync 1671", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504084, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504099, "dur": 10, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504110, "dur": 11, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504123, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504147, "dur": 9, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504158, "dur": 8, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504167, "dur": 12, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504181, "dur": 9, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504192, "dur": 9, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504203, "dur": 9, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504214, "dur": 9, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504224, "dur": 27, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504255, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504289, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504290, "dur": 27, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504323, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504346, "dur": 3, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504350, "dur": 16, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504370, "dur": 17, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504393, "dur": 15, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504410, "dur": 10, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504421, "dur": 9, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504432, "dur": 9, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504442, "dur": 10, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504453, "dur": 9, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504464, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504488, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504500, "dur": 7, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504508, "dur": 9, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504519, "dur": 10, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504531, "dur": 9, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504541, "dur": 9, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504553, "dur": 9, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504564, "dur": 9, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504575, "dur": 10, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504587, "dur": 8, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504596, "dur": 10, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504608, "dur": 9, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504618, "dur": 10, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504630, "dur": 12, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504643, "dur": 10, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504654, "dur": 9, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504665, "dur": 56, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504723, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504735, "dur": 9, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504746, "dur": 11, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504759, "dur": 11, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504771, "dur": 79, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504852, "dur": 47, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504901, "dur": 3, "ph": "X", "name": "ProcessMessages 2786", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504907, "dur": 24, "ph": "X", "name": "ReadAsync 2786", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504933, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********504935, "dur": 64, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505003, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505035, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505036, "dur": 24, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505063, "dur": 24, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505090, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505092, "dur": 26, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505121, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505123, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505157, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505159, "dur": 30, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505191, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505193, "dur": 52, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505249, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505288, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505290, "dur": 17, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505308, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505310, "dur": 64, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505377, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505380, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505407, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505409, "dur": 65, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505481, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505504, "dur": 42, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505552, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505591, "dur": 1, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505593, "dur": 24, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505621, "dur": 17, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505643, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505664, "dur": 3, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505667, "dur": 18, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505691, "dur": 26, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505722, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505741, "dur": 3, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505745, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505765, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505767, "dur": 29, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505799, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505802, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505830, "dur": 18, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505850, "dur": 3, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505854, "dur": 87, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505944, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505946, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505974, "dur": 13, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505987, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********505990, "dur": 41, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506033, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506047, "dur": 10, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506058, "dur": 11, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506071, "dur": 10, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506083, "dur": 33, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506117, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506131, "dur": 12, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506145, "dur": 10, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506157, "dur": 9, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506167, "dur": 34, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506203, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506216, "dur": 10, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506228, "dur": 12, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506242, "dur": 9, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506253, "dur": 101, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506356, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506369, "dur": 13, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506385, "dur": 12, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506398, "dur": 9, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506409, "dur": 31, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506442, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506457, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506476, "dur": 9, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506487, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506524, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506537, "dur": 10, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506549, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506575, "dur": 29, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506606, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506619, "dur": 10, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506631, "dur": 10, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506642, "dur": 6, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506648, "dur": 47, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506697, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506710, "dur": 33, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506745, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506757, "dur": 10, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506769, "dur": 22, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506792, "dur": 30, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506824, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506840, "dur": 16, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506857, "dur": 10, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506871, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506911, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506933, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506938, "dur": 19, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506959, "dur": 3, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506963, "dur": 19, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********506990, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507015, "dur": 9, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507026, "dur": 10, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507037, "dur": 91, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507130, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507143, "dur": 11, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507156, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507180, "dur": 36, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507218, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507255, "dur": 8, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507264, "dur": 37, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507303, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507316, "dur": 9, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507328, "dur": 9, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507339, "dur": 9, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507351, "dur": 35, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507388, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507399, "dur": 9, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507409, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507411, "dur": 10, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507423, "dur": 10, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507435, "dur": 61, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507498, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507509, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507511, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507537, "dur": 8, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507547, "dur": 30, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507579, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507592, "dur": 10, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507603, "dur": 10, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507615, "dur": 10, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507627, "dur": 9, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507638, "dur": 10, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507649, "dur": 34, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507685, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507705, "dur": 10, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507718, "dur": 10, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507730, "dur": 10, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507741, "dur": 9, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507752, "dur": 20, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507774, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507785, "dur": 10, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507796, "dur": 9, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507807, "dur": 10, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507818, "dur": 8, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507828, "dur": 31, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507861, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507874, "dur": 17, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507893, "dur": 12, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507906, "dur": 33, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507941, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507952, "dur": 9, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507963, "dur": 10, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507975, "dur": 9, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********507986, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508024, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508039, "dur": 10, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508051, "dur": 16, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508069, "dur": 56, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508126, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508142, "dur": 23, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508166, "dur": 8, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508175, "dur": 31, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508208, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508221, "dur": 9, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508232, "dur": 11, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508244, "dur": 8, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508254, "dur": 33, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508288, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508301, "dur": 14, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508316, "dur": 12, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508330, "dur": 7, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508339, "dur": 29, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508370, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508384, "dur": 9, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508394, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508417, "dur": 53, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508472, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508486, "dur": 9, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508497, "dur": 9, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508508, "dur": 9, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508519, "dur": 34, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508555, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508574, "dur": 10, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508585, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508586, "dur": 11, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508599, "dur": 33, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508633, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508645, "dur": 33, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508681, "dur": 22, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508704, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508727, "dur": 10, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508738, "dur": 9, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508749, "dur": 32, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508783, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508794, "dur": 19, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508815, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508826, "dur": 10, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508837, "dur": 9, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508848, "dur": 35, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508884, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508895, "dur": 9, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508905, "dur": 10, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508918, "dur": 10, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508929, "dur": 9, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508940, "dur": 10, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508952, "dur": 10, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508963, "dur": 9, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508974, "dur": 13, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508990, "dur": 3, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********508994, "dur": 9, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509004, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509035, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509045, "dur": 19, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509066, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509077, "dur": 9, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509088, "dur": 10, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509099, "dur": 9, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509109, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509111, "dur": 57, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509169, "dur": 1, "ph": "X", "name": "ProcessMessages 1460", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509171, "dur": 22, "ph": "X", "name": "ReadAsync 1460", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509194, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509207, "dur": 10, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509218, "dur": 10, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509230, "dur": 10, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509241, "dur": 30, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509273, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509284, "dur": 19, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509306, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509318, "dur": 23, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509342, "dur": 24, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509368, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509379, "dur": 9, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509390, "dur": 10, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509402, "dur": 9, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509413, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509447, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509458, "dur": 9, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509469, "dur": 22, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509493, "dur": 28, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509522, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509533, "dur": 10, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509544, "dur": 9, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509556, "dur": 9, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509566, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509600, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509611, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509633, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509646, "dur": 9, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509656, "dur": 9, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509667, "dur": 32, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509701, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509716, "dur": 11, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509729, "dur": 10, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509741, "dur": 8, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509750, "dur": 30, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509782, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509795, "dur": 9, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509806, "dur": 9, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509817, "dur": 9, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509828, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509861, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509872, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509875, "dur": 10, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509887, "dur": 9, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509898, "dur": 12, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509913, "dur": 26, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509940, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509951, "dur": 20, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509972, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509985, "dur": 9, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********509995, "dur": 10, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510007, "dur": 10, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510019, "dur": 10, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510030, "dur": 10, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510042, "dur": 10, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510054, "dur": 10, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510065, "dur": 8, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510075, "dur": 8, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510085, "dur": 30, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510116, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510129, "dur": 9, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510140, "dur": 9, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510150, "dur": 10, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510161, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510163, "dur": 26, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510191, "dur": 12, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510204, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510216, "dur": 11, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510229, "dur": 9, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510239, "dur": 9, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510250, "dur": 8, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510259, "dur": 30, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510290, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510301, "dur": 88, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510392, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510417, "dur": 197, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510616, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510631, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510633, "dur": 26, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510661, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510669, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510682, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510692, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510701, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510717, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510731, "dur": 8, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510742, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510751, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510759, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510779, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510788, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510797, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510806, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510815, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510825, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510834, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510842, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510851, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510860, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510861, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510871, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510902, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510915, "dur": 10, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510927, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510941, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510960, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********510979, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511018, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511027, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511039, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511053, "dur": 7, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511062, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511072, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511081, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511089, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511098, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511109, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511120, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511129, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511139, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511150, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511164, "dur": 9, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511175, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511187, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511196, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511206, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511216, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511225, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511235, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511246, "dur": 7, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511254, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511263, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511264, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511274, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511283, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511292, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511302, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511313, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511323, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511338, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511348, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511360, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511372, "dur": 7, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511381, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511391, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511401, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511410, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511420, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511428, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511441, "dur": 8, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511451, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511460, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511471, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511481, "dur": 7, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511491, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511500, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511509, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511518, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511529, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511537, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511546, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511556, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511566, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511577, "dur": 8, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511586, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511587, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511597, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511608, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511618, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511634, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511643, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511656, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511665, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511674, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511683, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511693, "dur": 7, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511703, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511714, "dur": 8, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511723, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511732, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511741, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511752, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511762, "dur": 9, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511773, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511788, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511797, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511807, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511819, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511830, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511842, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511852, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511862, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511872, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511881, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511891, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511900, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511910, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511920, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511921, "dur": 7, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511931, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511940, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511950, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511958, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511968, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511976, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511986, "dur": 10, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********511999, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512012, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512023, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512033, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512042, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512053, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512062, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512073, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512083, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512091, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512104, "dur": 8, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512114, "dur": 7, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512123, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512135, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512145, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512154, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512164, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512174, "dur": 10, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512185, "dur": 8, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512195, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512208, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512257, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512269, "dur": 9, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512279, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512281, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512290, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512292, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512303, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512313, "dur": 8, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512323, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512333, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512343, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512352, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512365, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512386, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512396, "dur": 7, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512404, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512418, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512428, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512437, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512448, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512459, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512470, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512479, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512494, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512505, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512515, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512524, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512545, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512567, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512582, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512592, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512614, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512623, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512634, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512650, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512660, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512852, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********512862, "dur": 1666, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********514531, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********514533, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********514549, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********514559, "dur": 4014, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********518580, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********518584, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********518601, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********518613, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********518615, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********518728, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********518738, "dur": 2083, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********520824, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********520826, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********520855, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********520875, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********520900, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********520902, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********520961, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********520979, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521201, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521223, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521305, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521314, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521342, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521353, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521396, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521411, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521421, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521433, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521442, "dur": 199, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521643, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521657, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521667, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521682, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521693, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521708, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521740, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521748, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521825, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521834, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521893, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521905, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********521913, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522073, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522081, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522090, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522193, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522202, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522227, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522238, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522247, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522267, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522276, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522293, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522306, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522469, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522477, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522506, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522515, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522584, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522595, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522605, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522621, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522630, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522639, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522648, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522663, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522671, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522693, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522703, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522713, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522721, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522742, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522751, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522760, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522792, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522803, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522812, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522822, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522896, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522905, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522949, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522960, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522975, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********522985, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523010, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523019, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523027, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523041, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523049, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523068, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523077, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523085, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523115, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523125, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523133, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523142, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523152, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523170, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523185, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523424, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523435, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523448, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523460, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523470, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523479, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523487, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523497, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523509, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523536, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523539, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523575, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523577, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523606, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523630, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523650, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523661, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523776, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523785, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523796, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523805, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523824, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523833, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523876, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523885, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********523893, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524048, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524057, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524066, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524090, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524113, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524227, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524241, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524271, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524292, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524294, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524313, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524324, "dur": 8, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524334, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524348, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524356, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524447, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524469, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524492, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524512, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524530, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524648, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524667, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524746, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524767, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524828, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524847, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524866, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524887, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524907, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524932, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524954, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********524981, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525003, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525027, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525047, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525068, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525091, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525113, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525133, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525155, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525180, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525204, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525223, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525244, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525347, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525361, "dur": 457, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525820, "dur": 27, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525849, "dur": 20, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525873, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525899, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525919, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********525954, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526025, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526049, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526069, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526121, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526141, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526161, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526216, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526239, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526242, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526376, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526379, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526399, "dur": 446, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526849, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526870, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********526876, "dur": 88184, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********615072, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********615076, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********615116, "dur": 1798, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********616919, "dur": 1355, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********618279, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********618281, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********618295, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********618413, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********618428, "dur": 232, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********618663, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********618664, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********618684, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********618704, "dur": 336, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********619042, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********619068, "dur": 133, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********619205, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********619216, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********619280, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********619289, "dur": 1162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620453, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620466, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620476, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620510, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620519, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620541, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620549, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620583, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620592, "dur": 249, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620843, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620853, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620952, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********620962, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********621011, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********621020, "dur": 262, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********621284, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********621296, "dur": 624, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********621924, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********621935, "dur": 446, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622385, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622386, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622399, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622418, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622427, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622458, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622467, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622628, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622638, "dur": 258, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622898, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622907, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622981, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********622989, "dur": 349, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********623341, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********623351, "dur": 232, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********623585, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********623591, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********623646, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********623655, "dur": 464, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624121, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624131, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624264, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624274, "dur": 542, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624820, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624822, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624833, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624841, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624902, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624910, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624936, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624945, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********624955, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625016, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625024, "dur": 193, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625222, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625224, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625240, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625253, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625263, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625282, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625303, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625320, "dur": 24, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625345, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625358, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625367, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625376, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625385, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625397, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625419, "dur": 8, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625429, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625439, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625447, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625458, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625467, "dur": 10, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625478, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625487, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625496, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625508, "dur": 7, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625518, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625527, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625548, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625558, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625570, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625579, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625592, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625600, "dur": 5, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625606, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625617, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625629, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625641, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625649, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625657, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625669, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625679, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625736, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625748, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625757, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625775, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625785, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625794, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625807, "dur": 176, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625986, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********625998, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626020, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626028, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626041, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626050, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626058, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626092, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626103, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626183, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626194, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626354, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626365, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626373, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626387, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626397, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626424, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626434, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626442, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626487, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626496, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626519, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626528, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626539, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626550, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626654, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626670, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626681, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********626696, "dur": 354, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********627055, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********627078, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********627080, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********627123, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********627150, "dur": 184, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********627339, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********627399, "dur": 42136, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********669557, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********669567, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********669608, "dur": 4261, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********673874, "dur": 32223, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********706116, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********706123, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********706148, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********706154, "dur": 93542, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********799710, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********799713, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********799731, "dur": 21, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********799753, "dur": 4008, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********803769, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********803773, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********803792, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********803794, "dur": 1044, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********804843, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********804846, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********804880, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********804909, "dur": 29981, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********834903, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********834907, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********834925, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********834928, "dur": 478, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********835412, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********835420, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********835448, "dur": 40, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********835488, "dur": 557, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********836049, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********836076, "dur": 435, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": **********836514, "dur": 7121, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26084, "tid": 673, "ts": **********855300, "dur": 2041, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26084, "tid": 8589934592, "ts": **********458838, "dur": 44203, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": **********503043, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": **********503047, "dur": 676, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26084, "tid": 673, "ts": **********857345, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26084, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26084, "tid": 4294967296, "ts": **********412081, "dur": 432994, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": **********414461, "dur": 37986, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": **********845099, "dur": 3702, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": **********847482, "dur": 138, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": **********848855, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26084, "tid": 673, "ts": **********857353, "dur": 28, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": **********492618, "dur": 1864, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********494493, "dur": 548, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********495155, "dur": 54, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": **********495210, "dur": 391, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********496395, "dur": 790, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_3AF52CA4203FDAC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********497922, "dur": 895, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********501503, "dur": 193, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********502229, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": **********495613, "dur": 14099, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********509722, "dur": 325144, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********834868, "dur": 190, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********835058, "dur": 149, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********835386, "dur": 56, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********835457, "dur": 934, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": **********495672, "dur": 14063, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********509749, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********509855, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": **********509821, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4740E213CDF2FB27.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********509929, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********510138, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********511133, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********511790, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8336928665352126947.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********512589, "dur": 1445, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 1, "ts": **********511929, "dur": 2166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********514095, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********514218, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********514352, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********514548, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********514692, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********514962, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Control\\LoopUnit.cs"}}, {"pid": 12345, "tid": 1, "ts": **********514935, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********515834, "dur": 1311, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugins\\InitializeAfterPluginsAttribute.cs"}}, {"pid": 12345, "tid": 1, "ts": **********515718, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********517396, "dur": 539, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\Avatar\\AvatarImages.cs"}}, {"pid": 12345, "tid": 1, "ts": **********518046, "dur": 852, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Tool\\LaunchTool.cs"}}, {"pid": 12345, "tid": 1, "ts": **********517267, "dur": 1852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********519120, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********519882, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********520268, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********520646, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********520837, "dur": 612, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": **********520830, "dur": 1113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********521943, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********522056, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********522539, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********522595, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********522903, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********523077, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********523199, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********523307, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********523663, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********523728, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********523864, "dur": 92200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********617367, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Windows.dll"}}, {"pid": 12345, "tid": 1, "ts": **********616076, "dur": 1967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********618044, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********618630, "dur": 1340, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 1, "ts": **********620051, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": **********620378, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HostFiltering.dll"}}, {"pid": 12345, "tid": 1, "ts": **********618108, "dur": 3020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********621128, "dur": 663, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********621800, "dur": 3302, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": **********625180, "dur": 629, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": **********625860, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": **********621795, "dur": 4833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********626682, "dur": 208185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********495671, "dur": 14059, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********509732, "dur": 7741, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********517541, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\ExternalLink.cs"}}, {"pid": 12345, "tid": 2, "ts": **********517473, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********518381, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********519516, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********520263, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********520649, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********520871, "dur": 582, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********520848, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********521837, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********521924, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********522117, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********522428, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 2, "ts": **********522040, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********522809, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********522959, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********523075, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********523238, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********523646, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********523742, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********523871, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********524057, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********524315, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********524497, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********524589, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********525014, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********525077, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********525335, "dur": 93300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********618653, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********619880, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.Xml.Linq.dll"}}, {"pid": 12345, "tid": 2, "ts": **********620265, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********620436, "dur": 1885, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********622322, "dur": 279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********622602, "dur": 2322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********624940, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********625372, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 2, "ts": **********618636, "dur": 7068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********625705, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********625804, "dur": 209057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********495747, "dur": 14007, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********509800, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": **********509757, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_094A556A01D249DF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********510142, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********510323, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 3, "ts": **********510322, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_8F98D4AB8445D873.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********510457, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_8F98D4AB8445D873.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********510834, "dur": 1109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********511943, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": **********512657, "dur": 1412, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.VisualBasic.Core.dll"}}, {"pid": 12345, "tid": 3, "ts": **********514077, "dur": 2782, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.JSInterop.dll"}}, {"pid": 12345, "tid": 3, "ts": **********511995, "dur": 5510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********517505, "dur": 1770, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetMenu\\AssetVcsOperations.cs"}}, {"pid": 12345, "tid": 3, "ts": **********517505, "dur": 2425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********519931, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********520262, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********520652, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********520788, "dur": 712, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********521504, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********521939, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********522024, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********522237, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********522386, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********522950, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********523033, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********523104, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********523683, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********523796, "dur": 92273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********616071, "dur": 1927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********617999, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********618712, "dur": 3110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 3, "ts": **********621847, "dur": 3690, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 3, "ts": **********618461, "dur": 8028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********626541, "dur": 208334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********495705, "dur": 14036, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********509832, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": **********509808, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B87B178D29F0F556.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********509903, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********510103, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********510333, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 4, "ts": **********510332, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_743DCD7F83EC4AAC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********510469, "dur": 258, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 4, "ts": **********510467, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********510730, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********510844, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********511168, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********511671, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********511785, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********511938, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********512794, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********513049, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********513861, "dur": 844, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics.Editor\\QuaternionDrawer.cs"}}, {"pid": 12345, "tid": 4, "ts": **********514706, "dur": 1142, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics.Editor\\PrimitiveVectorDrawer.cs"}}, {"pid": 12345, "tid": 4, "ts": **********513289, "dur": 2632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********515921, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********516144, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********516535, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********516863, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********517079, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********517585, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********518697, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********519717, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********520252, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********520651, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********520792, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********521491, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 4, "ts": **********521319, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********521954, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********522060, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********522237, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********522690, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********522952, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********523083, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********523689, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********523797, "dur": 92269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********616068, "dur": 1907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********617976, "dur": 743, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********618719, "dur": 1246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********619969, "dur": 415, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.IK.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": **********620435, "dur": 1337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 4, "ts": **********621843, "dur": 2760, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 4, "ts": **********619968, "dur": 6026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********625995, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********626109, "dur": 208782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********495730, "dur": 14017, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********509802, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": **********509750, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********509876, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********510026, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********510334, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 5, "ts": **********510333, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_66549C0209C53F09.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********510893, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********511225, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********511514, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********511889, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********512592, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********513044, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********513272, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********513805, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********514041, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********514249, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********514487, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********514956, "dur": 557, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\PointerEventUnit.cs"}}, {"pid": 12345, "tid": 5, "ts": **********514932, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********515835, "dur": 948, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Product\\LudiqProduct.cs"}}, {"pid": 12345, "tid": 5, "ts": **********515708, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********516900, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********517687, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetsUtils\\Processor\\AssetPostprocessor.cs"}}, {"pid": 12345, "tid": 5, "ts": **********517504, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********519094, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********519887, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********520259, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********520647, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********520792, "dur": 1334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********522129, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********522624, "dur": 1101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********523746, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********523813, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********523866, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********524120, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********524451, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********524522, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********524764, "dur": 91311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********616078, "dur": 1899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********617978, "dur": 849, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********618828, "dur": 1044, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********620265, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 5, "ts": **********619875, "dur": 1849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********621725, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********622593, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 5, "ts": **********622046, "dur": 1834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********623880, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********625059, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********625214, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********625956, "dur": 208954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********495779, "dur": 13981, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********509812, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": **********509763, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_699CA9E1D209A267.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********509869, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********510022, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********510342, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": **********510326, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_B663C5A7D98A973E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********510697, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********510970, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********511255, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********511867, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********512240, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********513046, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********513285, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********514017, "dur": 720, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1.1.8\\Editor\\Aseprite\\Chunks\\CellChunk.cs"}}, {"pid": 12345, "tid": 6, "ts": **********514760, "dur": 1444, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1.1.8\\Editor\\Aseprite\\AsepriteUtilities.cs"}}, {"pid": 12345, "tid": 6, "ts": **********513716, "dur": 2612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********516328, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********516555, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********516881, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********517107, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********518172, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********518743, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********519740, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********520265, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********520647, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********521350, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********521611, "dur": 1300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********522911, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********522991, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********523060, "dur": 1097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********524158, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********524263, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********524388, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********524643, "dur": 93362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********618711, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Authorization.dll"}}, {"pid": 12345, "tid": 6, "ts": **********619689, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": **********620050, "dur": 1775, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 6, "ts": **********618005, "dur": 3990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********622593, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 6, "ts": **********622049, "dur": 1825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********623875, "dur": 1273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********625213, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********625866, "dur": 208992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********495806, "dur": 13960, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********509815, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": **********509769, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21B473704285E949.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********509974, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********510337, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 7, "ts": **********510336, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_6C566B45F0C804AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********511874, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********512790, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********513025, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********513245, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********513605, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********514420, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********514651, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********514960, "dur": 557, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Lifecycle\\LateUpdate.cs"}}, {"pid": 12345, "tid": 7, "ts": **********514901, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********515679, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********515933, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********516157, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********516553, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********516920, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********518009, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********518456, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********518718, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********519283, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********519354, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********520264, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********520655, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********520872, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": **********521308, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 7, "ts": **********521491, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 7, "ts": **********520834, "dur": 1258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********522093, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********522223, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********522392, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********522847, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********522929, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********523077, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********523217, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********523667, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********523804, "dur": 94208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********619881, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 7, "ts": **********618013, "dur": 2090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********620103, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********620378, "dur": 1442, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 7, "ts": **********622313, "dur": 1602, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": **********624485, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 7, "ts": **********620260, "dur": 4837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********625098, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********625204, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********625865, "dur": 209036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********495836, "dur": 13936, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********509828, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": **********509775, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********509963, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********510188, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********510321, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 8, "ts": **********510320, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_038A17E6D6B77576.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********510582, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********511132, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********511259, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********511821, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********511879, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********512766, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********513028, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********513269, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********514076, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********514300, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********514674, "dur": 1182, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Path\\Editor\\EditorTool\\ScriptablePathInspector.cs"}}, {"pid": 12345, "tid": 8, "ts": **********514526, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********516084, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********516349, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********516565, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********516882, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********517294, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********518089, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int4x3.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": **********518052, "dur": 1882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********519934, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********520256, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********520654, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********520873, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 8, "ts": **********520852, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********521337, "dur": 738, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********522135, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********522438, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********522993, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********523085, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********523289, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********524484, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********524549, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********524999, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********525352, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********525553, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********525622, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********525788, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********526280, "dur": 142664, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********670053, "dur": 32268, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": **********670052, "dur": 33511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********705235, "dur": 184, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********705477, "dur": 93595, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********803018, "dur": 31208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": **********803016, "dur": 31213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": **********834249, "dur": 562, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": **********495859, "dur": 13920, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********509831, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": **********509782, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0998A984E55471CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********510019, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********510104, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********510351, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": **********510350, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_425C305CB5B8A325.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********510713, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********510914, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********511356, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********511881, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********512611, "dur": 1038, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\VisualStudioIntegration.cs"}}, {"pid": 12345, "tid": 9, "ts": **********514139, "dur": 1381, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Symbols.cs"}}, {"pid": 12345, "tid": 9, "ts": **********512610, "dur": 2979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********515590, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********515836, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********516056, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********516281, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********516498, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********516809, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********517099, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********518184, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float4x2.gen.cs"}}, {"pid": 12345, "tid": 9, "ts": **********518138, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********519918, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********520262, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********520815, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********521302, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 9, "ts": **********521723, "dur": 400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": **********521156, "dur": 1102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********522258, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********522532, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********522685, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********522924, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********523426, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********523498, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********523648, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********523749, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********523912, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********524230, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********524341, "dur": 93666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********618008, "dur": 1805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********619951, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 9, "ts": **********620265, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 9, "ts": **********621801, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": **********622594, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 9, "ts": **********619892, "dur": 2806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********622698, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********624464, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 9, "ts": **********622756, "dur": 1820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********625073, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********625220, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********625958, "dur": 208945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********495893, "dur": 13892, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********509840, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": **********509788, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5AA67F5B53C44BAD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********510022, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********510109, "dur": 609, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********510855, "dur": 1109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********511997, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********512876, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-memory-l1-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": **********512494, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********513466, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********514294, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********514953, "dur": 556, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Path\\Editor\\IMGUI\\GUIFramework\\GUIState.cs"}}, {"pid": 12345, "tid": 10, "ts": **********514519, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********515570, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********515822, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********516038, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********516271, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********516499, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********516808, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********517020, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********517820, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********518764, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********519473, "dur": 781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********520255, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********520848, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********521036, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********521440, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 10, "ts": **********521683, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": **********521322, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********521842, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********522020, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********522160, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********522476, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********523100, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********523651, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********523788, "dur": 92274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********616064, "dur": 1674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********617739, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********618653, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.DependencyInjection.Abstractions.dll"}}, {"pid": 12345, "tid": 10, "ts": **********617827, "dur": 1556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********619383, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********620378, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 10, "ts": **********621760, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 10, "ts": **********619958, "dur": 1993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********621951, "dur": 1105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********624457, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 10, "ts": **********623065, "dur": 1681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********624747, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********624912, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********625459, "dur": 1212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********626700, "dur": 208171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********495937, "dur": 13855, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********509807, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 11, "ts": **********509794, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_006DC43AA5DD858A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********509912, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********510224, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********510338, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": **********510337, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2A9C520E2391C187.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********510816, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********511071, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********511623, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********512033, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********512722, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********513003, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********513154, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********513303, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********513608, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********514304, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********514765, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Editor\\TextureGenerator\\TextureGeneratorHelper.cs"}}, {"pid": 12345, "tid": 11, "ts": **********514544, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********515634, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********515830, "dur": 1258, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_0_0.cs"}}, {"pid": 12345, "tid": 11, "ts": **********515781, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********517201, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********518224, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********519745, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********520268, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********520879, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********521441, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 11, "ts": **********521309, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********521888, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********522209, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********522413, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********522495, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********523088, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********523654, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********523760, "dur": 1257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********525017, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********525103, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********525177, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********525473, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********525544, "dur": 90527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********616074, "dur": 1907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********617982, "dur": 706, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********618692, "dur": 1220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********619913, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********620379, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 11, "ts": **********620001, "dur": 1316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********621318, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********622593, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 11, "ts": **********624490, "dur": 868, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 11, "ts": **********621836, "dur": 3526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********625363, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********625458, "dur": 1106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********626564, "dur": 208326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********495959, "dur": 13839, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********509834, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": **********509801, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7027E12F917228B0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********509889, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********510357, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********510536, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 12, "ts": **********510678, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********511049, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********511134, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********511262, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********511333, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********511887, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********512718, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********513006, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********513155, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********513305, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********513818, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********513958, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********514319, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********514459, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********514599, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********514742, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********514964, "dur": 884, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics\\OnControllerColliderHit.cs"}}, {"pid": 12345, "tid": 12, "ts": **********514889, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********515931, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********516174, "dur": 1020, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Runtime\\UTess2D\\Tessellator.cs"}}, {"pid": 12345, "tid": 12, "ts": **********516174, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********517646, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********518442, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Runtime\\BurstCompileAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": **********518395, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********519168, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********519714, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********520256, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********520846, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********521440, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll"}}, {"pid": 12345, "tid": 12, "ts": **********521090, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********521590, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********521699, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********522122, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********522490, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********523092, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********523654, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********523744, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********523850, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********524091, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********524275, "dur": 91798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********616076, "dur": 1905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********617981, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********618653, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": **********619880, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 12, "ts": **********618459, "dur": 1903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********620362, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********620428, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********621562, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********621884, "dur": 438, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": **********622593, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 12, "ts": **********621873, "dur": 2066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********623940, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********624257, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********624372, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********624436, "dur": 1156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********625595, "dur": 209268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********495983, "dur": 13851, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********509857, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": **********509836, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_C7A46CFE1C6B2861.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********510015, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********510125, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********510347, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 13, "ts": **********510346, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_20B214415B84035D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********510461, "dur": 408, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_20B214415B84035D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********510879, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********511157, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 13, "ts": **********511959, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 13, "ts": **********512084, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 13, "ts": **********512611, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 13, "ts": **********512828, "dur": 534, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\ILogScope.cs"}}, {"pid": 12345, "tid": 13, "ts": **********513726, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\BaseDelegator.cs"}}, {"pid": 12345, "tid": 13, "ts": **********513932, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 13, "ts": **********514053, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 13, "ts": **********514105, "dur": 1747, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 13, "ts": **********516706, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4EqualityComparer.cs"}}, {"pid": 12345, "tid": 13, "ts": **********516778, "dur": 849, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 13, "ts": **********510982, "dur": 6647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********517629, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********518029, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********518263, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": **********518438, "dur": 760, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 13, "ts": **********519293, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 13, "ts": **********519462, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 13, "ts": **********519786, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 13, "ts": **********518138, "dur": 2048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********520364, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********520643, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********520837, "dur": 613, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 13, "ts": **********521490, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 13, "ts": **********521723, "dur": 400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 13, "ts": **********520717, "dur": 1606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********522326, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********522972, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********523075, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********523211, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********523571, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********523645, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": **********524285, "dur": 129, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********524761, "dur": 89690, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": **********616056, "dur": 1298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": **********617354, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********617679, "dur": 1458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": **********619137, "dur": 1229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********621021, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-rtlsupport-l1-1-0.dll"}}, {"pid": 12345, "tid": 13, "ts": **********620370, "dur": 1973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": **********622343, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********622594, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 13, "ts": **********622399, "dur": 1798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": **********624198, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********625008, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********625094, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********625439, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********626473, "dur": 208407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********496010, "dur": 13850, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********509980, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 14, "ts": **********509979, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_88B56BCE4990CA03.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": **********510335, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********510713, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********510999, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********511177, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********511828, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********512002, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********513095, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********513344, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.spriteshape@9.0.5\\Editor\\SpriteShapeAssetPostProcessor.cs"}}, {"pid": 12345, "tid": 14, "ts": **********514081, "dur": 1770, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.spriteshape@9.0.5\\Editor\\SpriteSelector.cs"}}, {"pid": 12345, "tid": 14, "ts": **********513321, "dur": 2854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********516175, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********516397, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********516838, "dur": 575, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Collections\\INotifyCollectionChanged.cs"}}, {"pid": 12345, "tid": 14, "ts": **********516625, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********518113, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Configuration\\ClientConfiguration.cs"}}, {"pid": 12345, "tid": 14, "ts": **********517483, "dur": 1690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********519174, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********519270, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********519339, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********520258, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********520837, "dur": 661, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 14, "ts": **********521689, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 14, "ts": **********522116, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 14, "ts": **********520817, "dur": 1576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": **********522458, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********523102, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********523652, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********523749, "dur": 1192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********524941, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": **********525059, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": **********525372, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********525455, "dur": 93184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********618710, "dur": 325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 14, "ts": **********619880, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.dll"}}, {"pid": 12345, "tid": 14, "ts": **********620049, "dur": 336, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.dll"}}, {"pid": 12345, "tid": 14, "ts": **********620943, "dur": 951, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 14, "ts": **********618640, "dur": 3615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": **********622256, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********622316, "dur": 2641, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": **********625175, "dur": 448, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": **********622315, "dur": 4098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": **********626457, "dur": 208426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********496035, "dur": 13828, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********510012, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********510337, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********510509, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********510688, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********510880, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": **********511218, "dur": 628, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 15, "ts": **********512084, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 15, "ts": **********512309, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 15, "ts": **********512838, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 15, "ts": **********510991, "dur": 2865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": **********513856, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********513979, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********514417, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********514718, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********514954, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Animation\\OnAnimatorMove.cs"}}, {"pid": 12345, "tid": 15, "ts": **********514934, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********515831, "dur": 1200, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\ResourceProviders\\CreateTextureOptions.cs"}}, {"pid": 12345, "tid": 15, "ts": **********515707, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********518182, "dur": 1232, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\Tree\\GetChangesOverlayIcon.cs"}}, {"pid": 12345, "tid": 15, "ts": **********517129, "dur": 2528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********519657, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********520253, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********520842, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": **********521491, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 15, "ts": **********521724, "dur": 425, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 15, "ts": **********522328, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 15, "ts": **********521242, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": **********522477, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********522569, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": **********522950, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": **********523196, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********523477, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********523687, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********523790, "dur": 92267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********616060, "dur": 1942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": **********618002, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********618711, "dur": 1176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l1-1-0.dll"}}, {"pid": 12345, "tid": 15, "ts": **********619960, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Common.dll"}}, {"pid": 12345, "tid": 15, "ts": **********620266, "dur": 1551, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Debug.dll"}}, {"pid": 12345, "tid": 15, "ts": **********621844, "dur": 477, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordbi.dll"}}, {"pid": 12345, "tid": 15, "ts": **********622511, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 15, "ts": **********618074, "dur": 5236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": **********623311, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********624491, "dur": 734, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.CommandLine.dll"}}, {"pid": 12345, "tid": 15, "ts": **********623681, "dur": 2123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": **********625872, "dur": 209036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********496071, "dur": 13797, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********510001, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********510306, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_37A60B8D84ADA88C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": **********510389, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********510783, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": **********510876, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********511159, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********511537, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4345639507432806382.rsp"}}, {"pid": 12345, "tid": 16, "ts": **********511639, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********511776, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********511842, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********511921, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********512678, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********513113, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********514093, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDImporterAssetPostProcessor.cs"}}, {"pid": 12345, "tid": 16, "ts": **********513323, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********514961, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Graph\\HasScriptGraph.cs"}}, {"pid": 12345, "tid": 16, "ts": **********514884, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********515833, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Utilities\\NativeUtility.cs"}}, {"pid": 12345, "tid": 16, "ts": **********515658, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********516371, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********516527, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********516677, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********517166, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********518002, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********518938, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********519638, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********520251, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********520868, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": **********521305, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 16, "ts": **********521075, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": **********521558, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********521705, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********522123, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********522489, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********523094, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********523653, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********523781, "dur": 92581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********616365, "dur": 1210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": **********617576, "dur": 1038, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********618619, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": **********619855, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********620051, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 16, "ts": **********620440, "dur": 924, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 16, "ts": **********622312, "dur": 2795, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 16, "ts": **********619928, "dur": 5189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": **********625118, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********625179, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": **********625178, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": **********625505, "dur": 209355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********496096, "dur": 13778, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********510008, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********510120, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********510589, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********510706, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********510900, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********510971, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": **********511026, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********511287, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********512627, "dur": 1227, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": **********511884, "dur": 2308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********514192, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********514410, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********514634, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********514807, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********514960, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnDeselect.cs"}}, {"pid": 12345, "tid": 17, "ts": **********514933, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********515619, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********515766, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********515920, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********516060, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********516202, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********516336, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********516488, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********516633, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********516841, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********516992, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\PendingChanges\\DrawOperationSuccess.cs"}}, {"pid": 12345, "tid": 17, "ts": **********516992, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********518195, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********518996, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********519331, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********520259, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********520843, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********521129, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 17, "ts": **********521440, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 17, "ts": **********521094, "dur": 1022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": **********522165, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********522426, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********522555, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": **********522827, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********522905, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********523079, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********523657, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********523748, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********523933, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": **********524288, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********524468, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********524582, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": **********524939, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********525021, "dur": 93564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********618654, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 17, "ts": **********618586, "dur": 1449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": **********620035, "dur": 659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********621800, "dur": 523, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Trigger\\Unity.ILPP.Trigger.exe"}}, {"pid": 12345, "tid": 17, "ts": **********622594, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 17, "ts": **********620701, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": **********623002, "dur": 1200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": **********624203, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********624749, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Editor.dll"}}, {"pid": 12345, "tid": 17, "ts": **********624933, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********624995, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********625086, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********625467, "dur": 177551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********803021, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": **********803020, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": **********803162, "dur": 1077, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": **********804246, "dur": 30623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********496124, "dur": 13755, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********509881, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_A8E53AE16CB1305D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": **********509938, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********510047, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********510120, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********510301, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_2FD4CD6B4E1CB690.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": **********510375, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********510848, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********511217, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********511311, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********511615, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********512069, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********513043, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********513188, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********514136, "dur": 1383, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1.1.8\\Editor\\AsepriteImportData.cs"}}, {"pid": 12345, "tid": 18, "ts": **********513635, "dur": 1989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********515624, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********515792, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********515948, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********516330, "dur": 763, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\UnsafeBitArray.cs"}}, {"pid": 12345, "tid": 18, "ts": **********516114, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********517316, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********518329, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********518684, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********519126, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********519958, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********520260, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********520677, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": **********520872, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 18, "ts": **********520827, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": **********521422, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********521491, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll"}}, {"pid": 12345, "tid": 18, "ts": **********521490, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": **********521701, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********522126, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********522483, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********523096, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********523652, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********523782, "dur": 92278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********616063, "dur": 1911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": **********617975, "dur": 3357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********621761, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Forms.dll"}}, {"pid": 12345, "tid": 18, "ts": **********622312, "dur": 440, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 18, "ts": **********621345, "dur": 1691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": **********623070, "dur": 1129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": **********624231, "dur": 1166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********625415, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********626139, "dur": 208746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********496144, "dur": 13748, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********509910, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********510028, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********510353, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********510486, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 19, "ts": **********510483, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": **********510658, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********510732, "dur": 1046, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********511831, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********512066, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********513001, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********513455, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********513820, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********513972, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********514302, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********514440, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********514574, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********514715, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********514963, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\BinaryComparisonUnit.cs"}}, {"pid": 12345, "tid": 19, "ts": **********514845, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********515513, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********515763, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********515982, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********516198, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********516404, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********516654, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********517064, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********517676, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********518565, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********518678, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********519071, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********519842, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********520271, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********520650, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": **********520762, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": **********521111, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": **********521517, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********522042, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********522111, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": **********522312, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": **********522876, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********523087, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********523655, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********523745, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": **********523878, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": **********524155, "dur": 94044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********618653, "dur": 1239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-namedpipe-l1-1-0.dll"}}, {"pid": 12345, "tid": 19, "ts": **********620444, "dur": 1325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 19, "ts": **********621803, "dur": 520, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 19, "ts": **********622594, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": **********618201, "dur": 4562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": **********622763, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********624480, "dur": 768, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 19, "ts": **********623536, "dur": 2348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": **********625954, "dur": 208959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********496164, "dur": 13819, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********510018, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********510466, "dur": 385, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_3787B1E93F84ED7A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": **********510959, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********511180, "dur": 1083, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********512540, "dur": 1376, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\dbgshim.dll"}}, {"pid": 12345, "tid": 20, "ts": **********514075, "dur": 2716, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\aspnetcorev2_inprocess.dll"}}, {"pid": 12345, "tid": 20, "ts": **********517038, "dur": 662, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-multibyte-l1-1-0.dll"}}, {"pid": 12345, "tid": 20, "ts": **********517700, "dur": 1049, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-math-l1-1-0.dll"}}, {"pid": 12345, "tid": 20, "ts": **********512268, "dur": 6546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********518852, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\CommandLineTest\\RunSettings.cs"}}, {"pid": 12345, "tid": 20, "ts": **********518815, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********519869, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********520269, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********520844, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": **********521302, "dur": 821, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": **********521073, "dur": 1250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": **********522323, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********522435, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********522927, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********523078, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": **********523186, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": **********523522, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********523763, "dur": 1792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********525556, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": **********525632, "dur": 93002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********618711, "dur": 1180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 20, "ts": **********620050, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 20, "ts": **********620379, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 20, "ts": **********621135, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.Brotli.dll"}}, {"pid": 12345, "tid": 20, "ts": **********621266, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 20, "ts": **********621800, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 20, "ts": **********621884, "dur": 3687, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 20, "ts": **********618635, "dur": 7220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": **********625963, "dur": 208944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********839508, "dur": 2913, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26084, "tid": 673, "ts": **********858023, "dur": 2477, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26084, "tid": 673, "ts": **********860542, "dur": 2720, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26084, "tid": 673, "ts": **********852941, "dur": 10889, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}