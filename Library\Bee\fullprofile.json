{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26084, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26084, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26084, "tid": 497, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26084, "tid": 497, "ts": 1749615665465288, "dur": 642, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26084, "tid": 497, "ts": 1749615665468160, "dur": 612, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26084, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26084, "tid": 1, "ts": 1749615656823191, "dur": 23112, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749615656846307, "dur": 35847, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749615656882160, "dur": 38798, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26084, "tid": 497, "ts": 1749615665468774, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 26084, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656821913, "dur": 28265, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656850180, "dur": 8607051, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656850989, "dur": 1901, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656852895, "dur": 366, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656853263, "dur": 7573, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656860854, "dur": 242, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656861100, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656861139, "dur": 390, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656861531, "dur": 30549, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892087, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892091, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892176, "dur": 425, "ph": "X", "name": "ProcessMessages 3386", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892604, "dur": 101, "ph": "X", "name": "ReadAsync 3386", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892706, "dur": 4, "ph": "X", "name": "ProcessMessages 11382", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892711, "dur": 27, "ph": "X", "name": "ReadAsync 11382", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892742, "dur": 29, "ph": "X", "name": "ReadAsync 1061", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892777, "dur": 1, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892779, "dur": 16, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892798, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892802, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892824, "dur": 3, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892828, "dur": 22, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892853, "dur": 3, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892857, "dur": 20, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892879, "dur": 3, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892884, "dur": 22, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892907, "dur": 4, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892912, "dur": 19, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892933, "dur": 4, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892938, "dur": 19, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892959, "dur": 4, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656892964, "dur": 37, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656893003, "dur": 222, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656893229, "dur": 6, "ph": "X", "name": "ProcessMessages 5997", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656893236, "dur": 703, "ph": "X", "name": "ReadAsync 5997", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656893941, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656893943, "dur": 60, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894004, "dur": 2, "ph": "X", "name": "ProcessMessages 4694", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894007, "dur": 222, "ph": "X", "name": "ReadAsync 4694", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894237, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894328, "dur": 2, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894331, "dur": 25, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894359, "dur": 78, "ph": "X", "name": "ReadAsync 1419", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894443, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894460, "dur": 90, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894553, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894585, "dur": 4, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894591, "dur": 23, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894621, "dur": 15, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894638, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894643, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894663, "dur": 146, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894811, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894816, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894848, "dur": 4, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894853, "dur": 29, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894884, "dur": 5, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894890, "dur": 16, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656894909, "dur": 146, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895056, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895075, "dur": 95, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895172, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895174, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895220, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895223, "dur": 34, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895258, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895261, "dur": 67, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895330, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895334, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895360, "dur": 120, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895483, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895516, "dur": 19, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895536, "dur": 6, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895543, "dur": 26, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895572, "dur": 15, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895590, "dur": 190, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895784, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895811, "dur": 26, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895840, "dur": 24, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895866, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895867, "dur": 24, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895894, "dur": 33, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895933, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656895953, "dur": 101, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896056, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896077, "dur": 4, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896083, "dur": 23, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896108, "dur": 10, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896120, "dur": 11, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896132, "dur": 113, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896247, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896268, "dur": 10, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896279, "dur": 14, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896294, "dur": 11, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896307, "dur": 9, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896317, "dur": 36, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896358, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896372, "dur": 99, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896474, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896498, "dur": 3, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896502, "dur": 20, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896524, "dur": 3, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896528, "dur": 23, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896552, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896555, "dur": 11, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896567, "dur": 101, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896674, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896703, "dur": 6, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896710, "dur": 23, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896735, "dur": 3, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896740, "dur": 17, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896759, "dur": 3, "ph": "X", "name": "ProcessMessages 121", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896765, "dur": 18, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896785, "dur": 3, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896790, "dur": 19, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896810, "dur": 4, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896815, "dur": 20, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896837, "dur": 3, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896841, "dur": 22, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896864, "dur": 4, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896869, "dur": 20, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896891, "dur": 4, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896895, "dur": 17, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896914, "dur": 3, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896918, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896943, "dur": 14, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896964, "dur": 18, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656896988, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897013, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897015, "dur": 13, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897029, "dur": 162, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897193, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897218, "dur": 3, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897223, "dur": 19, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897244, "dur": 3, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897249, "dur": 21, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897271, "dur": 4, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897276, "dur": 16, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897298, "dur": 19, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897323, "dur": 19, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897344, "dur": 3, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897348, "dur": 21, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897371, "dur": 4, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897375, "dur": 15, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897396, "dur": 237, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897635, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897672, "dur": 1, "ph": "X", "name": "ProcessMessages 1250", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897674, "dur": 28, "ph": "X", "name": "ReadAsync 1250", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897705, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897727, "dur": 40, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897774, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897806, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897807, "dur": 24, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897835, "dur": 27, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897865, "dur": 20, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897888, "dur": 28, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897917, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897919, "dur": 23, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897946, "dur": 31, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897978, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656897980, "dur": 24, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898006, "dur": 209, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898217, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898249, "dur": 1, "ph": "X", "name": "ProcessMessages 960", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898251, "dur": 22, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898276, "dur": 28, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898307, "dur": 23, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898336, "dur": 129, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898468, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898501, "dur": 23, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898527, "dur": 29, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898557, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898559, "dur": 21, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898583, "dur": 20, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898605, "dur": 48, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898656, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898686, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898688, "dur": 23, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898714, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898739, "dur": 28, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898771, "dur": 23, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898797, "dur": 28, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898826, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898828, "dur": 24, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898854, "dur": 135, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656898992, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899023, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899024, "dur": 28, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899055, "dur": 28, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899085, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899086, "dur": 21, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899114, "dur": 27, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899143, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899144, "dur": 24, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899171, "dur": 27, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899201, "dur": 22, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899226, "dur": 154, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899383, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899416, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899418, "dur": 23, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899444, "dur": 31, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899478, "dur": 14, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899493, "dur": 114, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899614, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899639, "dur": 4, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899645, "dur": 19, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899666, "dur": 3, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899670, "dur": 20, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899692, "dur": 4, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899697, "dur": 30, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899732, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899753, "dur": 3, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899758, "dur": 19, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899779, "dur": 3, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899784, "dur": 20, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899805, "dur": 3, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899810, "dur": 21, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899837, "dur": 20, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899859, "dur": 3, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899864, "dur": 19, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899885, "dur": 4, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899890, "dur": 16, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899912, "dur": 13, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899931, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899951, "dur": 3, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899955, "dur": 41, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656899998, "dur": 4, "ph": "X", "name": "ProcessMessages 1494", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900003, "dur": 17, "ph": "X", "name": "ReadAsync 1494", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900025, "dur": 19, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900051, "dur": 19, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900072, "dur": 3, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900076, "dur": 16, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900094, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900098, "dur": 16, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900120, "dur": 17, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900143, "dur": 24, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900170, "dur": 3, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900174, "dur": 22, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900198, "dur": 4, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900203, "dur": 10, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900215, "dur": 216, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900437, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900457, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900461, "dur": 26, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900488, "dur": 4, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900493, "dur": 17, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900516, "dur": 16, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900539, "dur": 19, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900559, "dur": 3, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900564, "dur": 22, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900588, "dur": 3, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900592, "dur": 18, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900612, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900617, "dur": 16, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900635, "dur": 3, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900639, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900660, "dur": 4, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900665, "dur": 24, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900690, "dur": 3, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900695, "dur": 14, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900715, "dur": 12, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900728, "dur": 168, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900901, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900931, "dur": 1, "ph": "X", "name": "ProcessMessages 1162", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900932, "dur": 10, "ph": "X", "name": "ReadAsync 1162", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900944, "dur": 10, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900955, "dur": 22, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656900979, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901001, "dur": 22, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901025, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901027, "dur": 33, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901062, "dur": 23, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901096, "dur": 100, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901202, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901240, "dur": 1, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901242, "dur": 40, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901284, "dur": 1, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901286, "dur": 114, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901403, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901430, "dur": 3, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901435, "dur": 14, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901451, "dur": 108, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901564, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901581, "dur": 3, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901584, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901603, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901607, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901627, "dur": 3, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901631, "dur": 11, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901643, "dur": 77, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901726, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901746, "dur": 17, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901767, "dur": 25, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901794, "dur": 3, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901798, "dur": 22, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901822, "dur": 3, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901826, "dur": 10, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656901838, "dur": 185, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902028, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902048, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902052, "dur": 22, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902075, "dur": 3, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902079, "dur": 17, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902098, "dur": 3, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902102, "dur": 16, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902120, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902123, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902145, "dur": 3, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902149, "dur": 20, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902171, "dur": 3, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902174, "dur": 11, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902187, "dur": 171, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902363, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902383, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902386, "dur": 18, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902406, "dur": 2, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902409, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902429, "dur": 3, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902433, "dur": 20, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902454, "dur": 3, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902458, "dur": 18, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902479, "dur": 3, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902483, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902502, "dur": 3, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902506, "dur": 17, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902525, "dur": 3, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902529, "dur": 12, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902543, "dur": 143, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902687, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902701, "dur": 11, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902713, "dur": 9, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902724, "dur": 12, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902738, "dur": 18, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902758, "dur": 23, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902783, "dur": 10, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902795, "dur": 9, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902861, "dur": 18, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902880, "dur": 1, "ph": "X", "name": "ProcessMessages 1671", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656902882, "dur": 208, "ph": "X", "name": "ReadAsync 1671", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903096, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903116, "dur": 18, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903136, "dur": 3, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903140, "dur": 19, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903161, "dur": 3, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903165, "dur": 10, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903177, "dur": 117, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903299, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903324, "dur": 3, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903327, "dur": 23, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903352, "dur": 2, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903355, "dur": 15, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903372, "dur": 190, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903564, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903587, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903589, "dur": 13, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903605, "dur": 10, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903616, "dur": 22, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903641, "dur": 148, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903790, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903794, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903815, "dur": 10, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903827, "dur": 23, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903852, "dur": 10, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903863, "dur": 52, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903918, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903945, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903971, "dur": 4, "ph": "X", "name": "ProcessMessages 1140", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656903976, "dur": 3226, "ph": "X", "name": "ReadAsync 1140", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656907206, "dur": 2, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656907209, "dur": 125, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656907335, "dur": 7, "ph": "X", "name": "ProcessMessages 11956", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656907342, "dur": 226, "ph": "X", "name": "ReadAsync 11956", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656907574, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656907617, "dur": 1, "ph": "X", "name": "ProcessMessages 1051", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656907619, "dur": 648, "ph": "X", "name": "ReadAsync 1051", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656908270, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656908306, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656908309, "dur": 26, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656908339, "dur": 665, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656909006, "dur": 45, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656909054, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656909151, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656909152, "dur": 506, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656909673, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656909736, "dur": 1, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656909737, "dur": 512, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656910277, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656913780, "dur": 4, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656913788, "dur": 93, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656913882, "dur": 3, "ph": "X", "name": "ProcessMessages 5846", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656913886, "dur": 164, "ph": "X", "name": "ReadAsync 5846", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656914053, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656914090, "dur": 5, "ph": "X", "name": "ProcessMessages 1129", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656914096, "dur": 56, "ph": "X", "name": "ReadAsync 1129", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656914153, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656914175, "dur": 507, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656914689, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656914717, "dur": 3, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656914722, "dur": 27, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656914750, "dur": 483, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656915235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656915237, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656915290, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656915292, "dur": 21, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656915315, "dur": 435, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656915753, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656915762, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656915810, "dur": 5, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656915817, "dur": 438, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656916258, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656916293, "dur": 11, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656916305, "dur": 563, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656916874, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656916897, "dur": 4, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656916903, "dur": 14, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656916918, "dur": 398, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656917323, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656917355, "dur": 3, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656917359, "dur": 17, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656917377, "dur": 457, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656917837, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656917865, "dur": 20, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656917886, "dur": 377, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656918269, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656918298, "dur": 15, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656918314, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656918315, "dur": 462, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656918786, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656918809, "dur": 4, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656918814, "dur": 14, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656918830, "dur": 603, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656919440, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656919468, "dur": 4, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656919472, "dur": 20, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656919495, "dur": 3, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656919499, "dur": 10, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656919511, "dur": 439, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656919953, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656919991, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656919992, "dur": 30, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920024, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920026, "dur": 30, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920059, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920061, "dur": 44, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920108, "dur": 67, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920177, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920210, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920212, "dur": 25, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920242, "dur": 155, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920400, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920409, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920433, "dur": 428, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920862, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920888, "dur": 4, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920893, "dur": 13, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656920908, "dur": 483, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656921394, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656921433, "dur": 1, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656921435, "dur": 22, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656921464, "dur": 358, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656921825, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656921860, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656921862, "dur": 23, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656921888, "dur": 487, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656922378, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656922412, "dur": 4, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656922417, "dur": 14, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656922432, "dur": 436, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656922870, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656922896, "dur": 16, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656922915, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656922916, "dur": 509, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656923428, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656923432, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656923456, "dur": 3, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656923461, "dur": 15, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656923478, "dur": 412, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656923897, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656923924, "dur": 4, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656923929, "dur": 11, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656923942, "dur": 533, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656924484, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656924508, "dur": 3, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656924512, "dur": 19, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656924533, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656924534, "dur": 498, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656925037, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656925080, "dur": 1, "ph": "X", "name": "ProcessMessages 1085", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656925081, "dur": 526, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656925615, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656925642, "dur": 1, "ph": "X", "name": "ProcessMessages 1069", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656925644, "dur": 418, "ph": "X", "name": "ReadAsync 1069", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926064, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926069, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926097, "dur": 4, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926102, "dur": 19, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926124, "dur": 425, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926566, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926587, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926591, "dur": 25, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926618, "dur": 3, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926623, "dur": 11, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656926636, "dur": 550, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927193, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927219, "dur": 3, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927224, "dur": 20, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927245, "dur": 4, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927250, "dur": 25, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927277, "dur": 3, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927282, "dur": 20, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927303, "dur": 4, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927311, "dur": 10, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927322, "dur": 408, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927736, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927762, "dur": 17, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927780, "dur": 3, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927785, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927808, "dur": 3, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927818, "dur": 21, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927841, "dur": 4, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927845, "dur": 13, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656927860, "dur": 149, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928013, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928017, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928029, "dur": 395, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928429, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928467, "dur": 13, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928482, "dur": 385, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928880, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928907, "dur": 24, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928933, "dur": 3, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928937, "dur": 12, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656928951, "dur": 385, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656929342, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656929371, "dur": 19, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656929396, "dur": 502, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656929900, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656929967, "dur": 4, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656929972, "dur": 457, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656930435, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656930472, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656930474, "dur": 27, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656930504, "dur": 419, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656930929, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656930970, "dur": 28, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656931001, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656931033, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656931035, "dur": 559, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656931605, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656931642, "dur": 1, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656931649, "dur": 28, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656931680, "dur": 535, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656932217, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656932261, "dur": 7, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656932270, "dur": 577, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656932851, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656932882, "dur": 1, "ph": "X", "name": "ProcessMessages 1138", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656932885, "dur": 588, "ph": "X", "name": "ReadAsync 1138", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933485, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933495, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933525, "dur": 51, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933580, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933622, "dur": 21, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933645, "dur": 10, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933669, "dur": 50, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933721, "dur": 1, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933723, "dur": 32, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933768, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656933769, "dur": 440, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656934214, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656934219, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656934257, "dur": 1, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656934258, "dur": 26, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656934286, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656934288, "dur": 29, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656934320, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656934349, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656934354, "dur": 20, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656934380, "dur": 2903, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656937288, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656937364, "dur": 444, "ph": "X", "name": "ProcessMessages 3003", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656937890, "dur": 97, "ph": "X", "name": "ReadAsync 3003", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938037, "dur": 5, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938044, "dur": 93, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938198, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938201, "dur": 32, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938269, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938273, "dur": 79, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938355, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938358, "dur": 133, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938493, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938501, "dur": 26, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938529, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938531, "dur": 19, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938555, "dur": 285, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938842, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938844, "dur": 133, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938979, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656938983, "dur": 17, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939123, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939125, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939341, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939344, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939425, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939427, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939478, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939481, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939505, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939507, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939530, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939531, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939702, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939704, "dur": 147, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656939901, "dur": 1233, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656941139, "dur": 174, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656941450, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656941451, "dur": 2470, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656943926, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656943928, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656943956, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656943959, "dur": 98, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656944060, "dur": 867, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656944930, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656944949, "dur": 869, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656945823, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656945853, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656945879, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656945883, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656945909, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946077, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946100, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946122, "dur": 127, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946253, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946290, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946405, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946406, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946437, "dur": 91, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946529, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946550, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946552, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946573, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946592, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946612, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946707, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946820, "dur": 176, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656946999, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947018, "dur": 98, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947119, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947136, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947152, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947169, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947185, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947202, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947294, "dur": 145, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947442, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947459, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947485, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947505, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947506, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947528, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947591, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947688, "dur": 19, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947710, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947728, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947745, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947773, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947789, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947869, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947871, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947915, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947934, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947960, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656947976, "dur": 68, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948045, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948134, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948152, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948169, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948186, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948278, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948281, "dur": 151, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948436, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948544, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948546, "dur": 95, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948644, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948727, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948745, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948826, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948845, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948883, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656948992, "dur": 22, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949018, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949041, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949061, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949159, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949193, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949214, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949230, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949295, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949312, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949395, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949411, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949427, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949445, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949467, "dur": 199, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949668, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949669, "dur": 87, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949759, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949779, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949796, "dur": 124, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656949923, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656950071, "dur": 491, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656950639, "dur": 42, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656950683, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656950684, "dur": 19, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656950707, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656950733, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656950798, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656950823, "dur": 141, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656950968, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656951108, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656951109, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656951136, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656951138, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656951197, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656951222, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656951224, "dur": 262, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656951646, "dur": 1474, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656953128, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615656953133, "dur": 111311, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657064455, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657064460, "dur": 139, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657064600, "dur": 2213, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657066817, "dur": 1195, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068018, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068020, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068050, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068062, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068138, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068152, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068308, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068320, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068358, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068369, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068460, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068549, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068714, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068725, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068934, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657068946, "dur": 323, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657069271, "dur": 41, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657069313, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657069351, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657069461, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657069501, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657069527, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657069537, "dur": 564, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070104, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070118, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070161, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070198, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070314, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070353, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070370, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070393, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070426, "dur": 355, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070785, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657070797, "dur": 444, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657071244, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657071260, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657071348, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657071358, "dur": 234, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657071595, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657071637, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657071750, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657071767, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657071851, "dur": 377, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657072231, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657072241, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657072319, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657072336, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657072424, "dur": 128, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657072557, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657072631, "dur": 247, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657072882, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657072893, "dur": 415, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073311, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073313, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073326, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073456, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073458, "dur": 179, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073641, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073658, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073670, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073680, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073699, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073714, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073773, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073775, "dur": 13, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073792, "dur": 10, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073804, "dur": 8, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073815, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073836, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073905, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073984, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657073986, "dur": 87, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074075, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074078, "dur": 257, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074339, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074352, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074367, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074437, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074468, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074471, "dur": 11, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074485, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074569, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074580, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074590, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074605, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074672, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074700, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074702, "dur": 78, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074785, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074787, "dur": 95, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074885, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657074904, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657075014, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615657075115, "dur": 8156831, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665231957, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665231961, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665231992, "dur": 4443, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665236438, "dur": 17417, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665253864, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665253867, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665253987, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665253991, "dur": 162531, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665416535, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665416540, "dur": 227, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665416769, "dur": 109, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665416880, "dur": 3687, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665420573, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665420575, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665420601, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665420603, "dur": 864, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665421470, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665421495, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665421510, "dur": 25208, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665446735, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665446740, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665446822, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665446826, "dur": 337, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665447167, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665447170, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665447199, "dur": 38, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665447238, "dur": 612, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665447853, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665447856, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665447884, "dur": 500, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749615665448387, "dur": 8609, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26084, "tid": 497, "ts": 1749615665468783, "dur": 3100, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26084, "tid": 8589934592, "ts": 1749615656819566, "dur": 101431, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": 1749615656920998, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": 1749615656921003, "dur": 751, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26084, "tid": 497, "ts": 1749615665471886, "dur": 100, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26084, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26084, "tid": 4294967296, "ts": 1749615656743346, "dur": 8714580, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749615656745798, "dur": 68848, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749615665457940, "dur": 3606, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749615665460165, "dur": 82, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749615665461618, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26084, "tid": 497, "ts": 1749615665471987, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749615656849400, "dur": 23783, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749615656873313, "dur": 18697, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749615656892121, "dur": 51, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749615656892173, "dur": 467, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749615656893252, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_858DAC66656EEE20.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749615656893824, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_36BB48A459E34DBF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749615656892654, "dur": 42862, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749615656935527, "dur": 8512352, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749615665447881, "dur": 199, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749615665448080, "dur": 129, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749615665448262, "dur": 56, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749615665448410, "dur": 60, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749615665448487, "dur": 1232, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749615656892925, "dur": 42656, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656935868, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749615656936175, "dur": 284, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1749615656935771, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21B473704285E949.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749615656936734, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749615656936733, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_B663C5A7D98A973E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749615656936898, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1749615656937078, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749615656937511, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749615656937919, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749615656938231, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749615656938762, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656938882, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749615656939254, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749615656939575, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749615656939771, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656940449, "dur": 1685, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Identity.Core.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749615656940109, "dur": 2986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656943095, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656943322, "dur": 1305, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Utilities\\IconExportUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749615656944686, "dur": 843, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Utilities\\EditorFilteringUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749615656943322, "dur": 2291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656945613, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656946243, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656946478, "dur": 958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656947743, "dur": 399, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749615656947437, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749615656948301, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656948821, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656948889, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656949528, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656949635, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615656950079, "dur": 118612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615657068693, "dur": 1791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749615657070484, "dur": 1942, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615657072496, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749615657074284, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749615657074463, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749615657074594, "dur": 427, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749615657072430, "dur": 2727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749615657075158, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749615657075262, "dur": 8372625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615656892846, "dur": 42706, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615656935781, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1749615656936027, "dur": 357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1749615656935975, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_834724766976A551.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749615656936385, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615656936558, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656936556, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F45A17676A5D3A15.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749615656936730, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615656936912, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749615656937723, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656938448, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656938598, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656938654, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656938977, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656939046, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656939287, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656939633, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656939967, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656940055, "dur": 474, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656940605, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749615656940988, "dur": 447, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749615656941584, "dur": 542, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749615656942130, "dur": 287, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ITestSuiteModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749615656942648, "dur": 386, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\PlayerConnectionMessageIds.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749615656943035, "dur": 390, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749615656943640, "dur": 585, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\CoroutineRunner.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749615656937352, "dur": 7119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749615656944472, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615656944612, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749615656945744, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656945573, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749615656946542, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749615656946943, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656947240, "dur": 562, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656946819, "dur": 1188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749615656948008, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615656948104, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615656948353, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749615656948420, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615656948509, "dur": 386, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656949088, "dur": 495, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749615656948476, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749615656949745, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615656949845, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1749615656950310, "dur": 92, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615656950696, "dur": 114374, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1749615657066845, "dur": 2288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749615657069134, "dur": 956, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615657070095, "dur": 1706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749615657071801, "dur": 1150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615657072958, "dur": 1241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749615657074199, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615657074302, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615657074456, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615657075077, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749615657075286, "dur": 8372596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656892872, "dur": 42687, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656935872, "dur": 326, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615656936199, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1749615656935771, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749615656936493, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615656936780, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1749615656936492, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_8CE5665EC89AC339.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749615656936925, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656937050, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1749615656937445, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1749615656937731, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749615656937991, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656938086, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656938188, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749615656938391, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749615656938618, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656938890, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749615656939256, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749615656939514, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749615656939962, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656940408, "dur": 1833, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-2-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615656942559, "dur": 818, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-profile-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615656940260, "dur": 3288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656943577, "dur": 1155, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyGroupOption.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749615656943548, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656944860, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656945059, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656945639, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656946206, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656946464, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656947190, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749615656947868, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749615656947931, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656948335, "dur": 451, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615656948814, "dur": 595, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615656948146, "dur": 1281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749615656949516, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656949640, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615656950071, "dur": 118639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615657069960, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615657070057, "dur": 1393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615657068712, "dur": 3364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749615657072077, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615657072729, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615657073241, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615657073974, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Channels.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615657074595, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749615657072408, "dur": 2546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749615657075000, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615657075216, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749615657075664, "dur": 8372243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656892838, "dur": 42700, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656935810, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749615656936106, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1749615656936067, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_9E8FB24CB62B009F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749615656936413, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656936488, "dur": 238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749615656936486, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_D33284FC32F2214E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749615656936729, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656937001, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749615656937139, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749615656937355, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749615656937640, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749615656938447, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749615656938648, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656938756, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656938936, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749615656939195, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749615656939403, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3043913547040211301.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749615656939810, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656940036, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656940814, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656941086, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656941308, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656941549, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656941771, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656942014, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656942390, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656943044, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656943293, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656943815, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656944055, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656944277, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656944501, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656944801, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656945015, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656945225, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656945424, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656945839, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656946467, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656946769, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749615656946954, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749615656947410, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656947596, "dur": 763, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656948363, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749615656948902, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749615656949276, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656949465, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749615656949707, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656949852, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749615656950325, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615656950438, "dur": 118262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615657068702, "dur": 1976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749615657070679, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615657072498, "dur": 365, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749615657070744, "dur": 2326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749615657073070, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749615657073975, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749615657074596, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.KeyPerFile.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749615657075014, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749615657073519, "dur": 1923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749615657075476, "dur": 8372384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656892819, "dur": 42714, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656935535, "dur": 2998, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656938566, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1749615656938680, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656938770, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656938846, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749615656939144, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4345639507432806382.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749615656939351, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656939435, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9147286922948009921.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749615656939687, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656939938, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656940039, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656940729, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656941066, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656941281, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656941515, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656941735, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656941944, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656942172, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656942404, "dur": 684, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\IGraphVariableUnit.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749615656942404, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656943303, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656943796, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656944019, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656944242, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656944457, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656944661, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656944928, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656945128, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656945342, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656945574, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656946161, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656946473, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656946774, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749615656947240, "dur": 397, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749615656947797, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditorModule\\SpriteOutlineModule.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749615656946961, "dur": 897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749615656947859, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656947922, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656948229, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749615656948355, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749615656948736, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656948795, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656948910, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656949403, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656949660, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656949835, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615656950069, "dur": 116782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615657066853, "dur": 1151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749615657068005, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615657068654, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749615657069914, "dur": 1874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749615657071789, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615657072497, "dur": 369, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749615657074285, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749615657071988, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749615657074355, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615657074476, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749615657074475, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749615657074676, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615657074762, "dur": 324, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749615657075087, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749615657075354, "dur": 8372501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656892895, "dur": 42672, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656935796, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749615656936009, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1749615656935771, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_094A556A01D249DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749615656936398, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656936668, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749615656936759, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1749615656936667, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_2B4ABF5625D5DAD2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749615656936904, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749615656937130, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749615656937379, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1749615656937497, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749615656937748, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749615656937968, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749615656938028, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749615656938257, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749615656938466, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749615656938722, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749615656939053, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749615656939311, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749615656939616, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656939950, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17766336155681823506.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749615656940194, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656940841, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656941548, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656941770, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656941993, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656942304, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656942647, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics\\OnParticleCollision.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749615656942522, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656943679, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\EditorWindowWrapper.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749615656944722, "dur": 736, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Widgets\\Nodes\\NodeColorMix.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749615656943275, "dur": 2193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656945468, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656945709, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656946147, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656946465, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656946821, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749615656947066, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656947225, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749615656947732, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656947870, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749615656948299, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656948368, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749615656948679, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749615656949124, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656949199, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656949401, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656949627, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656949887, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615656950129, "dur": 116727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615657066858, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749615657068093, "dur": 672, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615657068771, "dur": 1328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749615657070100, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615657070167, "dur": 1942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749615657072110, "dur": 947, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615657073978, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749615657074474, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Web.HttpUtility.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749615657073064, "dur": 2168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749615657075276, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749615657075360, "dur": 8372498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656892915, "dur": 42660, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656935817, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749615656936116, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1749615656935770, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_699CA9E1D209A267.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749615656936401, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656936562, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749615656936785, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1749615656936560, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_9D4B9A2920A7642B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749615656936988, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749615656937086, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749615656937711, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656937792, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749615656938045, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656938102, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1749615656938294, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749615656938560, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749615656938718, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1749615656938878, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749615656939147, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749615656939397, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13445476680238899994.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749615656939628, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749615656939849, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656940030, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656941012, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656941228, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656941469, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656941687, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656941908, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656942136, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656942519, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656943137, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656943554, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656944698, "dur": 705, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Internal\\fsPortableReflection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749615656944221, "dur": 1661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656945882, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656946467, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656946771, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749615656947253, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749615656947083, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749615656947757, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656947873, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656948439, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749615656948598, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749615656948994, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656949099, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656949400, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656949551, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749615656949847, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749615656950167, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615656950275, "dur": 118445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615657070055, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749615657068721, "dur": 2198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749615657072502, "dur": 448, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749615657070968, "dur": 2882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749615657073851, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615657074041, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749615657075014, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.ConfigurationExtensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749615657073951, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749615657075575, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749615657075669, "dur": 8372204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656892937, "dur": 42651, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656935776, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749615656936058, "dur": 309, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1749615656935761, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749615656936368, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656936745, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749615656936743, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_66549C0209C53F09.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749615656936980, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749615656937199, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749615656937324, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749615656937625, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749615656937783, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749615656938083, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749615656938250, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749615656938570, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749615656938737, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749615656938909, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749615656939136, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749615656939392, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749615656939558, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749615656939768, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656939968, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6591615775254759062.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749615656940211, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656940837, "dur": 737, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\BindingUtility.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749615656940837, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656942187, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656942422, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656943186, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656943419, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656943665, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656943920, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656944159, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Exceptions\\UnexpectedEnumValueException.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749615656944721, "dur": 713, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Exceptions\\InvalidImplementationException.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749615656944159, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656945886, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656946465, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656946867, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749615656947184, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749615656947075, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749615656947529, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656947632, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656948381, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749615656948496, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749615656948904, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749615656950131, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656950244, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749615656950357, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749615656950941, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656951035, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749615656951145, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749615656951541, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656951620, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749615656951724, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749615656951975, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656952063, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749615656952199, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749615656952536, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749615656953367, "dur": 52, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615656953477, "dur": 8279153, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749615665234249, "dur": 17611, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749615665234247, "dur": 18493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749615665254266, "dur": 139, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749615665254431, "dur": 162676, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749615665421095, "dur": 26156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749615665421093, "dur": 26161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749615665447273, "dur": 535, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656892966, "dur": 42627, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656935795, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656936053, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1749615656935761, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0998A984E55471CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749615656936377, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656936523, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656936764, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1749615656936522, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_C1150B350FC25DE4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749615656936986, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749615656938709, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656938762, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656938985, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656939605, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656939880, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656940098, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656940260, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656941531, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656937547, "dur": 4110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749615656941658, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656941759, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656942516, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\GraphOutputAnalyser.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749615656942086, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656943180, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656943573, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656944235, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656944531, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656944937, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656945142, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656945384, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656945963, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656946466, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656946770, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749615656946925, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749615656947049, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656947240, "dur": 375, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615656947113, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749615656948004, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656948123, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749615656948245, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749615656948741, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656948872, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656949405, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656949705, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749615656949830, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749615656950105, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749615656950455, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656950587, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749615656950722, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749615656951246, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615656951335, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749615656951450, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749615656951853, "dur": 115001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615657066857, "dur": 2076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749615657068933, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615657068997, "dur": 1932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749615657070929, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615657071034, "dur": 2005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749615657073039, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615657074336, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Http.Json.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749615657073123, "dur": 2159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749615657075283, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749615657075355, "dur": 8372515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656892997, "dur": 42601, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656935816, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749615656936089, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1749615656935760, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5AA67F5B53C44BAD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749615656936384, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749615656936383, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_3787B1E93F84ED7A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749615656936615, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749615656936782, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1749615656936613, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BC5EF78C5461B6C8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749615656937029, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749615656937422, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749615656937728, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749615656937909, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749615656938187, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749615656938550, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749615656938656, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656938759, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749615656938971, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656939121, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749615656939413, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7634377931854478575.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749615656939629, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656939964, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14355918427286719942.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749615656940176, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656940595, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656940725, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656940863, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656941022, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656941164, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656941310, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656941480, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656941632, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656941780, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656942092, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656942471, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector2\\Vector2Angle.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749615656942471, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656943302, "dur": 723, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\UpdateWizard\\UpdateBackupPage.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749615656943256, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656944132, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656944289, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656944421, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656944583, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656944947, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656945148, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656945479, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656945709, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656945951, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656946471, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656947189, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749615656948133, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\PendingChanges\\Dialogs\\LaunchDependenciesDialog.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749615656948335, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Shelves\\ShelvesViewMenu.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749615656947457, "dur": 1131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749615656948588, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656949086, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749615656949268, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749615656949641, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656949706, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749615656949772, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749615656950055, "dur": 982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656951038, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749615656951146, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749615656951361, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615656951425, "dur": 118543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615657070002, "dur": 1449, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749615657072395, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.AppContext.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749615657073063, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749615657069969, "dur": 3737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749615657073706, "dur": 1318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615657075067, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749615657075255, "dur": 8372623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656893033, "dur": 42570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656935783, "dur": 346, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615656936130, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1749615656935748, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_006DC43AA5DD858A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749615656936394, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656936556, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615656936554, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_3A7A83D106FCE03C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749615656936735, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656936890, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656937026, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749615656937223, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749615656938021, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1749615656938248, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749615656938494, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656938547, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749615656938759, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749615656939019, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749615656939225, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749615656939594, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749615656940088, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656940481, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656941170, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656941323, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656941491, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656941632, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656941773, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656941904, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656942055, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656942391, "dur": 906, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\UnifiedVariableUnit.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749615656942391, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656943535, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656943745, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656943969, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656944189, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656944701, "dur": 698, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Branch\\Dialogs\\RenameBranchDialog.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749615656944692, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656945842, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656946475, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656947245, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749615656947352, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656947743, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615656947826, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615656947556, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749615656948061, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656948163, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656948354, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749615656948681, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656948814, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615656949087, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615656948807, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749615656949415, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656949550, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749615656949623, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749615656949987, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656950065, "dur": 2007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615656952073, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749615656952218, "dur": 114655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615657068686, "dur": 1238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615657069961, "dur": 1496, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615657066874, "dur": 4772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749615657071646, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749615657072395, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615657072843, "dur": 1140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615657074285, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749615657072233, "dur": 3123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749615657075392, "dur": 8372489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656893052, "dur": 42556, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656935865, "dur": 288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749615656936154, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1749615656935760, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7027E12F917228B0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749615656936446, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656936572, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749615656936570, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_B5228D2EF183D9AF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749615656936725, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656937072, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749615656937354, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749615656937687, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656937794, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749615656938219, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749615656938543, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749615656938611, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656938715, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749615656938799, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656938884, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749615656939130, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656939213, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749615656939688, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656940101, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656940612, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656940862, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656941094, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656941417, "dur": 642, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.performance.profile-analyzer@1.2.3\\Editor\\ProgressBarDisplay.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749615656941309, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656942157, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656942552, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656943191, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656943432, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656943670, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656944002, "dur": 1869, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Serialization\\DictionaryAsset.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749615656943890, "dur": 2044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656945934, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656946473, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656946768, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749615656946930, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749615656947353, "dur": 2327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615656949680, "dur": 422, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749615656950104, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749615656950103, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749615656950658, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749615656951109, "dur": 116377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615657067489, "dur": 1953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749615657069443, "dur": 1506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615657070954, "dur": 1795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749615657072750, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749615657073975, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749615657074055, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749615657074435, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749615657074596, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749615657072868, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749615657075329, "dur": 8372527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656893083, "dur": 42529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656935772, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615656936066, "dur": 379, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1749615656935747, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B87B178D29F0F556.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749615656936446, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656936613, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615656936784, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1749615656936612, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_9CA00EB8472AEF4E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749615656937021, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749615656937447, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749615656937861, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1749615656938150, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749615656938296, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749615656938628, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656938747, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749615656938941, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749615656939151, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749615656939446, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749615656939710, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656940139, "dur": 728, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.UserSecrets.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615656940998, "dur": 1140, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Caching.Memory.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615656942530, "dur": 803, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.WebSockets.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615656940138, "dur": 3277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656943415, "dur": 834, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Migrations\\Migration_1_0_5_to_1_0_6.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749615656943415, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656944471, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656944683, "dur": 860, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\CreateWorkspace\\ValidRepositoryName.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749615656944683, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656945841, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656946476, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656946766, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749615656946943, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615656947239, "dur": 512, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615656946840, "dur": 1026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749615656947867, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656948455, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749615656949121, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749615656949373, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656949575, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656949632, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615656950099, "dur": 116766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615657066869, "dur": 1577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749615657068447, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615657069961, "dur": 2448, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.DiagnosticSource.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615657072843, "dur": 1207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615657074284, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615657074474, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749615657068946, "dur": 5929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749615657074875, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615657075076, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749615657075260, "dur": 8372637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656893107, "dur": 42509, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656935861, "dur": 308, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749615656936170, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1749615656935747, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4740E213CDF2FB27.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749615656936449, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656936605, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749615656936789, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1749615656936604, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_264A36B967002A74.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749615656937016, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749615656937339, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749615656937673, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1749615656937960, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1749615656938011, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656938130, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749615656938661, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1749615656938833, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749615656939120, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656939301, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656939417, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749615656939734, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656940119, "dur": 2011, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749615656942525, "dur": 1055, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749615656940087, "dur": 3557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656943645, "dur": 1083, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Documentation\\XmlDocumentationTags.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749615656943645, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656944944, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656945152, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656945404, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\EditModeRunner.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749615656945404, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656946107, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656946481, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656947193, "dur": 1030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749615656948509, "dur": 584, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749615656948261, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749615656949299, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656949531, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656949632, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615656950095, "dur": 116754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615657066861, "dur": 1742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749615657068603, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615657068694, "dur": 1786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749615657070481, "dur": 4807, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749615657075314, "dur": 8372558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656893133, "dur": 42488, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656935795, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749615656936111, "dur": 341, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1749615656935746, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_C7A46CFE1C6B2861.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749615656936485, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749615656936484, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_7BBC845FD6321414.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749615656936731, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656937018, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749615656937274, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749615656937519, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749615656938256, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749615656938458, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749615656938729, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656938829, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749615656938942, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749615656939129, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656939205, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10654656162358534522.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749615656939511, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11295897478892413411.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749615656939664, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656940667, "dur": 1384, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\ucrtbase.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749615656942222, "dur": 1133, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749615656940014, "dur": 3657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656943671, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656943911, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656944137, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656944363, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656944612, "dur": 907, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Merge\\Gluon\\IncomingChangesTab.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749615656944612, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656945646, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656946181, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656946478, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656947192, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749615656947808, "dur": 315, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749615656948133, "dur": 445, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749615656948769, "dur": 517, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749615656948123, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749615656949499, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656949636, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615656950075, "dur": 116770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615657068687, "dur": 1280, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749615657066849, "dur": 3178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749615657070028, "dur": 2421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615657072452, "dur": 1423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749615657073876, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615657074412, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749615657074715, "dur": 322, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1749615657075065, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615657075126, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749615657075405, "dur": 8372446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656893156, "dur": 42469, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656935848, "dur": 403, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749615656936253, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1749615656935743, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_B4C1A84D8F19D0AB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749615656936566, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749615656936789, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1749615656936565, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_A8B15C2CFD2ACA00.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749615656937033, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1749615656937119, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749615656937502, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749615656938024, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656938117, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749615656938589, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749615656938763, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749615656938915, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749615656939201, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749615656939333, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656939389, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749615656939633, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5521740788080646575.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749615656939842, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656939985, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5806762800881712256.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749615656940230, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656940918, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656941140, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656941347, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656941970, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656942110, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656942297, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656942427, "dur": 760, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4Distance.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749615656942427, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656943405, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656943595, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\InspectorImplementationOrderAttribute.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749615656943595, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656944315, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656944441, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656944793, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656945005, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656945223, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656945442, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656946064, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656946490, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656946760, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749615656947190, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749615656946957, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749615656947527, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656947594, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656947935, "dur": 950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749615656948885, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656949124, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749615656949573, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656949704, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749615656949759, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656949932, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749615656950149, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615656950421, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749615656950487, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749615656950771, "dur": 116089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615657066861, "dur": 1936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749615657068798, "dur": 695, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615657070074, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749615657069500, "dur": 2001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749615657071501, "dur": 920, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615657072424, "dur": 1563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749615657074050, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615657074414, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749615657074706, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749615657075083, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749615657075312, "dur": 8372559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656893185, "dur": 42445, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656935797, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749615656936069, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1749615656935742, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3C3E151BE5CA996B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749615656936406, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656936462, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749615656936462, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_6A2BF8C19DF87A5F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749615656936728, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656937012, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749615656937399, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749615656937706, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1749615656937912, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1749615656938083, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1749615656938215, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749615656938590, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749615656938736, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749615656938880, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749615656939131, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749615656939370, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749615656939653, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656939934, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656940004, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656940206, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656940610, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656940741, "dur": 699, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Window\\Modes\\TimelineReadOnlyMode.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749615656940741, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656941658, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656941912, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656942649, "dur": 659, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\Equal.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749615656942517, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656943330, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656943760, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656943893, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656944241, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656944463, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656944678, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656944849, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656945029, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656945159, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656945293, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656945447, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656945922, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656946472, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656946951, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749615656947006, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656947200, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749615656947452, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656947523, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656948093, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656948564, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749615656948517, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749615656949060, "dur": 1112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615656950193, "dur": 116675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615657068686, "dur": 1318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749615657070055, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749615657066870, "dur": 3274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749615657070144, "dur": 653, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615657070802, "dur": 1544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749615657072388, "dur": 1788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749615657074177, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615657074455, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615657074706, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1749615657075084, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749615657075362, "dur": 8372491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656893204, "dur": 42430, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656935820, "dur": 504, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1749615656935743, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E5F938C6E667C796.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749615656936403, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656936460, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749615656936459, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E7683A8498EB54AE.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749615656936642, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749615656936780, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1749615656936641, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_3AF52CA4203FDAC9.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749615656937059, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1749615656937416, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749615656937617, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749615656937804, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749615656938312, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749615656938798, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749615656939055, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749615656939444, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/908057632503650206.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749615656939818, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656939924, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749615656940857, "dur": 1238, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749615656940041, "dur": 2055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656942096, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656942429, "dur": 625, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\Vector3Average.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749615656942429, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656943206, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656943372, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656943738, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656943878, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656944030, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656944175, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656945115, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656945256, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656945447, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656945978, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656946474, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656946950, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749615656947169, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656947249, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749615656947318, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749615656947369, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656947461, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749615656947696, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656947775, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656948516, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749615656948776, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749615656948599, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749615656948914, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656949145, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656949400, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656949629, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656950062, "dur": 1276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615656951338, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749615656951427, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749615656951654, "dur": 115208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615657068688, "dur": 1279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749615657066866, "dur": 3318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749615657070185, "dur": 815, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615657072121, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749615657072394, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749615657072843, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749615657073126, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749615657071003, "dur": 2302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749615657073305, "dur": 701, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615657074011, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615657074410, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615657074566, "dur": 676, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615657075254, "dur": 8345841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749615665421098, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1749615665421097, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1749615665421198, "dur": 911, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1749615665422113, "dur": 25780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656893223, "dur": 42415, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656935868, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615656936160, "dur": 415, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1749615656935747, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_F4A91D512CEBDB1F.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749615656936741, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615656936741, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_743DCD7F83EC4AAC.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749615656936906, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749615656937095, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749615656937215, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1749615656937479, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749615656937673, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656937818, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1749615656938111, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749615656938258, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749615656938593, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749615656938867, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749615656939064, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656939126, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749615656939221, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11932646040550446581.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749615656939407, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749615656939800, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656941429, "dur": 1923, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.Native.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615656940051, "dur": 3388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656943439, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656943590, "dur": 1144, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\GuidInspector.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749615656943590, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656944931, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656945135, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656945383, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656945955, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656946468, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656947184, "dur": 478, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615656947797, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615656948133, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Path\\Editor\\IMGUI\\IDrawer.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749615656946948, "dur": 1415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749615656948363, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656948499, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656948832, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656949529, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656949632, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615656950091, "dur": 116779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615657067154, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615657067937, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Protocols.Json.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615657066873, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749615657069269, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615657069501, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615657069354, "dur": 2024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749615657071699, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615657072842, "dur": 1142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615657074284, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615657074596, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749615657071423, "dur": 3476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749615657074929, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749615657075131, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749615657075484, "dur": 8372399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656893242, "dur": 42400, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656935824, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749615656936065, "dur": 338, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1749615656935741, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_A8E53AE16CB1305D.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749615656936452, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749615656936451, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C06C6D7BC8ACCF56.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749615656936721, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656936990, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749615656937653, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1749615656937817, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1749615656938037, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656938178, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749615656938322, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749615656938525, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656938594, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1749615656938766, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656938832, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749615656939100, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1749615656939175, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7038082120299526273.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749615656939548, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17122354673617034552.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749615656939782, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656940023, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656941155, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656941382, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656941814, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656942055, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656942495, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656943531, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656943761, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656944001, "dur": 1407, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\MemberInfoComparer.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749615656943978, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656945855, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656946476, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656946765, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749615656946943, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749615656947434, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749615656947742, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsReflectionUtility.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749615656948084, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnSubmitMessageListener.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749615656946928, "dur": 1423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749615656948351, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656948471, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749615656948563, "dur": 538, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749615656948552, "dur": 1396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749615656949948, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656950181, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749615656950297, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749615656950644, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615656950783, "dur": 116075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615657066867, "dur": 2164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749615657069031, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615657069100, "dur": 1875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749615657070975, "dur": 897, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749615657072394, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749615657072842, "dur": 1144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.IIS.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749615657074041, "dur": 290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.DependencyInjection.Abstractions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749615657071879, "dur": 3324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749615657075256, "dur": 8372648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749615665453217, "dur": 3083, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26084, "tid": 497, "ts": 1749615665473948, "dur": 1909, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26084, "tid": 497, "ts": 1749615665475936, "dur": 3341, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26084, "tid": 497, "ts": 1749615665467289, "dur": 12577, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}