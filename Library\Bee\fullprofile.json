{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26084, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26084, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26084, "tid": 699, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26084, "tid": 699, "ts": 1749617150108502, "dur": 612, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26084, "tid": 699, "ts": 1749617150112320, "dur": 912, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26084, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26084, "tid": 1, "ts": 1749617149648144, "dur": 3462, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749617149651609, "dur": 17366, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749617149668988, "dur": 23327, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26084, "tid": 699, "ts": 1749617150113235, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 26084, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149646965, "dur": 33607, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149680576, "dur": 420450, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149681555, "dur": 2164, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149683728, "dur": 1099, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149684830, "dur": 217, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685049, "dur": 9, "ph": "X", "name": "ProcessMessages 20522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685059, "dur": 27, "ph": "X", "name": "ReadAsync 20522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685091, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685095, "dur": 50, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685148, "dur": 2, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685152, "dur": 26, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685180, "dur": 4, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685186, "dur": 61, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685254, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685282, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685284, "dur": 40, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685327, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685329, "dur": 120, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685451, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685456, "dur": 57, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685515, "dur": 2, "ph": "X", "name": "ProcessMessages 2551", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685518, "dur": 25, "ph": "X", "name": "ReadAsync 2551", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685546, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685549, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685579, "dur": 30, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685611, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685613, "dur": 32, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685648, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685650, "dur": 34, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685686, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149685687, "dur": 742, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686434, "dur": 162, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686599, "dur": 11, "ph": "X", "name": "ProcessMessages 18761", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686611, "dur": 41, "ph": "X", "name": "ReadAsync 18761", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686655, "dur": 5, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686662, "dur": 41, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686710, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686740, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686745, "dur": 45, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686792, "dur": 4, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686798, "dur": 22, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686822, "dur": 3, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686826, "dur": 23, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686851, "dur": 4, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686856, "dur": 20, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686878, "dur": 3, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686883, "dur": 27, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686911, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686915, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686951, "dur": 23, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686977, "dur": 1, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149686978, "dur": 23, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687003, "dur": 3, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687008, "dur": 29, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687038, "dur": 4, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687043, "dur": 21, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687066, "dur": 3, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687071, "dur": 18, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687091, "dur": 3, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687096, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687115, "dur": 3, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687119, "dur": 54, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687179, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687200, "dur": 3, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687205, "dur": 23, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687229, "dur": 4, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687234, "dur": 21, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687257, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687262, "dur": 20, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687284, "dur": 3, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687288, "dur": 21, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687311, "dur": 4, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687316, "dur": 20, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687338, "dur": 3, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687342, "dur": 14, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687362, "dur": 16, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687380, "dur": 4, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687385, "dur": 24, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687411, "dur": 4, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687415, "dur": 19, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687436, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687441, "dur": 19, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687461, "dur": 3, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687466, "dur": 18, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687491, "dur": 24, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687516, "dur": 3, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687521, "dur": 23, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687546, "dur": 3, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687550, "dur": 16, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687568, "dur": 3, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687572, "dur": 180, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687758, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687784, "dur": 26, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687812, "dur": 4, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687817, "dur": 19, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687837, "dur": 3, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687842, "dur": 20, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687863, "dur": 3, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687868, "dur": 21, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687891, "dur": 3, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687895, "dur": 19, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687915, "dur": 4, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687920, "dur": 23, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687949, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687974, "dur": 3, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149687979, "dur": 20, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688001, "dur": 3, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688005, "dur": 18, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688025, "dur": 3, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688030, "dur": 20, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688052, "dur": 3, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688056, "dur": 20, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688078, "dur": 3, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688083, "dur": 19, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688104, "dur": 3, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688108, "dur": 15, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688125, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688129, "dur": 22, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688153, "dur": 3, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688157, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688178, "dur": 3, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688183, "dur": 22, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688207, "dur": 4, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688212, "dur": 18, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688232, "dur": 3, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688236, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688258, "dur": 3, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688262, "dur": 19, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688283, "dur": 3, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688287, "dur": 16, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688305, "dur": 3, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688309, "dur": 16, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688327, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688332, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688357, "dur": 3, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688362, "dur": 20, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688384, "dur": 10, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688395, "dur": 19, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688416, "dur": 3, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688420, "dur": 22, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688444, "dur": 3, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688449, "dur": 21, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688472, "dur": 3, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688476, "dur": 18, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688496, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688500, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688518, "dur": 3, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688522, "dur": 17, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688541, "dur": 3, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688545, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688567, "dur": 3, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688571, "dur": 24, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688597, "dur": 4, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688602, "dur": 18, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688622, "dur": 3, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688626, "dur": 20, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688648, "dur": 3, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688652, "dur": 19, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688673, "dur": 4, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688678, "dur": 16, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688696, "dur": 3, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688700, "dur": 16, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688722, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688748, "dur": 31, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688781, "dur": 3, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688785, "dur": 19, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688806, "dur": 3, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688811, "dur": 20, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688832, "dur": 3, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688837, "dur": 20, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688858, "dur": 3, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688863, "dur": 16, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688881, "dur": 3, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688885, "dur": 17, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688904, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688908, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688929, "dur": 3, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688933, "dur": 20, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688955, "dur": 3, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688959, "dur": 20, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688982, "dur": 4, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149688987, "dur": 18, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689007, "dur": 3, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689011, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689033, "dur": 3, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689037, "dur": 18, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689057, "dur": 3, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689061, "dur": 16, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689079, "dur": 3, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689084, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689106, "dur": 3, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689110, "dur": 19, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689131, "dur": 3, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689136, "dur": 18, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689156, "dur": 3, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689160, "dur": 21, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689182, "dur": 4, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689187, "dur": 19, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689208, "dur": 5, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689214, "dur": 18, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689234, "dur": 3, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689238, "dur": 15, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689259, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689288, "dur": 3, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689292, "dur": 19, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689313, "dur": 3, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689317, "dur": 18, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689338, "dur": 3, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689342, "dur": 23, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689367, "dur": 3, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689371, "dur": 19, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689392, "dur": 4, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689397, "dur": 18, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689417, "dur": 3, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689422, "dur": 16, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689439, "dur": 3, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689444, "dur": 20, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689466, "dur": 3, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689471, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689492, "dur": 4, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689497, "dur": 33, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689532, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689533, "dur": 41, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689577, "dur": 1, "ph": "X", "name": "ProcessMessages 1604", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689580, "dur": 23, "ph": "X", "name": "ReadAsync 1604", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689604, "dur": 17, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689624, "dur": 3, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689628, "dur": 31, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689661, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689664, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689689, "dur": 3, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689693, "dur": 19, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689713, "dur": 3, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689717, "dur": 21, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689739, "dur": 3, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689743, "dur": 68, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689815, "dur": 32, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689881, "dur": 53, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689936, "dur": 2, "ph": "X", "name": "ProcessMessages 3302", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689938, "dur": 26, "ph": "X", "name": "ReadAsync 3302", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689967, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689969, "dur": 25, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689996, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149689997, "dur": 32, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690031, "dur": 4, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690037, "dur": 23, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690061, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690066, "dur": 16, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690084, "dur": 3, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690088, "dur": 18, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690112, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690134, "dur": 3, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690139, "dur": 19, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690160, "dur": 3, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690164, "dur": 19, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690185, "dur": 3, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690189, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690210, "dur": 4, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690215, "dur": 20, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690237, "dur": 3, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690242, "dur": 17, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690261, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690265, "dur": 22, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690289, "dur": 3, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690293, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690313, "dur": 3, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690318, "dur": 20, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690340, "dur": 3, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690344, "dur": 23, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690369, "dur": 3, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690374, "dur": 16, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690392, "dur": 3, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690396, "dur": 19, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690417, "dur": 4, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690422, "dur": 26, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690449, "dur": 3, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690456, "dur": 43, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690505, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690524, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690529, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690552, "dur": 3, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690557, "dur": 25, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690583, "dur": 3, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690588, "dur": 20, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690610, "dur": 4, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690615, "dur": 20, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690637, "dur": 3, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690641, "dur": 20, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690663, "dur": 3, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690667, "dur": 22, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690691, "dur": 3, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690695, "dur": 15, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690712, "dur": 3, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690716, "dur": 26, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690748, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690769, "dur": 3, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690774, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690794, "dur": 3, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690799, "dur": 17, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690817, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690822, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690842, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690862, "dur": 3, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690867, "dur": 18, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690887, "dur": 3, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690891, "dur": 77, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690971, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149690995, "dur": 19, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691015, "dur": 16, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691035, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691111, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691140, "dur": 3, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691146, "dur": 18, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691166, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691169, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691213, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691216, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691234, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691237, "dur": 30, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691269, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691272, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691292, "dur": 3, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691296, "dur": 17, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691315, "dur": 3, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691319, "dur": 31, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691355, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691375, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691379, "dur": 18, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691399, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691403, "dur": 31, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691435, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691439, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691459, "dur": 3, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691463, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691482, "dur": 3, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691486, "dur": 29, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691516, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691520, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691544, "dur": 18, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691563, "dur": 3, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691567, "dur": 89, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691658, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691661, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691681, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691685, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691705, "dur": 2, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691709, "dur": 31, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691742, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691745, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691765, "dur": 3, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691769, "dur": 18, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691789, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691793, "dur": 27, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691822, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691825, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691845, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691848, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691868, "dur": 3, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691872, "dur": 28, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691902, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691905, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691925, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691929, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691949, "dur": 3, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149691952, "dur": 49, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692003, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692007, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692027, "dur": 3, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692030, "dur": 18, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692050, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692054, "dur": 26, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692082, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692085, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692113, "dur": 3, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692117, "dur": 16, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692135, "dur": 3, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692139, "dur": 24, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692164, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692168, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692187, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692191, "dur": 18, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692211, "dur": 3, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692214, "dur": 29, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692245, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692249, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692269, "dur": 3, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692273, "dur": 16, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692291, "dur": 2, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692294, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692317, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692361, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692403, "dur": 1, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692404, "dur": 20, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692428, "dur": 48, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692479, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692517, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692519, "dur": 21, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692543, "dur": 34, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692579, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692614, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692616, "dur": 22, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692641, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692683, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692718, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692720, "dur": 22, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692747, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692809, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692869, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692871, "dur": 23, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692898, "dur": 19, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692919, "dur": 28, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692950, "dur": 21, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149692974, "dur": 35, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693012, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693043, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693044, "dur": 23, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693071, "dur": 33, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693106, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693140, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693141, "dur": 22, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693165, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693167, "dur": 62, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693232, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693269, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693271, "dur": 20, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693296, "dur": 30, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693329, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693360, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693362, "dur": 22, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693387, "dur": 23, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693413, "dur": 29, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693445, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693475, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693477, "dur": 26, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693505, "dur": 23, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693531, "dur": 14, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693548, "dur": 26, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693577, "dur": 23, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693602, "dur": 18, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693623, "dur": 24, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693650, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693774, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693776, "dur": 38, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693817, "dur": 1, "ph": "X", "name": "ProcessMessages 1465", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693820, "dur": 30, "ph": "X", "name": "ReadAsync 1465", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693851, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693853, "dur": 30, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693885, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693887, "dur": 51, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693940, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693944, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693967, "dur": 3, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693971, "dur": 17, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149693994, "dur": 24, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694020, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694023, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694043, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694047, "dur": 20, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694068, "dur": 3, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694072, "dur": 24, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694098, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694102, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694124, "dur": 3, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694128, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694148, "dur": 3, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694152, "dur": 24, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694178, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694181, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694202, "dur": 3, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694206, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694226, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694229, "dur": 50, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694281, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694285, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694304, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694308, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694328, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694331, "dur": 28, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694362, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694365, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694386, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694389, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694409, "dur": 3, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694413, "dur": 26, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694441, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694445, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694464, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694468, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694487, "dur": 3, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694491, "dur": 26, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694519, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694523, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694542, "dur": 3, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694546, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694570, "dur": 27, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694598, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694602, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694619, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694623, "dur": 15, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694640, "dur": 2, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694644, "dur": 18, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694663, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694667, "dur": 15, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694683, "dur": 2, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694687, "dur": 17, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694705, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694709, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694729, "dur": 3, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694733, "dur": 25, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694760, "dur": 3, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694764, "dur": 20, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694786, "dur": 3, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694790, "dur": 19, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694811, "dur": 3, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694815, "dur": 15, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694833, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694837, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694859, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694863, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694887, "dur": 16, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694905, "dur": 3, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694909, "dur": 20, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694930, "dur": 3, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694934, "dur": 22, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694958, "dur": 3, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694961, "dur": 20, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694983, "dur": 3, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149694987, "dur": 16, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695004, "dur": 3, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695008, "dur": 14, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695024, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695028, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695045, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695050, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695072, "dur": 3, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695076, "dur": 17, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695095, "dur": 3, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695098, "dur": 28, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695128, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695132, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695149, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695153, "dur": 16, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695171, "dur": 2, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695174, "dur": 18, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695194, "dur": 3, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695198, "dur": 14, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695214, "dur": 3, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695218, "dur": 18, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695238, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695242, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695262, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695265, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695285, "dur": 3, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695289, "dur": 27, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695321, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695351, "dur": 3, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695355, "dur": 17, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695374, "dur": 3, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695378, "dur": 24, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695407, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695427, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695431, "dur": 18, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695451, "dur": 3, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695455, "dur": 24, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695485, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695507, "dur": 15, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695523, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695527, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695548, "dur": 3, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695552, "dur": 15, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695568, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695572, "dur": 17, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695591, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695595, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695615, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695619, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695639, "dur": 3, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695643, "dur": 25, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695670, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695675, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695694, "dur": 3, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695698, "dur": 18, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695721, "dur": 27, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695750, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695754, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695773, "dur": 3, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695777, "dur": 18, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695798, "dur": 3, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695801, "dur": 24, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695827, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695831, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695849, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695852, "dur": 19, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695873, "dur": 3, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695877, "dur": 23, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695902, "dur": 3, "ph": "X", "name": "ProcessMessages 921", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695906, "dur": 21, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695929, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695933, "dur": 18, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695954, "dur": 3, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695958, "dur": 17, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695976, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149695980, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696002, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696006, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696025, "dur": 3, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696029, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696049, "dur": 3, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696053, "dur": 18, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696077, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696095, "dur": 3, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696099, "dur": 22, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696123, "dur": 3, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696127, "dur": 20, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696149, "dur": 3, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696153, "dur": 24, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696179, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696183, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696206, "dur": 84, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696293, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696304, "dur": 235, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696541, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696564, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696567, "dur": 25, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696596, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696612, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696614, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696627, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696644, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696646, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696665, "dur": 10, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696677, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696689, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696709, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696740, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696767, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696783, "dur": 14, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696800, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696814, "dur": 13, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696830, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696848, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696849, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696868, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696869, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696885, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696887, "dur": 12, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696901, "dur": 14, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696918, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696935, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696936, "dur": 16, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696954, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696956, "dur": 13, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696972, "dur": 15, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149696990, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697004, "dur": 10, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697016, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697034, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697036, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697056, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697057, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697072, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697088, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697103, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697105, "dur": 15, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697122, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697124, "dur": 16, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697141, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697143, "dur": 10, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697155, "dur": 9, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697166, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697176, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697189, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697203, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697216, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697230, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697250, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697263, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697276, "dur": 9, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697287, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697299, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697310, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697322, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697325, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697337, "dur": 11, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697350, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697352, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697370, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697372, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697386, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697387, "dur": 15, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697404, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697406, "dur": 11, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697419, "dur": 11, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697433, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697453, "dur": 13, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697467, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697469, "dur": 10, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697482, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697496, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697507, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697529, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697542, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697552, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697570, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697583, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697597, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697610, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697624, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697626, "dur": 9, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697638, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697656, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697657, "dur": 9, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697669, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697680, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697692, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697714, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697749, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697750, "dur": 13, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697766, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697789, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697799, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697808, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697823, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697842, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697863, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697883, "dur": 20, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697907, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697929, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697946, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697948, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697962, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697980, "dur": 15, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149697998, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149698010, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149698027, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149698041, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149698149, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149698169, "dur": 276, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149698449, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149698462, "dur": 1819, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149700287, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149700304, "dur": 7090, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149707410, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149707416, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149707475, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149707479, "dur": 69, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149707553, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149707567, "dur": 400, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149707972, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708003, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708040, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708063, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708065, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708223, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708244, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708302, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708314, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708331, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708373, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708386, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708397, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708446, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149708458, "dur": 1075, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709536, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709538, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709570, "dur": 3, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709574, "dur": 50, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709629, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709641, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709656, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709665, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709676, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709684, "dur": 98, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709784, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709792, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709814, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709827, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709839, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709852, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709862, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709879, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149709893, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710072, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710080, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710096, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710109, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710120, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710127, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710150, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710162, "dur": 10, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710173, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710236, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710253, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710268, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710279, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710290, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710308, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710319, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710330, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710350, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710367, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710379, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710395, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710403, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710621, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710635, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710646, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710657, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710771, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710779, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710798, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710812, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710824, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710880, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710895, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710917, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710931, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710962, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710982, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149710993, "dur": 1402, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712404, "dur": 86, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712495, "dur": 3, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712499, "dur": 15, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712515, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712517, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712568, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712594, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712596, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712626, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712763, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712797, "dur": 197, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149712997, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713022, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713024, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713074, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713101, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713122, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713175, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713196, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713282, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713311, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713330, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713461, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713484, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713510, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713523, "dur": 109, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713635, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713658, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713686, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713706, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713833, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713856, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713904, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713925, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713970, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149713990, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149714067, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149714069, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149714090, "dur": 459, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149714553, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149714575, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149714577, "dur": 136754, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149851341, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149851345, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149851391, "dur": 2610, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149854011, "dur": 1524, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149855540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149855542, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149855573, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149855576, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149855633, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149855645, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149855726, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149855752, "dur": 307, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149856061, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149856079, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149856213, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149856229, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149856486, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149856504, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149856585, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149856604, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149856741, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149856755, "dur": 315, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149857077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149857080, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149857100, "dur": 1033, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149858136, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149858149, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149858195, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149858215, "dur": 333, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149858552, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149858572, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149858716, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149858730, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149858797, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149858808, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859058, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859072, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859092, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859103, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859353, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859369, "dur": 333, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859705, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859720, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859784, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859797, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859887, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149859897, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149860041, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149860053, "dur": 498, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149860553, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149860566, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149860614, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149860630, "dur": 415, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861046, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861056, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861110, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861123, "dur": 290, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861415, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861427, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861452, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861465, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861482, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861495, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861517, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861530, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861578, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861592, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861605, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861616, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149861641, "dur": 1511, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863157, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863159, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863189, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863203, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863220, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863237, "dur": 10, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863250, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863259, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863270, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863283, "dur": 11, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863297, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863299, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863314, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863327, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863341, "dur": 14, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863357, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863359, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863376, "dur": 10, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863390, "dur": 11, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863403, "dur": 10, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863414, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863429, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863443, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863456, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863472, "dur": 10, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863483, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863499, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863520, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863522, "dur": 12, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863536, "dur": 14, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863554, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863570, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863702, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863724, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863742, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863757, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863778, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863853, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863878, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863893, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863907, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863975, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863997, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149863999, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149864032, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149864034, "dur": 41532, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149905572, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149905575, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149905603, "dur": 3211, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149908819, "dur": 31968, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149940792, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149940795, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149940837, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617149940841, "dur": 106819, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150047680, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150047687, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150047726, "dur": 48, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150047775, "dur": 7264, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150055045, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150055049, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150055096, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150055101, "dur": 1235, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150056345, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150056347, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150056386, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150056408, "dur": 35135, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150091552, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150091557, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150091595, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150091601, "dur": 708, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150092318, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150092323, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150092352, "dur": 41, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150092394, "dur": 733, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150093133, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150093137, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150093157, "dur": 496, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617150093655, "dur": 7283, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26084, "tid": 699, "ts": 1749617150113249, "dur": 1623, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26084, "tid": 8589934592, "ts": 1749617149645038, "dur": 47747, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": 1749617149692786, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": 1749617149692791, "dur": 751, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26084, "tid": 699, "ts": 1749617150114874, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26084, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26084, "tid": 4294967296, "ts": 1749617149601129, "dur": 500793, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749617149603679, "dur": 35480, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749617150101938, "dur": 3918, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749617150104242, "dur": 108, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749617150105934, "dur": 72, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26084, "tid": 699, "ts": 1749617150114880, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749617149678908, "dur": 1598, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617149680516, "dur": 693, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617149681358, "dur": 78, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749617149681437, "dur": 539, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617149682841, "dur": 1025, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_3AF52CA4203FDAC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749617149684848, "dur": 964, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749617149687222, "dur": 149, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749617149681996, "dur": 14966, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617149696973, "dur": 396123, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617150093101, "dur": 404, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617150093559, "dur": 55, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617150093763, "dur": 67, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617150093881, "dur": 1066, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749617149682157, "dur": 14829, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149697013, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149697173, "dur": 666, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1749617149697157, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_2A2DD78CDAF18E7D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749617149697926, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749617149698241, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749617149698392, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149700561, "dur": 3071, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617149698750, "dur": 4896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149703647, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149703782, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149703928, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149704105, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149704326, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149705689, "dur": 1727, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\PickingTool.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749617149705160, "dur": 3101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149708262, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149708768, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149709171, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749617149709227, "dur": 1159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149710386, "dur": 261, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749617149710878, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617149710647, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749617149711419, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149711754, "dur": 1556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149713310, "dur": 141625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149855688, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.ClientFactory.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617149856963, "dur": 627, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617149854938, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749617149857633, "dur": 2210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617149860471, "dur": 682, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617149861325, "dur": 965, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Razor.dll"}}, {"pid": 12345, "tid": 1, "ts": ****************, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Rewrite.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617149863265, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617149859849, "dur": 3759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749617149863628, "dur": 372, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749617149864109, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749617149864320, "dur": 228812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617149682236, "dur": 14771, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617149697026, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617149697179, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1749617149697010, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_094A556A01D249DF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749617149697471, "dur": 478, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617149697459, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_6C566B45F0C804AD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749617149697967, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749617149698373, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617149698566, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617149698775, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617149698997, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617149699210, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617149699332, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617149699735, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617149700042, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149700181, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149700391, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogScope.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149700671, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149700903, "dur": 339, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\ConditionalIgnoreAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149701265, "dur": 2349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149703616, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149704067, "dur": 436, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableApplyChangesToContextCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149704504, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRepeatedTestCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149704631, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableSetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149704695, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149704829, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\SetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149705013, "dur": 363, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\CategoryFilterExtended.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149705377, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\FullNameFilter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149705463, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149705612, "dur": 493, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ITestSuiteModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149706106, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\OrderedTestSuiteModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149706214, "dur": 1288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149707504, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CoroutineTestWorkItem.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149707713, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617149698062, "dur": 10005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149708206, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749617149708326, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149708818, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149709156, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617149709076, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149709698, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749617149709808, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149710244, "dur": 788, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617149711039, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1749617149711441, "dur": 91, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617149711773, "dur": 140321, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1749617149854505, "dur": 1740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149856294, "dur": 1474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149857769, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617149857845, "dur": 1199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149859044, "dur": 1617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617149860667, "dur": 1174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149861887, "dur": 1256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149863143, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617149863233, "dur": 763, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749617149864159, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617149864311, "dur": 228836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149682157, "dur": 14823, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149696985, "dur": 3864, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149701229, "dur": 2236, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1.1.8\\Editor\\Tasks\\PixelBlends.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749617149703466, "dur": 643, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1.1.8\\Editor\\Tasks\\ImportMergedLayers.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749617149700850, "dur": 3388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149704238, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149705689, "dur": 1957, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int4x3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749617149705284, "dur": 2865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149708221, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149708770, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149709170, "dur": 1212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749617149710392, "dur": 494, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749617149710914, "dur": 288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617149710887, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749617149711632, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149711737, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149711845, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749617149712215, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149712549, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149713306, "dur": 141884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149855905, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617149855199, "dur": 1444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749617149856674, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149857648, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617149857923, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\clretwrc.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617149858315, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.FileExtensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617149856998, "dur": 2270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749617149859268, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149859659, "dur": 1045, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617149861141, "dur": 327, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-multibyte-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617149859491, "dur": 2829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749617149862400, "dur": 1521, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617149863928, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149864050, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617149864306, "dur": 228837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149682186, "dur": 14810, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149697206, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1749617149697159, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_88B56BCE4990CA03.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617149697379, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749617149697378, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_E7FC2534E5076132.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617149697480, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149697719, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149697914, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749617149698499, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13445476680238899994.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749617149698664, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6210570011482043853.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749617149698810, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149699917, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149700216, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149700424, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149701295, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149702312, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector2\\Vector2Maximum.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749617149702124, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149702961, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149703182, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149703412, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149703644, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149703862, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149704116, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149704337, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149705437, "dur": 794, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double2x3.gen.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749617149705409, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149706864, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149707616, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149708155, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149708761, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149709165, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617149710461, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749617149710930, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149711113, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149711427, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149711713, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617149711851, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749617149712213, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149712337, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617149712447, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749617149712832, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149713261, "dur": 143282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149856797, "dur": 1198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749617149856549, "dur": 3277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749617149859873, "dur": 2218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749617149862091, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617149862175, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.Common.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Routing.Abstractions.dll"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 228437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ****************, "dur": 14790, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ****************, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617149697003, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617149697171, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617149697171, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_37A60B8D84ADA88C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617149697422, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149697597, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749617149697774, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749617149698222, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149698678, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749617149698817, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149699640, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149700712, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149701572, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149702119, "dur": 765, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Editor\\ImagePacker\\Jobs\\FindTightRectJob.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749617149702885, "dur": 1576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Editor\\ImagePacker\\ImagePackNode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749617149701829, "dur": 3182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149705011, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149705949, "dur": 2000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149707950, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149708160, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149708758, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149709027, "dur": 1400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617149710445, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749617149710858, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149710947, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617149711061, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749617149711568, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149711760, "dur": 1087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149712848, "dur": 934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149713783, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617149713890, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749617149714249, "dur": 140271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149856537, "dur": 287, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617149854527, "dur": 2322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749617149856849, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149857769, "dur": 1549, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617149860161, "dur": 1172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.Uri.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617149861369, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617149857366, "dur": 4768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749617149862134, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617149864056, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617149862227, "dur": 2249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749617149864536, "dur": 228582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149682268, "dur": 14746, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149697032, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749617149697016, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_699CA9E1D209A267.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617149697169, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749617149697168, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_3787B1E93F84ED7A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617149697467, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749617149697466, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_20B214415B84035D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617149697535, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149697693, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149697826, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149697892, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149698253, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149698745, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149700147, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149700392, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149701425, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149702291, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics\\TriggerEventUnit.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749617149702210, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149703049, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149703258, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149703620, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149703838, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149704098, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149704336, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149705197, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149706255, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149706865, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149707903, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149708164, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149708778, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149709163, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617149709367, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149709633, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617149710273, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617149710458, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617149711010, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149711131, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149711423, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149711715, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617149711819, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617149711919, "dur": 1269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617149713189, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149713281, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617149713374, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617149713966, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617149714068, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617149714457, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617149714613, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617149714673, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617149714841, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617149715337, "dur": 191037, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617149907414, "dur": 31527, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749617149907411, "dur": 32554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749617149941370, "dur": 159, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617149941556, "dur": 106830, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749617150055495, "dur": 36716, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749617150055492, "dur": 36722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749617150092257, "dur": 763, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749617149682296, "dur": 14724, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149697088, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1749617149697023, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21B473704285E949.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617149697351, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_B7F0DFA76BCD38A9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617149697497, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149697916, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749617149698501, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3043913547040211301.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749617149698661, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5521740788080646575.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749617149698814, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149699879, "dur": 792, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Discovery.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749617149699653, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149700916, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149702135, "dur": 959, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Editor\\SkinningModule\\IMGUI\\WeightInspector.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749617149701319, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149703094, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149703575, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149704267, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149705035, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149705652, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149706924, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149707945, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149708167, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149708756, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149709035, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617149709178, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749617149709724, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149710329, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_436C0E2610862891.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617149710408, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617149710639, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749617149711065, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749617149710566, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749617149711361, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149711603, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149711765, "dur": 1036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149712802, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617149712925, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749617149713263, "dur": 142542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149855808, "dur": 2000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749617149857810, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149859312, "dur": 856, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749617149860693, "dur": 455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749617149857870, "dur": 3282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749617149861152, "dur": 1213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617149862427, "dur": 1500, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749617149864291, "dur": 191207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617150055503, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749617150055501, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749617150055721, "dur": 1383, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749617150057110, "dur": 35983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149682327, "dur": 14700, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149697030, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617149697164, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617149697162, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_EF9DC8C923ADA8E7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617149697358, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BC5EF78C5461B6C8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617149697503, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149697810, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749617149697967, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617149698353, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617149698995, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617149699224, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617149698235, "dur": 2774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617149701076, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149701639, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149701933, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149702310, "dur": 595, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarMaximum.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749617149702130, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149702955, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149703181, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149703416, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149703651, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149704163, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149704381, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149705403, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149706442, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149707140, "dur": 1017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149708157, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149708757, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149709028, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617149709255, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149709463, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617149709854, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149709912, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149710228, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617149710638, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617149710438, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617149710952, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149711176, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149711436, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149711731, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149711872, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149712743, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617149712856, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617149713237, "dur": 143314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617149857768, "dur": 569, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617149856554, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749617149860352, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617149858963, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749617149861484, "dur": 258, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617149861388, "dur": 2490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749617149863910, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749617149864301, "dur": 228789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149682358, "dur": 14676, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149697037, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0998A984E55471CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617149697212, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149697211, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_9DDD8969E101654D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617149697473, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149697471, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_425C305CB5B8A325.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617149697760, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749617149697974, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149698347, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149698739, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149699384, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149699987, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149700255, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149700847, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149701541, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149702303, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Path\\Editor\\IMGUI\\IDrawer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749617149702876, "dur": 784, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Path\\Editor\\IMGUI\\GUIFramework\\SliderAction.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749617149701815, "dur": 2104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149703919, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149704175, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149704397, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149705704, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149706077, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149706617, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149707175, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149708156, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149708761, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149709023, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617149709223, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749617149709680, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617149709802, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749617149710258, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149710934, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149711108, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149711430, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149711712, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617149711832, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749617149712272, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149712340, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149712782, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749617149712987, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149713267, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617149713367, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749617149713841, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749617149714063, "dur": 140686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149855969, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-debug-l1-1-0.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149856082, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-errorhandling-l1-1-0.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149856538, "dur": 1362, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149854752, "dur": 4525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749617149859334, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149861036, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149861142, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149861488, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149861692, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149859332, "dur": 2941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749617149862274, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617149862810, "dur": 1133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749617149864053, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617149864303, "dur": 228784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149682385, "dur": 14655, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149697043, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5AA67F5B53C44BAD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749617149697171, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617149697170, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_AC9CD2324DD83C0A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749617149697471, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149697711, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149697773, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749617149697943, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749617149698171, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149698404, "dur": 313, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749617149698751, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149699428, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149699959, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149700106, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149700316, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149701011, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149701207, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149701696, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149701842, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149702091, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149702305, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Lifecycle\\FixedUpdate.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749617149702232, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149703090, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149703575, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149703890, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149704043, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149704227, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149704685, "dur": 665, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\FindWorkspace.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749617149705962, "dur": 1008, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Configuration\\WriteLogConfiguration.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749617149704583, "dur": 2632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149707215, "dur": 947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149708162, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149708766, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149709164, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749617149709339, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749617149709633, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149710181, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149710940, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149711136, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149711455, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149711917, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149712926, "dur": 144736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149857831, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617149857895, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617149858316, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617149858566, "dur": 1052, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617149857664, "dur": 2845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749617149860509, "dur": 1309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149861879, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617149862163, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617149862305, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617149861835, "dur": 2345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749617149864181, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149864284, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617149864533, "dur": 228589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149682428, "dur": 14625, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149697099, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1749617149697056, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_006DC43AA5DD858A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749617149697476, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1749617149697464, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2A9C520E2391C187.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749617149697599, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749617149697845, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149697966, "dur": 694, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149698660, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749617149698785, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149699480, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149699962, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149700111, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149702063, "dur": 817, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.34\\Rider\\Editor\\Debugger\\Il2CppDebugSupport.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749617149700387, "dur": 2493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149702881, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149703020, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149703595, "dur": 919, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\UnsafeAppendBuffer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749617149703594, "dur": 2686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149706281, "dur": 932, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\CommandLineTest\\SettingsBuilder.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749617149706281, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149707964, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149708154, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149708772, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149709024, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749617149709097, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749617149709155, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149709315, "dur": 561, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617149709242, "dur": 1025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749617149710267, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149710370, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749617149710637, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149710937, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149711137, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149711449, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149711918, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149712922, "dur": 141581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149854580, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617149856050, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617149856538, "dur": 1185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617149854511, "dur": 3671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749617149858182, "dur": 2372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149860696, "dur": 453, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617149860559, "dur": 1606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749617149862165, "dur": 901, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149863066, "dur": 887, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749617149863958, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149864285, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617149864646, "dur": 228474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149682449, "dur": 14612, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149697064, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7027E12F917228B0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749617149697166, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149697340, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_9CA00EB8472AEF4E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749617149697489, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149697905, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749617149698122, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149698675, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749617149698931, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149699910, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149700149, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149700311, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149701013, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149701428, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149701539, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149701680, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149701938, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149702305, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Generic\\GenericSubtract.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749617149702160, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149703247, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149703541, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149703815, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149704037, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149704221, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149704766, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149705216, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149706355, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149706561, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149707308, "dur": 843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149708219, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149708773, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149709022, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749617149709158, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617149709151, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749617149709505, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149709829, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749617149709899, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749617149710174, "dur": 776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149710950, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149711117, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149711455, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149711913, "dur": 1017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149712930, "dur": 143881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149857710, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617149858316, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617149856818, "dur": 1978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749617149858796, "dur": 777, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149859657, "dur": 821, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617149859579, "dur": 2272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749617149861852, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149862266, "dur": 1885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749617149864167, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149864289, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617149864782, "dur": 228316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149682469, "dur": 14711, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149697196, "dur": 367, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617149697183, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C06C6D7BC8ACCF56.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749617149697711, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149697784, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749617149697869, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149698398, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749617149698674, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149698777, "dur": 1826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149701502, "dur": 569, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDImporterDataProvider.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749617149700603, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149702304, "dur": 560, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Graph\\GetGraphs.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749617149702180, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149703027, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149703167, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149703307, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149703833, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149704053, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149704461, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149706144, "dur": 1776, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Runtime\\Intrinsics\\x86\\Avx2.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749617149705512, "dur": 2682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149708205, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149708762, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149709223, "dur": 983, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149710334, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617149710207, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749617149710720, "dur": 937, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149711741, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149711994, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749617149712070, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749617149712411, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749617149712505, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749617149713333, "dur": 141572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149854909, "dur": 1465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749617149856964, "dur": 836, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617149858083, "dur": 238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordaccore.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617149858322, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordaccore_amd64_amd64_6.0.1322.58009.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617149859313, "dur": 1188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617149860696, "dur": 450, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617149861324, "dur": 1000, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617149856414, "dur": 5931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749617149862346, "dur": 1452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617149863798, "dur": 374, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749617149864333, "dur": 228796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149682492, "dur": 14693, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149697193, "dur": 686, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617149697187, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E7683A8498EB54AE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749617149697991, "dur": 797, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149698803, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149699775, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149700407, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149700921, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149701163, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149701818, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149702301, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnDrag.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749617149702269, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149703083, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149703303, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149703867, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149704303, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149705486, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149706309, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149706712, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149707208, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149708213, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149708755, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149709177, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749617149709358, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749617149709798, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149710264, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149711195, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149711459, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149711746, "dur": 1569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149713315, "dur": 141440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149856537, "dur": 1198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617149858009, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617149854759, "dur": 3369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749617149858129, "dur": 1698, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149860748, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617149861146, "dur": 1144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617149862301, "dur": 418, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Transactions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617149859832, "dur": 3284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749617149863149, "dur": 833, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749617149863986, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149864288, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617149864676, "dur": 228428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149682508, "dur": 14682, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149697352, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1487261004515A9D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749617149697498, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149698032, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749617149698249, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149698303, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749617149698494, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749617149698683, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8336928665352126947.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749617149699004, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149699669, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.OAuth.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617149700183, "dur": 1181, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617149699224, "dur": 2396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149702069, "dur": 1395, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\VariableUnitOption.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749617149701620, "dur": 1984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149703604, "dur": 817, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\HeapString.gen.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749617149705525, "dur": 2277, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\BitField.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749617149703604, "dur": 4380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149707984, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149708215, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149708775, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149709022, "dur": 1360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749617149710394, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749617149710658, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749617149711793, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749617149712243, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Input\\OnMouseEnter.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749617149711860, "dur": 1109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749617149713047, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749617149713143, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749617149713552, "dur": 140977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149854533, "dur": 1532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749617149856066, "dur": 742, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617149857769, "dur": 1678, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617149859657, "dur": 1679, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617149861662, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617149861792, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617149856836, "dur": 5357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749617149863195, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\aspnetcorev2_inprocess.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617149863537, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.StaticFiles.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617149863851, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617149863992, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617149862229, "dur": 2364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749617149864632, "dur": 228475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149682544, "dur": 14651, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149697210, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617149697198, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_6A2BF8C19DF87A5F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749617149697471, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149697709, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149698397, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149698748, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149700499, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Testing\\TestStatusAdaptor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749617149701360, "dur": 947, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Testing\\TestResultAdaptor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749617149702307, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Testing\\TestAdaptor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749617149702878, "dur": 772, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Symbols.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749617149703650, "dur": 817, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\SolutionProperties.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749617149699520, "dur": 5024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149705613, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Gluon\\Errors\\ErrorsPanel.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749617149704545, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149706209, "dur": 1044, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRun\\Tasks\\LegacyPlayerRunTask.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749617149706209, "dur": 1964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149708209, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149708757, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149709164, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749617149709711, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749617149709809, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749617149710119, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749617149710334, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617149710188, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749617149710603, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149710922, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149711177, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149711432, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149711925, "dur": 984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149712909, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149713969, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749617149714078, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749617149714293, "dur": 140221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149855569, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617149855863, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscorlib.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617149856022, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.DiagnosticSource.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617149856963, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617149854517, "dur": 2715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749617149857647, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617149857262, "dur": 1592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749617149859323, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617149860470, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617149858909, "dur": 1895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749617149860804, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149862296, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617149861336, "dur": 1664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749617149863021, "dur": 926, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749617149863983, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617149864312, "dur": 228812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149682571, "dur": 14631, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149697358, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_270C5DEE22A99EDB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749617149697508, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617149697507, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749617149697813, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1749617149698212, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749617149698494, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749617149698645, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749617149698804, "dur": 1429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149700233, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149700506, "dur": 1211, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@3.1.3\\Editor\\Tiles\\RuleTile\\RuleTileEditor.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749617149702274, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@3.1.3\\Editor\\Tiles\\HexagonalRuleTile\\HexagonalRuleTileEditor.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749617149702886, "dur": 783, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@3.1.3\\Editor\\Tiles\\AnimatedTile\\AnimatedTileEditor.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749617149700420, "dur": 3911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149705986, "dur": 1272, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Diff\\Dialogs\\GetRestorePathDialog.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749617149704331, "dur": 2950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149707281, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149708158, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149708768, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149709162, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749617149709385, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149709665, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749617149710213, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149710275, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149710907, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749617149711018, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749617149711563, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149711912, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149712934, "dur": 144060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149857372, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617149857546, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617149857711, "dur": 613, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617149858566, "dur": 930, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Razor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617149856996, "dur": 3601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749617149860598, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149861324, "dur": 982, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617149862412, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617149862654, "dur": 418, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Core.Api.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617149860820, "dur": 2956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749617149863788, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749617149864027, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617149864294, "dur": 228797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149682597, "dur": 14610, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149697436, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149697757, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749617149697908, "dur": 772, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149698680, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1749617149698929, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149699936, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149700394, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149700871, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149701357, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149701791, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149702111, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149702312, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnScrollbarValueChanged.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749617149702249, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149703001, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149703182, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149703311, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149703472, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149703607, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149703855, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149703999, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149704167, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149704432, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149705039, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149705460, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149706251, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149706815, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149707152, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149708162, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149708768, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149709032, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749617149709105, "dur": 703, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149709811, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749617149710286, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617149710082, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749617149710876, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749617149711112, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617149711073, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749617149711433, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149711922, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149712914, "dur": 1703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149714618, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749617149714743, "dur": 142913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149859339, "dur": 345, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617149857658, "dur": 2055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749617149859714, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149860470, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617149861141, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617149861219, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617149860124, "dur": 1728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749617149861853, "dur": 1217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149863070, "dur": 904, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749617149863978, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617149864304, "dur": 228834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149682617, "dur": 14603, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149697339, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_264A36B967002A74.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749617149697464, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149697933, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1749617149698348, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149698496, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749617149698755, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149699812, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149699946, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149700092, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149700269, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149700632, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149701186, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149701607, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149701755, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149701900, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149702026, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149702160, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149702311, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Application\\OnApplicationFocus.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749617149702311, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149703041, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149703178, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149703315, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149703463, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149703676, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149703908, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149704148, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149704361, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149704911, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149705705, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149705866, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149705944, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149706135, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149706680, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149707147, "dur": 1018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149708165, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149708767, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149709165, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617149709163, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749617149709626, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149710188, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149710933, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149711138, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149711442, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149711921, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149712918, "dur": 141592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149854569, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617149855244, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617149855670, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617149856543, "dur": 438, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617149854512, "dur": 2981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749617149857711, "dur": 353, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617149858068, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617149857520, "dur": 2122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749617149859642, "dur": 837, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149860696, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617149861140, "dur": 1166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617149862413, "dur": 673, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.Policy.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617149860483, "dur": 3292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749617149863775, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149864226, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617149864476, "dur": 228651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149682655, "dur": 14842, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149698057, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749617149698229, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749617149698318, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149698497, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749617149698651, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749617149699648, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149700480, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149698791, "dur": 2210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149701001, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149702005, "dur": 1622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Editor\\SkinningModule\\SkinningCache\\SkinningEnums.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749617149701228, "dur": 2952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149704181, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149706429, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@3.1.3\\Runtime\\GridInformation\\GridInformation.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749617149707165, "dur": 643, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteDragHandler.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749617149704968, "dur": 2921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149707890, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149708172, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149708773, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149709019, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749617149709217, "dur": 1055, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149710336, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149710878, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double3.gen.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749617149710277, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749617149711100, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149711766, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749617149711991, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749617149712050, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749617149712328, "dur": 980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149713308, "dur": 141720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617149855031, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749617149856537, "dur": 1793, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149858516, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149859303, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149859439, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149860190, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventSource.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149861140, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149861547, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149861756, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149856501, "dur": 5640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749617149862284, "dur": 882, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149863963, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617149862199, "dur": 2503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749617149864756, "dur": 228345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617150098625, "dur": 2678, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26084, "tid": 699, "ts": 1749617150115383, "dur": 2691, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26084, "tid": 699, "ts": 1749617150118117, "dur": 2557, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26084, "tid": 699, "ts": 1749617150111063, "dur": 10317, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}