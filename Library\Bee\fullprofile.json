{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26084, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26084, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26084, "tid": 9, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26084, "tid": 9, "ts": 1749607532379129, "dur": 12, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26084, "tid": 9, "ts": 1749607532379154, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26084, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26084, "tid": 1, "ts": 1749607532098040, "dur": 1131, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749607532099173, "dur": 15114, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749607532114289, "dur": 23593, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26084, "tid": 9, "ts": 1749607532379161, "dur": 10, "ph": "X", "name": "", "args": {}}, {"pid": 26084, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532098013, "dur": 26270, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532124284, "dur": 254292, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532124293, "dur": 25, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532124321, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532124446, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532124473, "dur": 4, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532124478, "dur": 2136, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126618, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126664, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126666, "dur": 33, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126703, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126705, "dur": 28, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126735, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126740, "dur": 32, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126779, "dur": 1, "ph": "X", "name": "ProcessMessages 1249", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126782, "dur": 36, "ph": "X", "name": "ReadAsync 1249", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126820, "dur": 1, "ph": "X", "name": "ProcessMessages 1422", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126823, "dur": 27, "ph": "X", "name": "ReadAsync 1422", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126854, "dur": 56, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126912, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126950, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126952, "dur": 25, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126979, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532126980, "dur": 31, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532127013, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532127015, "dur": 27, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532127045, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532127047, "dur": 25, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532127075, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532127077, "dur": 28, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532127108, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532127110, "dur": 29, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532127143, "dur": 1, "ph": "X", "name": "ProcessMessages 1032", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128460, "dur": 162, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128623, "dur": 8, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128632, "dur": 26, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128660, "dur": 4, "ph": "X", "name": "ProcessMessages 1426", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128665, "dur": 27, "ph": "X", "name": "ReadAsync 1426", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128698, "dur": 24, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128729, "dur": 17, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128749, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128753, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128772, "dur": 3, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128776, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128794, "dur": 3, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128798, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128821, "dur": 3, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128826, "dur": 21, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128848, "dur": 3, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128853, "dur": 19, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128874, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128878, "dur": 19, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128899, "dur": 3, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128903, "dur": 20, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128925, "dur": 4, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128929, "dur": 15, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128946, "dur": 3, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128951, "dur": 18, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128971, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128976, "dur": 15, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128992, "dur": 3, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532128996, "dur": 24, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129026, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129048, "dur": 4, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129053, "dur": 21, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129076, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129081, "dur": 398, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129485, "dur": 58, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129545, "dur": 3, "ph": "X", "name": "ProcessMessages 5932", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129548, "dur": 93, "ph": "X", "name": "ReadAsync 5932", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129648, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129666, "dur": 3, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129670, "dur": 19, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129691, "dur": 3, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129696, "dur": 20, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129717, "dur": 3, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129722, "dur": 21, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129745, "dur": 3, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129749, "dur": 19, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129770, "dur": 3, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129775, "dur": 21, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129797, "dur": 3, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129802, "dur": 17, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129820, "dur": 3, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129825, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129848, "dur": 17, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129866, "dur": 3, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129871, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129893, "dur": 3, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129897, "dur": 19, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129918, "dur": 4, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129923, "dur": 16, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129945, "dur": 23, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129970, "dur": 3, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129974, "dur": 21, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532129997, "dur": 3, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130001, "dur": 16, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130023, "dur": 17, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130046, "dur": 45, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130093, "dur": 4, "ph": "X", "name": "ProcessMessages 1558", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130098, "dur": 384, "ph": "X", "name": "ReadAsync 1558", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130489, "dur": 158, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130648, "dur": 4, "ph": "X", "name": "ProcessMessages 10771", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130653, "dur": 34, "ph": "X", "name": "ReadAsync 10771", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130689, "dur": 5, "ph": "X", "name": "ProcessMessages 2236", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130695, "dur": 24, "ph": "X", "name": "ReadAsync 2236", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532130726, "dur": 1354, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132083, "dur": 14, "ph": "X", "name": "ProcessMessages 20513", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132099, "dur": 24, "ph": "X", "name": "ReadAsync 20513", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132125, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132148, "dur": 3, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132153, "dur": 21, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132176, "dur": 3, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132180, "dur": 17, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132203, "dur": 37, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132243, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132272, "dur": 1, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132274, "dur": 21, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132297, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132319, "dur": 4, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132324, "dur": 17, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132343, "dur": 3, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132348, "dur": 47, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132398, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132400, "dur": 40, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132442, "dur": 2, "ph": "X", "name": "ProcessMessages 1528", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132444, "dur": 25, "ph": "X", "name": "ReadAsync 1528", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132474, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132498, "dur": 31, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132532, "dur": 21, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132555, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132557, "dur": 25, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132585, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132587, "dur": 26, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132615, "dur": 3, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132620, "dur": 23, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132644, "dur": 4, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132650, "dur": 25, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132677, "dur": 3, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132681, "dur": 14, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132697, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132715, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132720, "dur": 25, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132747, "dur": 4, "ph": "X", "name": "ProcessMessages 1070", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132752, "dur": 20, "ph": "X", "name": "ReadAsync 1070", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132774, "dur": 4, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132779, "dur": 18, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132798, "dur": 3, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132803, "dur": 18, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132823, "dur": 3, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132827, "dur": 19, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132848, "dur": 3, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132853, "dur": 17, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132872, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132876, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132898, "dur": 20, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132920, "dur": 3, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132924, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132947, "dur": 3, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132951, "dur": 19, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132971, "dur": 3, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132976, "dur": 19, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532132997, "dur": 3, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133001, "dur": 19, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133022, "dur": 3, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133026, "dur": 21, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133050, "dur": 3, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133054, "dur": 16, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133072, "dur": 3, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133077, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133095, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133099, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133120, "dur": 3, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133124, "dur": 19, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133145, "dur": 4, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133149, "dur": 18, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133170, "dur": 3, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133174, "dur": 17, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133193, "dur": 3, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133197, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133218, "dur": 3, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133223, "dur": 20, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133245, "dur": 3, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133249, "dur": 16, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133271, "dur": 13, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133289, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133309, "dur": 3, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133314, "dur": 46, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133363, "dur": 17, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133382, "dur": 4, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133387, "dur": 19, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133408, "dur": 3, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133412, "dur": 18, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133432, "dur": 4, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133437, "dur": 14, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133453, "dur": 3, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133457, "dur": 15, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133478, "dur": 22, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133501, "dur": 4, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133505, "dur": 26, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133534, "dur": 4, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133538, "dur": 16, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133556, "dur": 3, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133560, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133582, "dur": 3, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133586, "dur": 19, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133607, "dur": 3, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133611, "dur": 18, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133631, "dur": 3, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133636, "dur": 16, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133657, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133675, "dur": 3, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133680, "dur": 20, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133702, "dur": 3, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133706, "dur": 19, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133727, "dur": 4, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133732, "dur": 19, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133753, "dur": 3, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133758, "dur": 19, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133779, "dur": 3, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133783, "dur": 18, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133803, "dur": 4, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133807, "dur": 14, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133823, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133827, "dur": 17, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133846, "dur": 3, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133850, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133875, "dur": 3, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133879, "dur": 18, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133899, "dur": 3, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133904, "dur": 16, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133922, "dur": 3, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133926, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133964, "dur": 24, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133990, "dur": 4, "ph": "X", "name": "ProcessMessages 1584", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532133995, "dur": 14, "ph": "X", "name": "ReadAsync 1584", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134011, "dur": 3, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134015, "dur": 16, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134037, "dur": 20, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134058, "dur": 3, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134063, "dur": 42, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134107, "dur": 3, "ph": "X", "name": "ProcessMessages 1147", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134111, "dur": 22, "ph": "X", "name": "ReadAsync 1147", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134135, "dur": 3, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134139, "dur": 18, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134159, "dur": 3, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134164, "dur": 18, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134183, "dur": 3, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134188, "dur": 17, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134208, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134211, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134239, "dur": 4, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134244, "dur": 19, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134269, "dur": 18, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134288, "dur": 3, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134293, "dur": 19, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134314, "dur": 3, "ph": "X", "name": "ProcessMessages 1093", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134318, "dur": 19, "ph": "X", "name": "ReadAsync 1093", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134339, "dur": 3, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134344, "dur": 17, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134362, "dur": 3, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134367, "dur": 16, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134384, "dur": 3, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134389, "dur": 18, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134409, "dur": 3, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134413, "dur": 22, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134437, "dur": 4, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134441, "dur": 25, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134468, "dur": 3, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134473, "dur": 19, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134494, "dur": 3, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134498, "dur": 20, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134520, "dur": 3, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134525, "dur": 16, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134543, "dur": 3, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134547, "dur": 41, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134594, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134615, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134637, "dur": 3, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134641, "dur": 19, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134662, "dur": 3, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134666, "dur": 48, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134716, "dur": 4, "ph": "X", "name": "ProcessMessages 1323", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134721, "dur": 19, "ph": "X", "name": "ReadAsync 1323", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134744, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134746, "dur": 20, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134768, "dur": 3, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134773, "dur": 15, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134794, "dur": 32, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134832, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134854, "dur": 3, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134858, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134878, "dur": 3, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134882, "dur": 16, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134900, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134904, "dur": 12, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134922, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134944, "dur": 3, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134948, "dur": 17, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134967, "dur": 3, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532134972, "dur": 31, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135008, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135028, "dur": 3, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135032, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135054, "dur": 3, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135059, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135089, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135109, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135113, "dur": 15, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135131, "dur": 3, "ph": "X", "name": "ProcessMessages 226", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135135, "dur": 16, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135153, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135157, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135189, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135209, "dur": 3, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135213, "dur": 18, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135233, "dur": 3, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135237, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135266, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135286, "dur": 3, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135291, "dur": 18, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135311, "dur": 3, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135315, "dur": 22, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135343, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135365, "dur": 3, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135370, "dur": 18, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135389, "dur": 3, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135394, "dur": 21, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135420, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135454, "dur": 3, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135458, "dur": 15, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135480, "dur": 39, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135524, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135544, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135549, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135571, "dur": 3, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135575, "dur": 20, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135601, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135649, "dur": 4, "ph": "X", "name": "ProcessMessages 1136", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135654, "dur": 13, "ph": "X", "name": "ReadAsync 1136", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135669, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135673, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135694, "dur": 3, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135699, "dur": 17, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135718, "dur": 3, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135722, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135749, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135768, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135773, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135793, "dur": 3, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135797, "dur": 43, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135842, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135846, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135867, "dur": 3, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135872, "dur": 18, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135896, "dur": 20, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135918, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135922, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135943, "dur": 3, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135947, "dur": 17, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135967, "dur": 3, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135971, "dur": 20, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532135997, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136017, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136021, "dur": 17, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136040, "dur": 3, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136045, "dur": 21, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136072, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136098, "dur": 3, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136102, "dur": 14, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136118, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136123, "dur": 17, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136141, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136145, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136171, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136192, "dur": 3, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136196, "dur": 17, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136215, "dur": 3, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136220, "dur": 20, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136245, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136265, "dur": 3, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136270, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136289, "dur": 3, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136294, "dur": 20, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136319, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136339, "dur": 3, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136344, "dur": 21, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136366, "dur": 3, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136370, "dur": 22, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136398, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136425, "dur": 3, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136430, "dur": 16, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136448, "dur": 3, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136452, "dur": 41, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136499, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136519, "dur": 3, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136524, "dur": 17, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136543, "dur": 3, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136547, "dur": 22, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136574, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136595, "dur": 4, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136600, "dur": 18, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136619, "dur": 3, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136624, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136650, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136670, "dur": 3, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136674, "dur": 18, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136695, "dur": 3, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136699, "dur": 19, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136724, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136751, "dur": 4, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136756, "dur": 16, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136774, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136778, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136821, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136842, "dur": 3, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136846, "dur": 18, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136866, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136871, "dur": 20, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136896, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136918, "dur": 3, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136923, "dur": 18, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136943, "dur": 3, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136948, "dur": 16, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136966, "dur": 3, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136970, "dur": 16, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532136992, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137012, "dur": 3, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137016, "dur": 33, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137051, "dur": 4, "ph": "X", "name": "ProcessMessages 1197", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137056, "dur": 14, "ph": "X", "name": "ReadAsync 1197", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137072, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137076, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137099, "dur": 17, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137117, "dur": 3, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137122, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137143, "dur": 14, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137160, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137164, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137184, "dur": 3, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137188, "dur": 18, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137208, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137212, "dur": 19, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137233, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137237, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137283, "dur": 4, "ph": "X", "name": "ProcessMessages 1100", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137288, "dur": 12, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137302, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137322, "dur": 3, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137327, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137347, "dur": 48, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137397, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137401, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137421, "dur": 4, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137426, "dur": 18, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137450, "dur": 19, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137475, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137497, "dur": 3, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137502, "dur": 18, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137522, "dur": 3, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137527, "dur": 18, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137550, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137570, "dur": 3, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137575, "dur": 18, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137595, "dur": 3, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137599, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137624, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137645, "dur": 3, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137649, "dur": 18, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137669, "dur": 3, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137673, "dur": 41, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137721, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137741, "dur": 3, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137746, "dur": 17, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137765, "dur": 3, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137770, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137796, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137816, "dur": 3, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137821, "dur": 19, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137841, "dur": 3, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137846, "dur": 18, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137869, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137889, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137890, "dur": 28, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137920, "dur": 4, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137925, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137949, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532137985, "dur": 18, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138004, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138009, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138030, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138052, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138057, "dur": 16, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138075, "dur": 3, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138079, "dur": 18, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138100, "dur": 3, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138104, "dur": 23, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138128, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138149, "dur": 3, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138153, "dur": 22, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138177, "dur": 3, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138182, "dur": 19, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138203, "dur": 3, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138207, "dur": 18, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138227, "dur": 3, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138232, "dur": 16, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138249, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138254, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138278, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138296, "dur": 3, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138300, "dur": 16, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138319, "dur": 3, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138323, "dur": 25, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138350, "dur": 3, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138354, "dur": 23, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138380, "dur": 13, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138395, "dur": 19, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138415, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138445, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138473, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138475, "dur": 21, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138499, "dur": 34, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138537, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138565, "dur": 19, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138587, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138588, "dur": 27, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138618, "dur": 13, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138633, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138660, "dur": 15, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138678, "dur": 36, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138719, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138745, "dur": 19, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138765, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138766, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138799, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138838, "dur": 1, "ph": "X", "name": "ProcessMessages 1198", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138839, "dur": 22, "ph": "X", "name": "ReadAsync 1198", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138865, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138888, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138889, "dur": 28, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138920, "dur": 17, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138941, "dur": 45, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532138989, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139021, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139023, "dur": 20, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139045, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139046, "dur": 27, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139075, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139099, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139122, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139123, "dur": 31, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139158, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139191, "dur": 1, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139193, "dur": 24, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139223, "dur": 18, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139243, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139245, "dur": 14, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139261, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139279, "dur": 12, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139292, "dur": 10, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139304, "dur": 10, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139315, "dur": 10, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139326, "dur": 11, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139339, "dur": 10, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139350, "dur": 10, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139361, "dur": 9, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139372, "dur": 31, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139404, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139423, "dur": 10, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139434, "dur": 10, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139445, "dur": 10, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139457, "dur": 10, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139469, "dur": 19, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139489, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139501, "dur": 11, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139514, "dur": 9, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139525, "dur": 12, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139539, "dur": 7, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139548, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139577, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139588, "dur": 75, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139667, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139762, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139763, "dur": 226, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139991, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532139993, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532140026, "dur": 6, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532140033, "dur": 1049, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532141087, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532141192, "dur": 21, "ph": "X", "name": "ProcessMessages 4916", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532141216, "dur": 4739, "ph": "X", "name": "ReadAsync 4916", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532145959, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532145961, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532145993, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146074, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146081, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146109, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146111, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146136, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146292, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146322, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146326, "dur": 88, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146417, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146461, "dur": 2, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146465, "dur": 22, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146489, "dur": 2, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146493, "dur": 23, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146519, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532146677, "dur": 435, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532147116, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532147145, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532147152, "dur": 1063, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532148219, "dur": 2799, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532151024, "dur": 4, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532151030, "dur": 104, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532151136, "dur": 39, "ph": "X", "name": "ProcessMessages 1558", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532151177, "dur": 71, "ph": "X", "name": "ReadAsync 1558", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532151252, "dur": 18, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532151272, "dur": 852, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152130, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152175, "dur": 26, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152201, "dur": 21, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152225, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152227, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152290, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152312, "dur": 11, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152325, "dur": 26, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152353, "dur": 10, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152365, "dur": 124, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152493, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152513, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152516, "dur": 56, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152575, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152601, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152629, "dur": 6, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152636, "dur": 49, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152689, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152714, "dur": 238, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152955, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532152977, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532153126, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532153155, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532153157, "dur": 80904, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532234070, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532234074, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532234087, "dur": 18, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532234106, "dur": 12494, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532246620, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532246628, "dur": 966, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532247605, "dur": 189, "ph": "X", "name": "ProcessMessages 1692", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532247797, "dur": 41, "ph": "X", "name": "ReadAsync 1692", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532247847, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532247850, "dur": 201, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532248055, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532248077, "dur": 10, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532248088, "dur": 33, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532248123, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532248138, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532248146, "dur": 9, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532248156, "dur": 2, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532248159, "dur": 215, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532248380, "dur": 3342, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532251841, "dur": 102, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532251948, "dur": 712, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532252672, "dur": 46, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532252720, "dur": 375, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253101, "dur": 265, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253370, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253383, "dur": 40, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253425, "dur": 11, "ph": "X", "name": "ProcessMessages 107", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253438, "dur": 25, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253464, "dur": 32, "ph": "X", "name": "ProcessMessages 51", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253498, "dur": 177, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253677, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253680, "dur": 229, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253912, "dur": 23, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253937, "dur": 24, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253962, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532253966, "dur": 10331, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532264311, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532264316, "dur": 1687, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532266011, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532266016, "dur": 511, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532266532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532266534, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532266581, "dur": 42, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532266625, "dur": 95, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532266724, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532266726, "dur": 262, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532266991, "dur": 16, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532267008, "dur": 7470, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532274494, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532274502, "dur": 650, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275157, "dur": 72, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275230, "dur": 43, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275301, "dur": 50, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275353, "dur": 27, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275384, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275387, "dur": 302, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275696, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275728, "dur": 2, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275731, "dur": 223, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275959, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532275962, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532276068, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532276097, "dur": 12, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532276110, "dur": 21, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532276133, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532276135, "dur": 579, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532276719, "dur": 405, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532277128, "dur": 27, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532277157, "dur": 915, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532278079, "dur": 15, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532278096, "dur": 95, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532278195, "dur": 50, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532278247, "dur": 47, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532278298, "dur": 45, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532278346, "dur": 150, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532278499, "dur": 475, "ph": "X", "name": "ProcessMessages 106", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532278978, "dur": 39, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532279021, "dur": 42, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532279065, "dur": 21, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532279090, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532279093, "dur": 109, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532279205, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532279210, "dur": 668, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532279885, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532279904, "dur": 314, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532280337, "dur": 166, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532280506, "dur": 509, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532281020, "dur": 54, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532281075, "dur": 421, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532281500, "dur": 24, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532281526, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532281571, "dur": 37, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532281611, "dur": 462, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282080, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282106, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282118, "dur": 124, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282246, "dur": 208, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282460, "dur": 11, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282472, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282506, "dur": 17, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282525, "dur": 136, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282666, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282687, "dur": 16, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282704, "dur": 14, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532282720, "dur": 738, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532283465, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532283469, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532283577, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532283597, "dur": 34, "ph": "X", "name": "ProcessMessages 6", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532283633, "dur": 827, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532284468, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532284471, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532284561, "dur": 30, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532284592, "dur": 80636, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532365244, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532365250, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532365282, "dur": 139, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532365422, "dur": 3803, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532369232, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532369235, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532369255, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26084, "tid": 25769803776, "ts": 1749607532369257, "dur": 9306, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26084, "tid": 9, "ts": 1749607532379172, "dur": 2200, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26084, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26084, "tid": 21474836480, "ts": 1749607532097975, "dur": 39906, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26084, "tid": 21474836480, "ts": 1749607532137882, "dur": 14, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26084, "tid": 9, "ts": 1749607532381377, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26084, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26084, "tid": 17179869184, "ts": 1749607532075278, "dur": 303362, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26084, "tid": 17179869184, "ts": 1749607532075349, "dur": 22593, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26084, "tid": 17179869184, "ts": 1749607532378645, "dur": 135, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26084, "tid": 17179869184, "ts": 1749607532378690, "dur": 27, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26084, "tid": 17179869184, "ts": 1749607532378782, "dur": 2, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26084, "tid": 9, "ts": 1749607532381388, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749607532124289, "dur": 1159, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749607532125460, "dur": 682, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749607532126276, "dur": 312, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749607532127304, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_2FD4CD6B4E1CB690.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749607532127858, "dur": 758, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749607532129162, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749607532130223, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1749607532130497, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749607532130950, "dur": 173, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1749607532131604, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1749607532131765, "dur": 317, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749607532126599, "dur": 12988, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749607532139596, "dur": 227392, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749607532366991, "dur": 444, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749607532369096, "dur": 91, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749607532369210, "dur": 1293, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749607532126680, "dur": 12928, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532139976, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749607532139975, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749607532140290, "dur": 363, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749607532141376, "dur": 911, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749607532142288, "dur": 600, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Formats.Asn1.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749607532142888, "dur": 989, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749607532140895, "dur": 3011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532143906, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532144124, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532144441, "dur": 1470, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\PlusHandler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749607532144328, "dur": 1641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532145970, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532146388, "dur": 5139, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749607532151552, "dur": 1066, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749607532152619, "dur": 82823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532235445, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749607532236724, "dur": 9835, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532246570, "dur": 668, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532247337, "dur": 19383, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749607532266725, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532266829, "dur": 7529, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532276708, "dur": 1339, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749607532274364, "dur": 3700, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749607532278068, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749607532280035, "dur": 4424, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749607532284464, "dur": 82543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532126797, "dur": 12835, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532139635, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_699CA9E1D209A267.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749607532139722, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_9E8FB24CB62B009F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749607532139985, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532140444, "dur": 525, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749607532140970, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749607532141107, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532141366, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532141677, "dur": 619, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532142863, "dur": 1062, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\PointerEventData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749607532141078, "dur": 3461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749607532144687, "dur": 1173, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Collections\\AotList.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749607532144577, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532145900, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532146068, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532146453, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532147112, "dur": 1622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749607532148735, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532149186, "dur": 330, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532149632, "dur": 811, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532150584, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532148997, "dur": 1780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749607532150841, "dur": 1116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532151958, "dur": 92670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532244632, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532244922, "dur": 3099, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532248026, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532266311, "dur": 85, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532248337, "dur": 18185, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749607532266525, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532266672, "dur": 64, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532266738, "dur": 7909, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532274650, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532274890, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532277478, "dur": 882, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532274955, "dur": 3514, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532278474, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749607532278598, "dur": 1553, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749607532280176, "dur": 1900, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749607532282078, "dur": 85111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532126678, "dur": 12926, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532139606, "dur": 2487, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532142094, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532142340, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532142581, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532142865, "dur": 2196, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\IUnifiedVariableUnit.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749607532142815, "dur": 2405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532145221, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532145469, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532145692, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532146014, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532146083, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532146477, "dur": 4715, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749607532151193, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532151548, "dur": 745, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749607532152294, "dur": 85278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532237573, "dur": 1949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749607532239523, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532239919, "dur": 1870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749607532241790, "dur": 2904, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532244703, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532244954, "dur": 3388, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749607532248349, "dur": 803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532249159, "dur": 17217, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749607532266382, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532266751, "dur": 7594, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532277337, "dur": 853, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532274355, "dur": 3926, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749607532278286, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532283417, "dur": 129, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749607532279028, "dur": 4534, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749607532283708, "dur": 83308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532126701, "dur": 12915, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532139671, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749607532139663, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7027E12F917228B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749607532139979, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749607532139978, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749607532140378, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532140682, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11932646040550446581.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749607532140802, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749607532140896, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532141752, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532141960, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532142173, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532142407, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532142633, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532142844, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532143211, "dur": 1250, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Codebase\\GetMember.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749607532143082, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532144564, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532144788, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532145013, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532145244, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532145484, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532145699, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532145936, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532146070, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532146444, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532146693, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749607532147098, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749607532146846, "dur": 1334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749607532148180, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532148263, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532148319, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749607532148502, "dur": 1059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749607532149627, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749607532149741, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532150050, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749607532150500, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532150852, "dur": 1540, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749607532152393, "dur": 89761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532243109, "dur": 752, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749607532242155, "dur": 2315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749607532244538, "dur": 3452, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749607532247996, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532248239, "dur": 5546, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749607532271647, "dur": 2955, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532253822, "dur": 21515, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749607532275342, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532277959, "dur": 483, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532275679, "dur": 2775, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/PsdPlugin.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749607532278460, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749607532278539, "dur": 1799, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749607532280339, "dur": 86645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532126736, "dur": 12885, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532139624, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749607532139809, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749607532139808, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_188ECFE2CFFCF3FE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749607532139975, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532140128, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749607532140892, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532141267, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532141597, "dur": 697, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\SettingsProvider\\EditorPreferencesProvider.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749607532141438, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532142347, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532142574, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532143205, "dur": 669, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\IsApplicationVariableDefined.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749607532142791, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532143936, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532144167, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532144438, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\UI\\UnityOnScrollRectValueChangedMessageListener.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749607532144378, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532145218, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532145468, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532145877, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532146078, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532146442, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532146694, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749607532146824, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749607532147106, "dur": 1156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749607532147105, "dur": 1544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749607532148649, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532148734, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749607532148832, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532149098, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749607532149472, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532149631, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749607532149889, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749607532150296, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749607532150626, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749607532150412, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749607532150856, "dur": 776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532151632, "dur": 90552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532243108, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749607532242184, "dur": 1832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749607532244048, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532244158, "dur": 1810, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749607532245980, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532246122, "dur": 3504, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Animation.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749607532249632, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532249889, "dur": 16630, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Aseprite.Common.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749607532266523, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532271012, "dur": 3573, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532266678, "dur": 8626, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749607532275310, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532277847, "dur": 584, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532275669, "dur": 2769, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749607532278444, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749607532278536, "dur": 1611, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749607532280180, "dur": 2510, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749607532282692, "dur": 84311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532126765, "dur": 12862, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532139629, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_094A556A01D249DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749607532139734, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532139975, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532140187, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749607532140291, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532140498, "dur": 441, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749607532140939, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532141804, "dur": 645, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\GlyphInfoDrawer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749607532141787, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532142651, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532142898, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532143671, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532143921, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532144140, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532144362, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532144600, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532144811, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532145038, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532145258, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532145480, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532145695, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532146013, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532146068, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532146415, "dur": 4409, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749607532150854, "dur": 1463, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749607532152318, "dur": 89753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532242508, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749607532242657, "dur": 461, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749607532243854, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749607532242075, "dur": 2476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749607532244630, "dur": 3485, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749607532248119, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532248251, "dur": 18078, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749607532266339, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532270972, "dur": 3528, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532266689, "dur": 7966, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749607532277002, "dur": 1130, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532274706, "dur": 3520, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749607532278235, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749607532278345, "dur": 3830, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749607532282178, "dur": 84808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532126829, "dur": 12809, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532139640, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21B473704285E949.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749607532139821, "dur": 346, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749607532139820, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_858DAC66656EEE20.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749607532140486, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749607532140725, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749607532140899, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532141251, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532141448, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532141686, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532141902, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532142115, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532142365, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532142601, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532142821, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532143703, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532143932, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532144151, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532144442, "dur": 1241, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\ActionDirection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749607532144368, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532145845, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532146081, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532146439, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532146701, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749607532146834, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749607532146929, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532147379, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749607532147838, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532147940, "dur": 789, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532148733, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749607532148846, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749607532149368, "dur": 2103, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749607532151833, "dur": 83617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532236206, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-handle-l1-1-0.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749607532235452, "dur": 2157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749607532237610, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532237685, "dur": 1964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749607532239649, "dur": 623, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532240275, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749607532242163, "dur": 1663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749607532243875, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532244065, "dur": 1907, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749607532245977, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532246125, "dur": 3363, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749607532249549, "dur": 16791, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Aseprite.Common.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749607532266348, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532271331, "dur": 3286, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532266686, "dur": 7938, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749607532274629, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532277211, "dur": 973, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749607532274874, "dur": 3406, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749607532278426, "dur": 4124, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749607532282553, "dur": 84459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532126871, "dur": 12772, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532139646, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749607532139740, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532139925, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532140289, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749607532140740, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7634377931854478575.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749607532141430, "dur": 860, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Numerics.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749607532140891, "dur": 1490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532142381, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532142600, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532142822, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532143591, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532143793, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532143985, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532144220, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532144438, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532144662, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532144872, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532145102, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532145317, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532145524, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532145848, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532146077, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532146443, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532146697, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749607532146837, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749607532147097, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749607532146960, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749607532147537, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532147754, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749607532147855, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532148191, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749607532148317, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749607532148186, "dur": 867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749607532149054, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532149626, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749607532149823, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532149917, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749607532150321, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749607532150625, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749607532150433, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749607532150886, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532151627, "dur": 959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532152587, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749607532152680, "dur": 89462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532242148, "dur": 591, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.Common.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749607532242774, "dur": 302, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749607532242145, "dur": 2534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749607532244725, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532244905, "dur": 3243, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749607532248152, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532248242, "dur": 18091, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749607532266339, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532271438, "dur": 3144, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532266682, "dur": 8610, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749607532275299, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532277681, "dur": 733, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532275726, "dur": 2695, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749607532278466, "dur": 1230, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749607532279700, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749607532280032, "dur": 1495, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-firstpass.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749607532281529, "dur": 85497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532126902, "dur": 12747, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532139748, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749607532139747, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C06C6D7BC8ACCF56.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749607532139965, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532140046, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2A9C520E2391C187.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749607532140675, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532140928, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532141451, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532141893, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532142107, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532142335, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532142480, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532142628, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532142769, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532142982, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532143143, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532143854, "dur": 602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_1_2.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749607532143738, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532144559, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532144795, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532145007, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532145238, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532145526, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532145860, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532146076, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532146443, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532146696, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749607532146829, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749607532147098, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749607532146952, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749607532147634, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749607532147776, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749607532148263, "dur": 819, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532149107, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532149384, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532149636, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749607532149747, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749607532150118, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532150358, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749607532150626, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749607532150472, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749607532150948, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532151589, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749607532151686, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749607532152034, "dur": 90039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532242148, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749607532242219, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749607532242076, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749607532243963, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532244363, "dur": 3949, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749607532248317, "dur": 706, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532249045, "dur": 4597, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749607532253651, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532270652, "dur": 3867, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532253905, "dur": 20752, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749607532276976, "dur": 1136, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749607532274703, "dur": 3419, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749607532278235, "dur": 1377, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749607532279652, "dur": 1866, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749607532281521, "dur": 85513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532126989, "dur": 12666, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532139744, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532139917, "dur": 345, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749607532139917, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_038A17E6D6B77576.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749607532140677, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532140937, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532141504, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532141696, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532141834, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532141978, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532142289, "dur": 2133, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.State\\Plugin\\Changelogs\\Changelog_1_0_2.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749607532142124, "dur": 2298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532144422, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532144570, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532144770, "dur": 1104, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_TextProcessingStack.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749607532144714, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532146012, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532146079, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532146439, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532147104, "dur": 1361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749607532148486, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749607532149186, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\Reflection\\NamespaceInspector.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749607532148611, "dur": 1159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749607532149770, "dur": 1229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749607532151025, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749607532151121, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749607532151676, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749607532151768, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749607532152193, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749607532152289, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749607532152585, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749607532152695, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749607532152952, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749607532153139, "dur": 212043, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749607532127007, "dur": 12663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532139848, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749607532139847, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_42EEE2042AED6643.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749607532140254, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532140426, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1749607532140868, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532140999, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532141609, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532141754, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532141897, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532142057, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532142208, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532142437, "dur": 1431, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\INesterStateTransition.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749607532143869, "dur": 834, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\INesterState.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749607532142437, "dur": 2392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532144829, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532144968, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532145108, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532145253, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532145388, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532145566, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532145698, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532145865, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532146067, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532146448, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532146709, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749607532147097, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749607532146878, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749607532147362, "dur": 611, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532147994, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749607532148392, "dur": 804, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749607532148110, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749607532149336, "dur": 2113, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749607532151838, "dur": 83600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532235440, "dur": 1289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749607532236756, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749607532237954, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532238273, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749607532239541, "dur": 1195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749607532240736, "dur": 1097, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532241836, "dur": 1184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749607532243021, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532243457, "dur": 1181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532244645, "dur": 3321, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749607532247970, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532248232, "dur": 5182, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749607532253418, "dur": 1096, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532274465, "dur": 80, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532254528, "dur": 20761, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749607532275298, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532277708, "dur": 713, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749607532275574, "dur": 2854, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.IK.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749607532278470, "dur": 1666, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749607532280162, "dur": 2361, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749607532282524, "dur": 84492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532127095, "dur": 12583, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532139747, "dur": 529, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1749607532139732, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_88B56BCE4990CA03.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749607532140722, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749607532140879, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532141444, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532141685, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532141909, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532142266, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.State\\Flow\\FlowGraphContextStateExtension.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749607532142129, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532142946, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532143112, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532143853, "dur": 1206, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Reflection\\NamespaceOption.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749607532143690, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532145138, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532145358, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532145569, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532145976, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532146085, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532146438, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532147105, "dur": 448, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749607532147104, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749607532148093, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532148309, "dur": 3165, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749607532151831, "dur": 83609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532235444, "dur": 1172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749607532236617, "dur": 2058, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532238679, "dur": 1261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749607532239941, "dur": 1483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532242066, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749607532241426, "dur": 1218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749607532242718, "dur": 3113, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749607532245835, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532245979, "dur": 2318, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749607532248304, "dur": 830, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532267682, "dur": 6792, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532249141, "dur": 25510, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749607532274694, "dur": 2016, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749607532276742, "dur": 1352, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532278096, "dur": 2121, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749607532282140, "dur": 157, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749607532280336, "dur": 1969, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749607532282308, "dur": 84723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532127115, "dur": 12635, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532139962, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532140474, "dur": 497, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1749607532140971, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532141592, "dur": 1292, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\ControlPlayableUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749607532141456, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532143105, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Input\\IMouseEventUnit.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749607532142960, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532143935, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532144160, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532144369, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532144591, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532144795, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532145042, "dur": 831, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\Avatar\\ApplyCircleMask.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749607532145010, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532145987, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532146083, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532146471, "dur": 5034, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749607532151623, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532151678, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749607532151782, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749607532152191, "dur": 83262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532235454, "dur": 2101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749607532237556, "dur": 616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532238177, "dur": 1977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749607532240179, "dur": 1875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749607532242151, "dur": 1838, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749607532244607, "dur": 1054, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749607532242103, "dur": 4357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749607532246593, "dur": 6842, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749607532253445, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532271375, "dur": 3249, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532253580, "dur": 21053, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749607532276744, "dur": 1358, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749607532274668, "dur": 3442, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749607532278230, "dur": 4005, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749607532282236, "dur": 84925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532127138, "dur": 12616, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532139761, "dur": 648, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749607532139756, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_766B22F663D142F3.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749607532140424, "dur": 528, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_766B22F663D142F3.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749607532140958, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749607532141107, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749607532142266, "dur": 631, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749607532142997, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749607532143201, "dur": 337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749607532143598, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnitySetUpAttribute.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749607532143855, "dur": 1020, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749607532145038, "dur": 821, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestExecutionContext.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749607532141072, "dur": 4962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749607532146116, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749607532146492, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749607532146689, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749607532146740, "dur": 1544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532148317, "dur": 883, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749607532148286, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749607532149624, "dur": 1324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749607532150965, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749607532151364, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532151831, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1749607532152392, "dur": 87, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532152486, "dur": 81552, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1749607532235437, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749607532236730, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532236809, "dur": 1234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749607532238075, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749607532239281, "dur": 787, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532240070, "dur": 1162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749607532241254, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749607532242720, "dur": 3108, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749607532245881, "dur": 3782, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749607532249675, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532249893, "dur": 25094, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749607532274993, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532277369, "dur": 843, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532275275, "dur": 2947, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749607532278256, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749607532278603, "dur": 1596, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749607532280335, "dur": 2191, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749607532282527, "dur": 84566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532127156, "dur": 12603, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532139773, "dur": 667, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749607532139761, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_6A2BF8C19DF87A5F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749607532140485, "dur": 505, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1749607532140991, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532141399, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532141595, "dur": 1287, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\ItemGui\\TimelineMarkerGUI.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749607532141559, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532143030, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532143852, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Utilities\\VersionControlUtility.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749607532143576, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532144498, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532144748, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532145052, "dur": 817, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\Progress\\DrawProgressForOperations.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749607532144971, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532146000, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532146075, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532146451, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532146690, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749607532147084, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749607532146833, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749607532147541, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749607532147655, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749607532148031, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532148176, "dur": 1239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532149490, "dur": 2617, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749607532152108, "dur": 83336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532235453, "dur": 2022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749607532237476, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532237701, "dur": 1881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749607532239607, "dur": 1774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749607532242065, "dur": 1934, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749607532244607, "dur": 382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749607532244991, "dur": 509, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749607532245543, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749607532241413, "dur": 4326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749607532245740, "dur": 675, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532246429, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532246589, "dur": 7047, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749607532253643, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532253838, "dur": 22231, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749607532277959, "dur": 1240, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532276103, "dur": 3104, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749607532279214, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749607532279515, "dur": 1996, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1749607532281513, "dur": 85696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532127214, "dur": 12550, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532139961, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532140791, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11295897478892413411.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749607532140889, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532141648, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532141858, "dur": 1691, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_BaseEditorPanel.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749607532143594, "dur": 1183, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMPro_FontAssetCreatorWindow.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749607532141782, "dur": 3024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532144806, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532144956, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532145123, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532145279, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532145509, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532145659, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532146010, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532146071, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532146447, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532147104, "dur": 314, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749607532147103, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749607532147747, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749607532148197, "dur": 314, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749607532148083, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749607532148690, "dur": 689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532149383, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532149638, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749607532149751, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749607532150200, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532150508, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532150849, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532151655, "dur": 83793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532235449, "dur": 1990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749607532237480, "dur": 1883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749607532239364, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532239665, "dur": 1799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749607532241464, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532242066, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749607532242599, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749607532243108, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749607532241629, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749607532243683, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532243883, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532244250, "dur": 1882, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749607532246136, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532246229, "dur": 6815, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749607532253058, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532253325, "dur": 21067, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749607532277424, "dur": 798, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532274618, "dur": 3613, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749607532278238, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749607532278606, "dur": 1755, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749607532280363, "dur": 86627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532127251, "dur": 12518, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532139847, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749607532139846, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_36BB48A459E34DBF.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749607532139976, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532140154, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532140426, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1749607532140730, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3043913547040211301.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749607532141308, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Pkcs.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749607532140887, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532141935, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532142105, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532142234, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532142464, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532142691, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532142939, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532143173, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532143798, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532143976, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532144128, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532144264, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532144411, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532144557, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532144724, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532144887, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532145061, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532145209, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532145846, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532146076, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532146447, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532147104, "dur": 2092, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749607532147103, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749607532149659, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749607532149779, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749607532149905, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749607532150299, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532150867, "dur": 781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532151648, "dur": 83803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532235453, "dur": 1962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749607532237416, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532237657, "dur": 1860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749607532239548, "dur": 1804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749607532241352, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532242066, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749607532242149, "dur": 515, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-errorhandling-l1-1-0.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749607532242773, "dur": 343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCaching.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749607532241494, "dur": 2240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749607532243787, "dur": 2173, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749607532245979, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532246152, "dur": 3761, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Animation.Editor.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1749607532249920, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532271754, "dur": 2818, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532249982, "dur": 25312, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1749607532275301, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532277499, "dur": 873, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749607532275658, "dur": 2827, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1749607532278608, "dur": 1548, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1749607532280195, "dur": 2180, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1749607532282376, "dur": 84645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532127274, "dur": 12650, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532139970, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532140201, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749607532140264, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532140689, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749607532141610, "dur": 694, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749607532140886, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532142369, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532142522, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532142675, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532142829, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532143056, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532143691, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532143984, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532144205, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532144430, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532144682, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532144946, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532145401, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532145546, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532145720, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532146069, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532146448, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532146832, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749607532147098, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749607532146967, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532147525, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749607532147602, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749607532147670, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749607532147730, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749607532147817, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532148196, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749607532148317, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749607532148101, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532148545, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532149144, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532149297, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749607532149378, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532149625, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749607532149704, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532149972, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532150081, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749607532150145, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532150387, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532151378, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Editor\\SpriteLib\\SpriteLibraryEditor\\SpriteLibraryAssetPostprocessor.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749607532150965, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532151587, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749607532151723, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532152105, "dur": 84632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532236738, "dur": 1180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532237919, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532238089, "dur": 1189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532239300, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532240554, "dur": 1193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749607532241750, "dur": 1411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532243182, "dur": 2829, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.IK.Runtime.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749607532246014, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532246166, "dur": 7203, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749607532253376, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532253792, "dur": 20626, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749607532277367, "dur": 831, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749607532274631, "dur": 3653, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749607532278342, "dur": 1248, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749607532279621, "dur": 1901, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1749607532281524, "dur": 85454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532127293, "dur": 12692, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532139990, "dur": 474, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532139986, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749607532140537, "dur": 528, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749607532141066, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532141402, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532141643, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532141865, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532142262, "dur": 621, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\IK\\Editor\\IKEditorManager.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749607532142079, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532143114, "dur": 761, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Literal.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749607532142909, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532143881, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532144020, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532144177, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532144403, "dur": 1308, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceFunctionInvoker_2.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749607532144325, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532145850, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532146079, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532146451, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532146693, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749607532147098, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532146826, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749607532147604, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532147792, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749607532148197, "dur": 1035, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532147917, "dur": 1420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749607532149406, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532150228, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749607532150632, "dur": 351, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532150388, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749607532151097, "dur": 847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532151944, "dur": 83502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532235448, "dur": 2101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749607532237584, "dur": 1964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749607532239576, "dur": 1804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749607532242013, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532242149, "dur": 633, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532242805, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\aspnetcorev2_inprocess.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532241407, "dur": 2221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749607532243628, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532243721, "dur": 2261, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532245984, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532246157, "dur": 3848, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532271860, "dur": 2702, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532250039, "dur": 25259, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532275304, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749607532275678, "dur": 2382, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532278106, "dur": 2223, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749607532280331, "dur": 86707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532127322, "dur": 12668, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532139998, "dur": 566, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749607532139991, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749607532140639, "dur": 423, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749607532141063, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532141576, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532141790, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532142008, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532142220, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532142520, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532142742, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532142968, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532143162, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532143851, "dur": 1203, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_11.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749607532143770, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532145186, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532145409, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532145590, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532145899, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532146074, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532146452, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532147110, "dur": 1412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749607532148541, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749607532148970, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749607532149187, "dur": 451, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749607532149070, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749607532149934, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532149989, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749607532150348, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532150437, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532150986, "dur": 965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532151951, "dur": 83485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532235438, "dur": 2118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749607532237556, "dur": 4621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532242277, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749607532242748, "dur": 360, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749607532242181, "dur": 2732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749607532244939, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532245444, "dur": 2883, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749607532248331, "dur": 792, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532249133, "dur": 4655, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749607532253825, "dur": 20838, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749607532277198, "dur": 941, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532274791, "dur": 3437, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749607532278236, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532278339, "dur": 1360, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1749607532279704, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749607532280030, "dur": 1413, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1749607532281445, "dur": 85536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749607532374478, "dur": 3519, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26084, "tid": 9, "ts": 1749607532381484, "dur": 6221, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26084, "tid": 9, "ts": 1749607532387791, "dur": 6064, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26084, "tid": 9, "ts": 1749607532379142, "dur": 14782, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}