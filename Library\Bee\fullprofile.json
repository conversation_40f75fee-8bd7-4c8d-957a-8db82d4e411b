{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26084, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26084, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26084, "tid": 279, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26084, "tid": 279, "ts": 1749608488574279, "dur": 7, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26084, "tid": 279, "ts": 1749608488574295, "dur": 2, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26084, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26084, "tid": 1, "ts": 1749608487906082, "dur": 1157, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749608487907242, "dur": 11265, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749608487918510, "dur": 17167, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26084, "tid": 279, "ts": 1749608488574300, "dur": 5, "ph": "X", "name": "", "args": {}}, {"pid": 26084, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487906040, "dur": 28562, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487934604, "dur": 639402, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487934615, "dur": 32, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487934650, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487934751, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487934755, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487934774, "dur": 11, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487934786, "dur": 2010, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487936801, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487936840, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487936843, "dur": 21, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487936867, "dur": 25, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487936894, "dur": 21, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487936918, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487936920, "dur": 29, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487936951, "dur": 1, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487936953, "dur": 23, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487936979, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937004, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937006, "dur": 32, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937042, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937044, "dur": 27, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937074, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937077, "dur": 26, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937106, "dur": 58, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937168, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937208, "dur": 24, "ph": "X", "name": "ReadAsync 1099", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937234, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937259, "dur": 3, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937262, "dur": 25, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937290, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937293, "dur": 26, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937322, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937324, "dur": 25, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937352, "dur": 20, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937374, "dur": 18, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937394, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937396, "dur": 28, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937426, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937428, "dur": 29, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937460, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937463, "dur": 30, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937495, "dur": 2, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937498, "dur": 35, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937537, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937571, "dur": 1, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937573, "dur": 29, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937604, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937606, "dur": 26, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937636, "dur": 24, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937661, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937663, "dur": 30, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937695, "dur": 4, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937700, "dur": 29, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937731, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937733, "dur": 41, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937776, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937778, "dur": 23, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937804, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937828, "dur": 27, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937857, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937859, "dur": 26, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937888, "dur": 27, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937917, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937919, "dur": 30, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937951, "dur": 1, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937953, "dur": 22, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487937978, "dur": 29, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938008, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938010, "dur": 27, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938038, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938040, "dur": 28, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938072, "dur": 18, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938093, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938136, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938168, "dur": 1, "ph": "X", "name": "ProcessMessages 1006", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938170, "dur": 22, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938197, "dur": 31, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938230, "dur": 1, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938232, "dur": 26, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938261, "dur": 31, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938295, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938296, "dur": 27, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938326, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938328, "dur": 29, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938359, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938360, "dur": 33, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938395, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938397, "dur": 23, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938423, "dur": 28, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938453, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938455, "dur": 27, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938484, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938486, "dur": 24, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938511, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938513, "dur": 33, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938548, "dur": 1, "ph": "X", "name": "ProcessMessages 1092", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938550, "dur": 30, "ph": "X", "name": "ReadAsync 1092", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938582, "dur": 28, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938613, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938614, "dur": 26, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938644, "dur": 20, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938667, "dur": 19, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938689, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938712, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938741, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938743, "dur": 22, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938767, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938768, "dur": 64, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938834, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938836, "dur": 31, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938870, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938872, "dur": 29, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938905, "dur": 25, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938934, "dur": 24, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938961, "dur": 14, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487938978, "dur": 58, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939039, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939066, "dur": 19, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939089, "dur": 94, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939186, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939212, "dur": 23, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939238, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939261, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939263, "dur": 29, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939296, "dur": 20, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939319, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939340, "dur": 30, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939372, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939374, "dur": 26, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939402, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939404, "dur": 22, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939427, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939429, "dur": 23, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939455, "dur": 68, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939527, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939549, "dur": 28, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939580, "dur": 21, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939603, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939604, "dur": 29, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939635, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939637, "dur": 26, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939666, "dur": 29, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939696, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939698, "dur": 25, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939726, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939728, "dur": 28, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939758, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939759, "dur": 19, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939781, "dur": 18, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939803, "dur": 27, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939832, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939834, "dur": 25, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939861, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939863, "dur": 28, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939892, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939894, "dur": 20, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939917, "dur": 29, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939948, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939950, "dur": 25, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939977, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487939978, "dur": 24, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940005, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940007, "dur": 20, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940029, "dur": 25, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940058, "dur": 23, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940084, "dur": 28, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940114, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940116, "dur": 23, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940140, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940143, "dur": 29, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940174, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940175, "dur": 27, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940207, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940259, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940262, "dur": 25, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940290, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940293, "dur": 28, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940324, "dur": 45, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940373, "dur": 2, "ph": "X", "name": "ProcessMessages 1406", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940376, "dur": 33, "ph": "X", "name": "ReadAsync 1406", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940412, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940414, "dur": 30, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940448, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940449, "dur": 28, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940480, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940481, "dur": 25, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940510, "dur": 28, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940541, "dur": 18, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940561, "dur": 1, "ph": "X", "name": "ProcessMessages 51", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940563, "dur": 23, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940591, "dur": 33, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940625, "dur": 1, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940628, "dur": 22, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940651, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940653, "dur": 28, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940684, "dur": 1, "ph": "X", "name": "ProcessMessages 1011", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940685, "dur": 30, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940717, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940719, "dur": 26, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940748, "dur": 24, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940775, "dur": 21, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940800, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940802, "dur": 31, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940835, "dur": 1, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940837, "dur": 27, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940868, "dur": 24, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940894, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940895, "dur": 30, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940927, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940929, "dur": 26, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940957, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940958, "dur": 28, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487940990, "dur": 20, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941012, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941014, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941036, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941066, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941068, "dur": 22, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941092, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941094, "dur": 28, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941124, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941126, "dur": 19, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941148, "dur": 31, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941181, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941183, "dur": 22, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941207, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941208, "dur": 28, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941238, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941240, "dur": 20, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941263, "dur": 23, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941289, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941320, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941322, "dur": 22, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941346, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941348, "dur": 29, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941380, "dur": 1, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941382, "dur": 20, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941405, "dur": 29, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941437, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941439, "dur": 26, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941467, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941496, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941498, "dur": 19, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941521, "dur": 41, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941564, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941595, "dur": 1, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941597, "dur": 25, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941623, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941625, "dur": 27, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941654, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941656, "dur": 27, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941685, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941687, "dur": 23, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941713, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941714, "dur": 29, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941745, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941747, "dur": 20, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941770, "dur": 48, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941821, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941853, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941855, "dur": 22, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941879, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941880, "dur": 28, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941910, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941912, "dur": 21, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941935, "dur": 27, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941970, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487941973, "dur": 35, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942010, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942012, "dur": 25, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942039, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942041, "dur": 30, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942073, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942075, "dur": 197, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942275, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942308, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942311, "dur": 22, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942336, "dur": 34, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942373, "dur": 23, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942401, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942402, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942422, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942450, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942452, "dur": 29, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942483, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942485, "dur": 31, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942518, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942521, "dur": 23, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942548, "dur": 70, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942622, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942648, "dur": 22, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942672, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942674, "dur": 23, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942701, "dur": 20, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942724, "dur": 18, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942743, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942745, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942766, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942791, "dur": 20, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942814, "dur": 26, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942843, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942845, "dur": 26, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942873, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942875, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942901, "dur": 78, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942981, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487942988, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943019, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943021, "dur": 21, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943044, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943045, "dur": 23, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943072, "dur": 25, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943099, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943101, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943125, "dur": 31, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943158, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943161, "dur": 23, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943185, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943187, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943211, "dur": 23, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943238, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943269, "dur": 30, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943301, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943303, "dur": 27, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943332, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943333, "dur": 24, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943359, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943362, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943387, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943389, "dur": 32, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943423, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943425, "dur": 26, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943454, "dur": 23, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943479, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943481, "dur": 18, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943503, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943527, "dur": 32, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943561, "dur": 1, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943564, "dur": 27, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943593, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943595, "dur": 31, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943628, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943630, "dur": 20, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943653, "dur": 29, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943684, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943685, "dur": 22, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943709, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943711, "dur": 25, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943737, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943739, "dur": 19, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943761, "dur": 19, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943783, "dur": 29, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943814, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943816, "dur": 26, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943845, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943846, "dur": 27, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943875, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943877, "dur": 28, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943907, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943909, "dur": 23, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943935, "dur": 25, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943962, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943963, "dur": 20, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487943987, "dur": 27, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944017, "dur": 21, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944040, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944042, "dur": 29, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944073, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944075, "dur": 26, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944103, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944105, "dur": 20, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944128, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944159, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944161, "dur": 35, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944198, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944200, "dur": 20, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944224, "dur": 26, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944253, "dur": 29, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944283, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944285, "dur": 25, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944314, "dur": 23, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944339, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944342, "dur": 27, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944370, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944372, "dur": 25, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944399, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944401, "dur": 24, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944426, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944428, "dur": 21, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944452, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944488, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944490, "dur": 27, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944519, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944521, "dur": 26, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944551, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944574, "dur": 28, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944603, "dur": 1, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944606, "dur": 27, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944635, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944637, "dur": 23, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944662, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944664, "dur": 22, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944688, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944690, "dur": 14, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944706, "dur": 19, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944729, "dur": 26, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944757, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944759, "dur": 25, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944786, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944787, "dur": 22, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944812, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944814, "dur": 27, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944842, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944844, "dur": 26, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944874, "dur": 30, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944906, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944908, "dur": 21, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944931, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944932, "dur": 18, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944953, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944982, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487944984, "dur": 21, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945007, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945009, "dur": 27, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945039, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945040, "dur": 20, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945064, "dur": 27, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945110, "dur": 31, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945143, "dur": 2, "ph": "X", "name": "ProcessMessages 1346", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945146, "dur": 22, "ph": "X", "name": "ReadAsync 1346", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945172, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945195, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945231, "dur": 1, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945234, "dur": 30, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945266, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945268, "dur": 23, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945293, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945294, "dur": 22, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945319, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945321, "dur": 28, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945351, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945353, "dur": 23, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945377, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945379, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945406, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945409, "dur": 21, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945433, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945464, "dur": 1, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945465, "dur": 27, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945495, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945497, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945522, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945524, "dur": 26, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945554, "dur": 28, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945584, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945586, "dur": 27, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945615, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945616, "dur": 22, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945640, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945642, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945664, "dur": 49, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945715, "dur": 1, "ph": "X", "name": "ProcessMessages 1316", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945717, "dur": 25, "ph": "X", "name": "ReadAsync 1316", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945745, "dur": 26, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945773, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945775, "dur": 27, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945804, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945805, "dur": 22, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945829, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945831, "dur": 24, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945858, "dur": 22, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945883, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945912, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945914, "dur": 27, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945942, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945944, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945969, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487945970, "dur": 28, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946000, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946002, "dur": 22, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946027, "dur": 30, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946059, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946061, "dur": 22, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946085, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946088, "dur": 28, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946117, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946119, "dur": 23, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946146, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946168, "dur": 23, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946193, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946195, "dur": 19, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946217, "dur": 16, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946237, "dur": 30, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946268, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946270, "dur": 21, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946293, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946295, "dur": 40, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946338, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946368, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946369, "dur": 22, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946393, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946394, "dur": 34, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946431, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946462, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946464, "dur": 18, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946485, "dur": 17, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946504, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946507, "dur": 20, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946531, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946562, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946564, "dur": 20, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946586, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946588, "dur": 30, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946620, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946650, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946652, "dur": 20, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946674, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946676, "dur": 31, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946710, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946739, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946741, "dur": 21, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946764, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946766, "dur": 30, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946799, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946848, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946850, "dur": 28, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946879, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946881, "dur": 50, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946934, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946965, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946972, "dur": 23, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946997, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487946999, "dur": 84, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947086, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947115, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947118, "dur": 32, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947152, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947153, "dur": 69, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947225, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947257, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947259, "dur": 19, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947281, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947283, "dur": 43, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947328, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947350, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947354, "dur": 19, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947375, "dur": 3, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947379, "dur": 66, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947450, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947470, "dur": 3, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947474, "dur": 18, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947493, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947497, "dur": 29, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947528, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947531, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947550, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947554, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947574, "dur": 3, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947578, "dur": 28, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947608, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947611, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947630, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947634, "dur": 18, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947653, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947657, "dur": 27, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947686, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947689, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947709, "dur": 3, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947713, "dur": 16, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947731, "dur": 2, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947734, "dur": 17, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947754, "dur": 2, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947757, "dur": 13, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947772, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947776, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947795, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947798, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947817, "dur": 3, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947822, "dur": 18, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947842, "dur": 3, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947845, "dur": 26, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947873, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947876, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947895, "dur": 3, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947899, "dur": 18, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947919, "dur": 3, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947922, "dur": 26, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947950, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947954, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947975, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947979, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487947998, "dur": 2, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948002, "dur": 23, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948027, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948030, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948049, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948052, "dur": 18, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948072, "dur": 3, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948076, "dur": 47, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948125, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948129, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948158, "dur": 17, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948178, "dur": 3, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948181, "dur": 20, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948203, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948208, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948228, "dur": 2, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948231, "dur": 17, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948250, "dur": 3, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948254, "dur": 24, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948280, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948283, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948305, "dur": 2, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948309, "dur": 18, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948328, "dur": 3, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948332, "dur": 21, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948355, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948359, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948377, "dur": 3, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948381, "dur": 18, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948401, "dur": 3, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948405, "dur": 49, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948455, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948459, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948478, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948481, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948501, "dur": 3, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948505, "dur": 23, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948533, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948552, "dur": 3, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948556, "dur": 41, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948599, "dur": 3, "ph": "X", "name": "ProcessMessages 1016", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948603, "dur": 13, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948622, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948663, "dur": 3, "ph": "X", "name": "ProcessMessages 1131", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948667, "dur": 17, "ph": "X", "name": "ReadAsync 1131", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948686, "dur": 3, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948690, "dur": 12, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948703, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948725, "dur": 2, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948729, "dur": 16, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948747, "dur": 3, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948751, "dur": 15, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948771, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948789, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948792, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948812, "dur": 3, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948815, "dur": 18, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948835, "dur": 3, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948839, "dur": 24, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948865, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948868, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948888, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948891, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948911, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948914, "dur": 24, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948940, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948944, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948964, "dur": 3, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948967, "dur": 18, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948987, "dur": 3, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487948990, "dur": 45, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949037, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949041, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949064, "dur": 3, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949068, "dur": 17, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949086, "dur": 3, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949090, "dur": 22, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949114, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949117, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949137, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949141, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949160, "dur": 3, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949164, "dur": 22, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949187, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949191, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949218, "dur": 3, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949221, "dur": 16, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949239, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949243, "dur": 20, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949265, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949268, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949287, "dur": 3, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949291, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949310, "dur": 3, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949314, "dur": 47, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949362, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949366, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949385, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949389, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949408, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949412, "dur": 24, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949442, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949464, "dur": 3, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949468, "dur": 17, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949486, "dur": 3, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949490, "dur": 19, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949511, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949514, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949533, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949537, "dur": 17, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949556, "dur": 3, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949560, "dur": 22, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949584, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949587, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949613, "dur": 3, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949617, "dur": 16, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949635, "dur": 2, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949638, "dur": 20, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949660, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949664, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949681, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949684, "dur": 15, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949701, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949705, "dur": 21, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949727, "dur": 3, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949731, "dur": 24, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949757, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949760, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949780, "dur": 3, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949783, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949804, "dur": 2, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949808, "dur": 33, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949842, "dur": 3, "ph": "X", "name": "ProcessMessages 1100", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949846, "dur": 16, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949864, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949868, "dur": 15, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949885, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949888, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949908, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949911, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949928, "dur": 2, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949932, "dur": 15, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949949, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949952, "dur": 18, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949972, "dur": 3, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949975, "dur": 19, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487949996, "dur": 3, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950000, "dur": 25, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950026, "dur": 3, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950030, "dur": 15, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950051, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950071, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950074, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950093, "dur": 3, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950097, "dur": 18, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950117, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950120, "dur": 23, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950145, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950149, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950165, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950169, "dur": 19, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950190, "dur": 3, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950194, "dur": 18, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950213, "dur": 3, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950217, "dur": 21, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950240, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950243, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950262, "dur": 4, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950266, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950286, "dur": 2, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950289, "dur": 23, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950315, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950318, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950344, "dur": 3, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950348, "dur": 16, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950366, "dur": 3, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950370, "dur": 19, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950390, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950394, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950413, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950416, "dur": 18, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950436, "dur": 3, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950440, "dur": 22, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950464, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950468, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950484, "dur": 2, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950488, "dur": 14, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950503, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950507, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950532, "dur": 3, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950535, "dur": 23, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950560, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950564, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950582, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950586, "dur": 17, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950605, "dur": 3, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950609, "dur": 25, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950636, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950640, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950659, "dur": 3, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950663, "dur": 18, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950682, "dur": 3, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950686, "dur": 23, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950710, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950714, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950739, "dur": 3, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950743, "dur": 16, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950761, "dur": 3, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950764, "dur": 17, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950784, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950787, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950804, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950807, "dur": 15, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950825, "dur": 2, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950828, "dur": 23, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950853, "dur": 3, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950857, "dur": 30, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950888, "dur": 3, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950892, "dur": 16, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950910, "dur": 3, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950914, "dur": 16, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950931, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950935, "dur": 19, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950955, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950959, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950984, "dur": 3, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487950988, "dur": 18, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951008, "dur": 3, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951011, "dur": 16, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951029, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951032, "dur": 44, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951078, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951082, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951101, "dur": 3, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951104, "dur": 16, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951122, "dur": 3, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951126, "dur": 15, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951144, "dur": 2, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951147, "dur": 17, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951166, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951169, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951190, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951192, "dur": 88, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951283, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951305, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951328, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951348, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951366, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951386, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951404, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951422, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951439, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951457, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951475, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951496, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951514, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951532, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951554, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951556, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951579, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951601, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951603, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951625, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951646, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951669, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951671, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951693, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951713, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951732, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951761, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951764, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951819, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951840, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951862, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951863, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951881, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951903, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951923, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951951, "dur": 17, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951970, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951972, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487951990, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952008, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952026, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952044, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952063, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952065, "dur": 14, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952083, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952101, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952123, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952140, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952158, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952159, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952179, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952203, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952223, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952224, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952245, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952263, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952281, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952284, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952303, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952321, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952342, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952362, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952380, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952399, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952417, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952436, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952454, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952456, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952477, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952479, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952504, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952505, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952525, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952545, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952567, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952585, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952603, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952621, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952640, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952659, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952676, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952695, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952715, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952734, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952754, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952778, "dur": 22, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952802, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952804, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952824, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952846, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952848, "dur": 19, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952869, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952871, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952893, "dur": 19, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952914, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952933, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952950, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952967, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952986, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487952988, "dur": 24, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953015, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953035, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953056, "dur": 17, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953076, "dur": 16, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953095, "dur": 17, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953114, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953115, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953141, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953163, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953165, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953186, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953218, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953243, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953245, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953264, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953284, "dur": 16, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953303, "dur": 16, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953322, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953341, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953361, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953363, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953383, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953402, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953419, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953436, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953459, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953477, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953605, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953624, "dur": 195, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953823, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953841, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953859, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953889, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487953908, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487954023, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487954044, "dur": 3825, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487957874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487957876, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487957897, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487957899, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487957929, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487957946, "dur": 1358, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959309, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959330, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959357, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959361, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959386, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959388, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959408, "dur": 415, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959827, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959857, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959877, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959883, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487959897, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960048, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960056, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960079, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960153, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960171, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960223, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960242, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960260, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960301, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960322, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960350, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960366, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960382, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960401, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960429, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960445, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960500, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960516, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960597, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960619, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960654, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960672, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960709, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960728, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960745, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960761, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960790, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487960806, "dur": 222, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961031, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961047, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961072, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961090, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961107, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961124, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961141, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961157, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961174, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961189, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961205, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961226, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961244, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961267, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961290, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961307, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961324, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961398, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961416, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961417, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961438, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961478, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961496, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961514, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961534, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961574, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961590, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961643, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961660, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961685, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961700, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961735, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961752, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961776, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961797, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961846, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961863, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961899, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961916, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961959, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961976, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487961998, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962018, "dur": 13, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962034, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962081, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962100, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962129, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962146, "dur": 226, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962375, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962392, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962408, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962433, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962449, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962466, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962486, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962509, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962528, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962530, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962548, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962551, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962568, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962591, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962607, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962624, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962641, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962675, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962695, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962697, "dur": 161, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962861, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962881, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962943, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962964, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487962987, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963008, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963027, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963029, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963049, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963068, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963189, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963209, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963227, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963245, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963401, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963421, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963438, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963457, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963474, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963491, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963525, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963541, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963590, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963617, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963637, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963662, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963709, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963727, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963748, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963767, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963785, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963805, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963825, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963842, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963882, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963904, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963924, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963945, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963965, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487963983, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964003, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964025, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964226, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964246, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964275, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964309, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964330, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964383, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964406, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964428, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964449, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964454, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964513, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964533, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964535, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964628, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964654, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964679, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964745, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964778, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964808, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964935, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487964956, "dur": 187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487965147, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487965167, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608487965172, "dur": 110572, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488075756, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488075759, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488075787, "dur": 27, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488075815, "dur": 3091, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488078915, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488078919, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488078935, "dur": 835, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488079775, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488079793, "dur": 8, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488079804, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488079846, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488079856, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488079890, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488079902, "dur": 190, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488080095, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488080105, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488080169, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488080183, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488080201, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488080271, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488080282, "dur": 462, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488080747, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488080758, "dur": 1331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082093, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082103, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082142, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082152, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082213, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082224, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082249, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082260, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082353, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082364, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082377, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082414, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082426, "dur": 245, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082673, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082692, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082723, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488082737, "dur": 298, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488083038, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488083048, "dur": 284, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488083335, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488083347, "dur": 511, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488083862, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488083877, "dur": 490, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084370, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084382, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084397, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084408, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084429, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084443, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084445, "dur": 10, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084457, "dur": 426, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084888, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084890, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084904, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084931, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084943, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084962, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488084980, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085003, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085017, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085028, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085046, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085057, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085074, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085086, "dur": 7, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085095, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085113, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085125, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085139, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085150, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085174, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085190, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085210, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085212, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085228, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085239, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085257, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085273, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085285, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085300, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085313, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085329, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085343, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085358, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085371, "dur": 13, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085388, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085402, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085413, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085424, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085439, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085461, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085476, "dur": 174, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085654, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085777, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085779, "dur": 44, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085826, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085828, "dur": 83, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085920, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085938, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085959, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085961, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488085972, "dur": 111514, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488197495, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488197499, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488197554, "dur": 276, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488197832, "dur": 12112, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488209955, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488209960, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488209978, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488209982, "dur": 335552, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488545546, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488545550, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488545576, "dur": 23, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488545600, "dur": 3796, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488549403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488549405, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488549425, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488549428, "dur": 847, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488550281, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488550303, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488550322, "dur": 14781, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488565112, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488565116, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488565137, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488565142, "dur": 511, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488565657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488565659, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488565686, "dur": 37, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488565724, "dur": 612, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488566340, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488566378, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749608488566381, "dur": 7618, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26084, "tid": 279, "ts": 1749608488574306, "dur": 3240, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26084, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26084, "tid": 42949672960, "ts": 1749608487905984, "dur": 29701, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26084, "tid": 42949672960, "ts": 1749608487935686, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26084, "tid": 42949672960, "ts": 1749608487935687, "dur": 51, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26084, "tid": 279, "ts": 1749608488577551, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26084, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26084, "tid": 38654705664, "ts": 1749608487881027, "dur": 693015, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26084, "tid": 38654705664, "ts": 1749608487881103, "dur": 24301, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26084, "tid": 38654705664, "ts": 1749608488574047, "dur": 39, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26084, "tid": 38654705664, "ts": 1749608488574059, "dur": 13, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26084, "tid": 279, "ts": 1749608488577564, "dur": 60, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749608487935324, "dur": 1218, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749608487936552, "dur": 453, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749608487937104, "dur": 53, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749608487937157, "dur": 337, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749608487937514, "dur": 14392, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749608487951916, "dur": 614703, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749608488566620, "dur": 238, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749608488566990, "dur": 59, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749608488567069, "dur": 1252, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749608487937736, "dur": 14214, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487951998, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1749608487951952, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_699CA9E1D209A267.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749608487952149, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487952201, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608487952200, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_9DDD8969E101654D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749608487952392, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487952493, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487952799, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487953011, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749608487953138, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487953429, "dur": 1199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487954744, "dur": 742, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-rtlsupport-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608487955487, "dur": 2216, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-profile-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608487954631, "dur": 3352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487957983, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487958205, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487958728, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487958881, "dur": 681, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\PendingChanges\\UnityPendingChangesTree.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749608487958866, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487959940, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487960078, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487960573, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487960812, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749608487960965, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608487961320, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608487960883, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749608487961715, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487962418, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749608487962696, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608487962634, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749608487963221, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749608487963524, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487963663, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749608487963731, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749608487964141, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608487964368, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749608487964439, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749608487964690, "dur": 113725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608488078416, "dur": 1345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749608488079761, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749608488080532, "dur": 2531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749608488083171, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608488083480, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608488084447, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608488085170, "dur": 487, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608488085785, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749608488083106, "dur": 3206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749608488086377, "dur": 480238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487937705, "dur": 14235, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487951942, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749608487952049, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608487952048, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_C7A46CFE1C6B2861.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749608487952205, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_C7A46CFE1C6B2861.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749608487952297, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608487952296, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_36BB48A459E34DBF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749608487952485, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487952622, "dur": 1996, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487954630, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749608487954686, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487955051, "dur": 458, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608487955907, "dur": 477, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608487956717, "dur": 733, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749608487957479, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749608487957586, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749608487958225, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ScrollRect.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749608487954751, "dur": 3782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749608487958675, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487958939, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487959158, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487959373, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487959634, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487959930, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487960085, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487960569, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487960819, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749608487960926, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487961504, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608487961817, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608487962021, "dur": 382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608487962446, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608487961336, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749608487962840, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487963333, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749608487963749, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749608487964257, "dur": 1131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608487965389, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749608487965518, "dur": 114993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749608488080911, "dur": 2214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608488083436, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-heap-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608488083571, "dur": 894, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.Abstractions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608488084546, "dur": 580, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Core.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749608488080512, "dur": 5934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749608488086480, "dur": 480049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487937698, "dur": 14236, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487951946, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487952030, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749608487952022, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7027E12F917228B0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749608487952152, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487952242, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487952643, "dur": 1116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487954755, "dur": 740, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encoding.CodePages.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749608487955495, "dur": 2054, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ServiceProcess.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749608487954088, "dur": 3489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487957577, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487957829, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487957975, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487958127, "dur": 1428, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\FixedStringFormatMethods.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749608487958127, "dur": 2036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487960164, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487960563, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487961140, "dur": 369, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749608487961793, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749608487961122, "dur": 998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749608487962504, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487962725, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487963093, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487963419, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608487964269, "dur": 113879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608488078149, "dur": 1442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749608488079629, "dur": 1338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749608488081007, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749608488082266, "dur": 2336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749608488085701, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Physical.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749608488085847, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749608488084607, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749608488086668, "dur": 479763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487937667, "dur": 14259, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487951942, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487952028, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4740E213CDF2FB27.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749608487952281, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487952374, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487952515, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487953202, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487953470, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487953882, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11344994280883157806.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749608487954082, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487954274, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487954895, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487955125, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487955334, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487955591, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487955783, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487956083, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487956337, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487956722, "dur": 825, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnGUI.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749608487956570, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487957729, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487957866, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487958014, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487958162, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487958361, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487958491, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487958987, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487959197, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487959407, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487959686, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487960262, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487960568, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487960834, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749608487961505, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749608487961079, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749608487961941, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487962026, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749608487962181, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487962493, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749608487962243, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749608487962960, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487963201, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749608487963421, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749608487963806, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487963921, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749608487963986, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608487964480, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749608487964715, "dur": 116201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749608488080918, "dur": 2135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749608488083541, "dur": 1012, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749608488083089, "dur": 3326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749608488086442, "dur": 480003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487937634, "dur": 14287, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487951923, "dur": 5156, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487957080, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487957891, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487958124, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487958396, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487958685, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487958927, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487959146, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487959367, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487959605, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487959925, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487960061, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487960572, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487960818, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749608487960947, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487961439, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749608487961234, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749608487961809, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749608487962453, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749608487962968, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487963283, "dur": 1006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608487964289, "dur": 113851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608488078142, "dur": 2356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749608488080499, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608488081404, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\clretwrc.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749608488080581, "dur": 2259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749608488082840, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608488083703, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-filesystem-l1-1-0.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749608488083094, "dur": 2449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749608488085544, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608488085766, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608488085878, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608488086115, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749608488086387, "dur": 480090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487937727, "dur": 14219, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487951949, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_094A556A01D249DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749608487952125, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487952403, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749608487952394, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_ADBEF0390C22E5A8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749608487952802, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487952913, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487953365, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487953705, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487954115, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487954814, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487955137, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487955369, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487955568, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487955723, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487955932, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487956162, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487956411, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487956718, "dur": 734, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\ValueExpression.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749608487956643, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487957761, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487958002, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487958213, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487958472, "dur": 1464, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Graphs\\MergedGraphElementCollection.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749608487958433, "dur": 1645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487960078, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487960571, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487960817, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749608487960946, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487961438, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749608487961328, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749608487961900, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487961972, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487962169, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608487962500, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749608487962643, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749608487963969, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749608487964148, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749608487964637, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749608487964719, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749608487965049, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749608487965258, "dur": 112899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608488078158, "dur": 2323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749608488080911, "dur": 603, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749608488082932, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749608488080526, "dur": 2834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749608488083360, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608488083479, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749608488083570, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749608488084546, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventLog.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749608488085732, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749608488085806, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749608488083456, "dur": 2522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749608488086039, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608488086351, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749608488086687, "dur": 479732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487937761, "dur": 14194, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487951958, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21B473704285E949.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749608487952138, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487952283, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_88B56BCE4990CA03.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749608487952481, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487952618, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487952708, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487952789, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487952939, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487954118, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487954895, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487955116, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487955329, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487955570, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487955788, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487956166, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487956412, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487956719, "dur": 737, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Connections\\ValueConnection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749608487956658, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487957763, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487957909, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487958066, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487958349, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487958581, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487958916, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487959141, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487959363, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487959601, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487959813, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487959979, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487960061, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487960577, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487960993, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749608487961093, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487961522, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749608487962458, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487962863, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749608487962966, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487963188, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749608487963672, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749608487963757, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608487964261, "dur": 113882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608488078144, "dur": 2042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749608488080186, "dur": 712, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608488080911, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749608488080906, "dur": 2160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749608488083119, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749608488083437, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749608488085192, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749608488083104, "dur": 2310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749608488085414, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608488085931, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608488086205, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749608488086512, "dur": 479925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487937789, "dur": 14172, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487952004, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1749608487951963, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749608487952375, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487952495, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487952603, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487952888, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487953141, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487953441, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487953533, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487953748, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487953886, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749608487953947, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487954103, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487954191, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487954984, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487955204, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487955426, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487955702, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487956027, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487956289, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487956723, "dur": 737, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Lifecycle\\OnDisable.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749608487956511, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487957545, "dur": 756, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Utilities\\ScriptReference.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749608487957467, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487958469, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487958879, "dur": 682, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Animation\\AnimationTrack.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749608487958701, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487959592, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487959803, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487960234, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487960577, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487960975, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749608487961118, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749608487961245, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487961442, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749608487961776, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749608487961441, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749608487962005, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487962258, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487962418, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749608487962633, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487962692, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749608487963102, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487963276, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487963346, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608487964279, "dur": 113859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608488080501, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749608488078147, "dur": 2423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749608488080570, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608488080831, "dur": 2121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749608488083480, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749608488083571, "dur": 885, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749608488085733, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749608488082986, "dur": 3353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749608488086365, "dur": 463561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749608488549928, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749608488549927, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749608488550131, "dur": 881, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749608488551018, "dur": 15463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487937811, "dur": 14155, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487951980, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749608487951969, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0998A984E55471CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749608487952338, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487952478, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487952632, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487952726, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487953511, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487953839, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10654656162358534522.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749608487954121, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487954806, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487955036, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487955267, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487955513, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487955775, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487956111, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487956377, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487956723, "dur": 729, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Control\\SwitchOnEnum.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749608487956624, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487957747, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487957990, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487958228, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487958519, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487958746, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487958983, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487959201, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487959400, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487960067, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487960572, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487960815, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749608487960948, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487961049, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749608487961545, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487961881, "dur": 930, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487962817, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749608487963075, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487963407, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749608487963855, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487963966, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749608487964076, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608487964131, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749608487964566, "dur": 116390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608488080956, "dur": 2142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749608488083098, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608488083170, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749608488084439, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Identity.Core.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749608488085732, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749608488083161, "dur": 2784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749608488086002, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608488086168, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749608488086439, "dur": 480022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487937858, "dur": 14114, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487951975, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5AA67F5B53C44BAD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749608487952251, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487952378, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487952466, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749608487952465, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_20B214415B84035D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749608487952613, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487952736, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487952852, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487953004, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487953187, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487953563, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487953651, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749608487953813, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487954095, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487954738, "dur": 124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487954862, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487955005, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487955142, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487955295, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487955437, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487956245, "dur": 1209, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Editor\\SkinningModule\\SkinningCache\\MeshPreviewCache.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749608487957473, "dur": 661, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Editor\\SkinningModule\\SkinningCache\\CharacterPartCacheExtensions.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749608487955736, "dur": 2437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487958338, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\SerializedProperties\\SerializedPropertyProvider.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749608487958174, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487958954, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487959104, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487959256, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487959409, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487959772, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487960238, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487960581, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487960813, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749608487960867, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487961504, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Graphs\\IGraphData.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749608487961050, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749608487962384, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487962479, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749608487962697, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749608487962579, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749608487963406, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487963640, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749608487963692, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749608487963773, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749608487964024, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487964182, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487964238, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608487964332, "dur": 113814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608488078147, "dur": 2311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749608488080510, "dur": 2216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749608488082726, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608488083119, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749608488083480, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749608488082829, "dur": 2246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749608488085179, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608488085632, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749608488085716, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608488086143, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749608488086475, "dur": 480076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487937871, "dur": 14115, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487952251, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487952413, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487952640, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487952903, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487953071, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487953191, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487953648, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487954153, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487954631, "dur": 857, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\clretwrc.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749608487956234, "dur": 1219, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-runtime-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749608487957583, "dur": 719, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-math-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749608487954625, "dur": 3843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487958469, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487958878, "dur": 934, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Evaluation\\RuntimeClipBase.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749608487958692, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487959924, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487960066, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487960565, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487960973, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749608487961041, "dur": 760, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487961867, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749608487961806, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749608487962230, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749608487962697, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\ICoverageReporterFilter.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749608487962306, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749608487962758, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487963221, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749608487963360, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749608487963724, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487963784, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608487964282, "dur": 113869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608488078152, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749608488080626, "dur": 2217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749608488082844, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608488083171, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749608488082949, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749608488084800, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608488085181, "dur": 1261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749608488086444, "dur": 480123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487937909, "dur": 14115, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487952122, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487952211, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_9E8FB24CB62B009F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749608487952413, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749608487952412, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EE2E1C75DDFB128E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749608487952595, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487954743, "dur": 918, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749608487954083, "dur": 1795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487955878, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487956073, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487956267, "dur": 1287, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Runtime\\SpriteLib\\SpriteLibraryAsset.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749608487957554, "dur": 2464, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Runtime\\SpriteLib\\SpriteLibrary.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749608487956260, "dur": 3857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487960117, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487960567, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487961041, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749608487961998, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487962698, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749608487962477, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749608487963166, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487963276, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487963945, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487964248, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608487964640, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749608487964747, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749608487964972, "dur": 113178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608488078232, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749608488079101, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749608488079682, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749608488080912, "dur": 2265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749608488083437, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749608488078152, "dur": 5602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749608488083776, "dur": 1794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749608488085617, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749608488085907, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608488086165, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749608488086381, "dur": 480224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608487937932, "dur": 14120, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608487952054, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_B4C1A84D8F19D0AB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749608487952108, "dur": 585, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608487952714, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749608487952820, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608487953119, "dur": 1086, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749608487954325, "dur": 344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749608487955040, "dur": 469, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749608487955510, "dur": 414, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749608487956725, "dur": 738, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749608487957479, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnitySetUpAttribute.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749608487957545, "dur": 922, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTearDownAttribute.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749608487959534, "dur": 412, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackAttribute.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749608487952978, "dur": 7016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749608487960137, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749608487960624, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749608487960811, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749608487960861, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608487960965, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749608487961319, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749608487960956, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749608487961692, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608487961912, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608487962003, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749608487962367, "dur": 1263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608487963630, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749608487963763, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749608487964235, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1749608487964731, "dur": 119, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608487965197, "dur": 111261, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1749608488078137, "dur": 2349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749608488080514, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749608488082775, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608488082877, "dur": 1645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749608488084522, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749608488084588, "dur": 2001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749608488086616, "dur": 479842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487937964, "dur": 14182, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487952174, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487952378, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487952562, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749608487952561, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749608487952819, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487952989, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749608487953177, "dur": 1372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487954554, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487955068, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487955293, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487955914, "dur": 1779, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Editor\\SpriteLib\\SpriteLibraryEditor\\UI\\EditorWindowElements.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749608487955606, "dur": 2099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487957706, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487957928, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487958153, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487958395, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487958553, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487958715, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487959002, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487959141, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487959275, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487959407, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487959575, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487959714, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487960350, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487960574, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487960820, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749608487961320, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749608487961794, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\GridSelection\\GridSelectionMoveTool.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749608487961867, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\GridSelection\\GridSelectionScaleTool.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749608487961163, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749608487962139, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487962748, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487963424, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608487964265, "dur": 113879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608488078146, "dur": 2230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749608488080377, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608488082139, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749608488080515, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749608488082796, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608488083082, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749608488084545, "dur": 580, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749608488082887, "dur": 2735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749608488085701, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749608488085700, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749608488085993, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608488086160, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749608488086394, "dur": 480194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487937988, "dur": 14213, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487952263, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487952327, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487952645, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487952908, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487953051, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487953106, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487953261, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749608487953340, "dur": 1242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487954752, "dur": 752, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749608487954585, "dur": 1568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487956153, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487956331, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487956476, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487956721, "dur": 2222, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Codebase\\CreateStruct.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749608487956638, "dur": 2390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487959028, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487959253, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487959518, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487960006, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487960093, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487960568, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487960971, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749608487961117, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749608487961503, "dur": 379, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749608487961946, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749608487962493, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Audio\\AudioMixerProperties.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749608487961103, "dur": 1695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749608487962798, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487962872, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487963258, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749608487963328, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487963701, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749608487964265, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608487964346, "dur": 113810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608488078296, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749608488078157, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749608488080558, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608488080629, "dur": 2113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749608488082742, "dur": 2415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608488085185, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608488085849, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608488085947, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608488086206, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749608488086617, "dur": 479822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487938018, "dur": 14201, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487952442, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487952548, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749608487952548, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749608487952627, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487952799, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487953044, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487953119, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487953321, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487953450, "dur": 605, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487954089, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487954760, "dur": 1464, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-datetime-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749608487956224, "dur": 1328, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-2-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749608487957553, "dur": 748, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749608487954716, "dur": 3703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487958419, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487959044, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487959188, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487959334, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487959524, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487959686, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487960289, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487960564, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487960974, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749608487961035, "dur": 962, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487962455, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetsUtils\\Processor\\AssetPostprocessor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749608487962167, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749608487962989, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487963269, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749608487963357, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749608487963638, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749608487963768, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608487964290, "dur": 113863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608488078155, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749608488080911, "dur": 2215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749608488083170, "dur": 276, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749608488084447, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749608488084546, "dur": 581, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749608488085539, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749608488080506, "dur": 5295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749608488085801, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608488086184, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749608488086457, "dur": 480068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487938041, "dur": 14218, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487952427, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749608487952426, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_038A17E6D6B77576.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749608487952484, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487952547, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487952853, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487953089, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487953740, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1749608487954043, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487954334, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487955165, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487955299, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487955434, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487956047, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487956297, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487956447, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487956718, "dur": 723, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Animation\\OnAnimatorIK.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749608487956595, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487957464, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487957816, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487958034, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487958537, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487958710, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487958877, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\WebApi\\SubscriptionDetailsResponse.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749608487958855, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487959723, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487960180, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487960566, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487960878, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749608487960977, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749608487961329, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487961385, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749608487961483, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749608487961857, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487962028, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749608487962100, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487962446, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749608487962209, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749608487962547, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487962740, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487963226, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487963417, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608487964294, "dur": 113865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608488078159, "dur": 2265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749608488080425, "dur": 1052, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608488083120, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749608488083480, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749608488081483, "dur": 2163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749608488083647, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749608488085806, "dur": 360, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749608488084098, "dur": 2282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749608488086424, "dur": 480165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487938058, "dur": 14243, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487952484, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487952678, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487953509, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749608487953617, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487953717, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487954141, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487954804, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487954945, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487955073, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487955215, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487955361, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487955574, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487955714, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487955879, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487956009, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487956157, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487956414, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487956722, "dur": 1479, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Lists\\RemoveListItem.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749608487956636, "dur": 1880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487958517, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487958883, "dur": 672, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager@2.0.1\\Editor\\ISettingsRepository.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749608487958750, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487959969, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487960062, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487960573, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487960816, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749608487961117, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749608487961504, "dur": 280, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749608487962020, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749608487961045, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749608487962350, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487962427, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487962507, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749608487962506, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749608487962734, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487963223, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487963417, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608487964294, "dur": 113867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608488078163, "dur": 2387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749608488080551, "dur": 2853, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608488083435, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749608488083571, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749608488084435, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Localization.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749608488085863, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749608488083409, "dur": 2604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749608488086153, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608488086208, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749608488086682, "dur": 479739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487938081, "dur": 14358, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487952482, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487952611, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487953181, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487953268, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749608487953528, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487953994, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487954621, "dur": 904, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.VisualBasic.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749608487954122, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487955525, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487955890, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487956226, "dur": 1490, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Control\\SelectUnitDescriptor.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749608487956060, "dur": 1681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487957741, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487957980, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487958226, "dur": 1331, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Serialization\\ISerializationDependency.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749608487958197, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487959682, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487960154, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487960566, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487961042, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749608487961125, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749608487961467, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487961971, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487962274, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487962417, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749608487962696, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749608487962516, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749608487963040, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487963132, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749608487963271, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749608487963711, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487964252, "dur": 803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608487965056, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749608487965144, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749608487965382, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749608487965468, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749608487965666, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749608487965885, "dur": 232307, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749608488199092, "dur": 9094, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749608488199091, "dur": 9999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749608488210448, "dur": 169, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749608488210657, "dur": 335573, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749608488549923, "dur": 15866, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749608488549922, "dur": 15869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749608488565804, "dur": 583, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749608487938107, "dur": 14404, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487952512, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487953130, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487953331, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487953385, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487953439, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487953573, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487953637, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749608487953692, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487954111, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487954971, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487955198, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487955571, "dur": 737, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PsdFile\\Compression\\ImageDataFactory.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749608487955430, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487956368, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487956524, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487956715, "dur": 762, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\SinglePageWindow.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749608487957542, "dur": 1254, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\Sidebars\\SidebarPanel.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749608487956707, "dur": 2235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487958942, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487959171, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487959390, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487959635, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487959999, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487960071, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487960574, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487960816, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749608487961439, "dur": 349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749608487961794, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749608487961867, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749608487961325, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749608487962277, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487962386, "dur": 1528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487963921, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749608487964076, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608487964133, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749608487964526, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749608487964640, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749608487965112, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749608487965359, "dur": 112795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608488078160, "dur": 2306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749608488080502, "dur": 419, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749608488080923, "dur": 2059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749608488082982, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749608488083171, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749608488083571, "dur": 984, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749608488085695, "dur": 470, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventLog.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749608488083150, "dur": 3513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749608488086683, "dur": 479938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749608488571130, "dur": 3330, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26084, "tid": 279, "ts": 1749608488577684, "dur": 608, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26084, "tid": 279, "ts": 1749608488578347, "dur": 5601, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26084, "tid": 279, "ts": 1749608488574289, "dur": 9694, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}