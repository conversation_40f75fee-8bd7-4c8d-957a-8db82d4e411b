{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26084, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26084, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26084, "tid": 742, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26084, "tid": 742, "ts": 1749617489065289, "dur": 753, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26084, "tid": 742, "ts": 1749617489069476, "dur": 654, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26084, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26084, "tid": 1, "ts": 1749617488612415, "dur": 3685, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749617488616107, "dur": 17274, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749617488633388, "dur": 25193, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26084, "tid": 742, "ts": 1749617489070136, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 26084, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488611232, "dur": 30140, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488641376, "dur": 416147, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488642191, "dur": 1764, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488643959, "dur": 929, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488644890, "dur": 180, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645073, "dur": 9, "ph": "X", "name": "ProcessMessages 20522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645082, "dur": 28, "ph": "X", "name": "ReadAsync 20522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645115, "dur": 1, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645116, "dur": 17, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645138, "dur": 35, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645178, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645202, "dur": 23, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645229, "dur": 3, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645233, "dur": 25, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645260, "dur": 3, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645264, "dur": 17, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645283, "dur": 3, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645287, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645309, "dur": 3, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645313, "dur": 19, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645334, "dur": 3, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645338, "dur": 16, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645356, "dur": 3, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645360, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645378, "dur": 3, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645382, "dur": 15, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645399, "dur": 3, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645403, "dur": 21, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645426, "dur": 3, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645430, "dur": 21, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645453, "dur": 3, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645457, "dur": 19, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645478, "dur": 3, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645483, "dur": 19, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645504, "dur": 3, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645508, "dur": 19, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645529, "dur": 3, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645533, "dur": 17, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645552, "dur": 3, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645556, "dur": 14, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645572, "dur": 3, "ph": "X", "name": "ProcessMessages 115", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645576, "dur": 19, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645597, "dur": 3, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645601, "dur": 24, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645626, "dur": 3, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645631, "dur": 31, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645664, "dur": 3, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645668, "dur": 16, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645686, "dur": 2, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645690, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645711, "dur": 3, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645715, "dur": 25, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645741, "dur": 3, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645745, "dur": 17, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645763, "dur": 4, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645768, "dur": 14, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645785, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645788, "dur": 16, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645806, "dur": 2, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645810, "dur": 20, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645832, "dur": 3, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645836, "dur": 19, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645857, "dur": 3, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645861, "dur": 19, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645882, "dur": 3, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645885, "dur": 19, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645906, "dur": 3, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645910, "dur": 23, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645935, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645937, "dur": 26, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488645966, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646007, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646008, "dur": 24, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646036, "dur": 38, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646077, "dur": 2, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646080, "dur": 24, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646107, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646132, "dur": 31, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646164, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646166, "dur": 21, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646191, "dur": 33, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646227, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646229, "dur": 25, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646256, "dur": 16, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646278, "dur": 25, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646305, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646310, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646333, "dur": 21, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646356, "dur": 3, "ph": "X", "name": "ProcessMessages 1047", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646360, "dur": 16, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646379, "dur": 3, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646382, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646404, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646406, "dur": 459, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646866, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646868, "dur": 112, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646981, "dur": 6, "ph": "X", "name": "ProcessMessages 10812", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488646988, "dur": 30, "ph": "X", "name": "ReadAsync 10812", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647020, "dur": 1, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647022, "dur": 15, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647039, "dur": 19, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647060, "dur": 16, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647079, "dur": 32, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647112, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647124, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647147, "dur": 32, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647181, "dur": 26, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647209, "dur": 3, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647213, "dur": 19, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647239, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647262, "dur": 3, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647266, "dur": 19, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647290, "dur": 3, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647293, "dur": 9, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647304, "dur": 143, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647449, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647453, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647477, "dur": 3, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647481, "dur": 23, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647505, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647509, "dur": 12, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647523, "dur": 9, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647534, "dur": 12, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647548, "dur": 10, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647559, "dur": 9, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647570, "dur": 10, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647582, "dur": 16, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647599, "dur": 13, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647613, "dur": 36, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647651, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647655, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647679, "dur": 19, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647700, "dur": 2, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647703, "dur": 18, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647723, "dur": 3, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647727, "dur": 21, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647750, "dur": 3, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647754, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647777, "dur": 20, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647799, "dur": 3, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647802, "dur": 10, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647815, "dur": 32, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647853, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647878, "dur": 3, "ph": "X", "name": "ProcessMessages 1068", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647882, "dur": 21, "ph": "X", "name": "ReadAsync 1068", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647906, "dur": 3, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647911, "dur": 20, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647933, "dur": 3, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647938, "dur": 19, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647958, "dur": 3, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647962, "dur": 19, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647983, "dur": 10, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488647995, "dur": 9, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648006, "dur": 39, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648048, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648073, "dur": 27, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648103, "dur": 3, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648106, "dur": 21, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648129, "dur": 2, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648132, "dur": 53, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648187, "dur": 16, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648206, "dur": 15, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648222, "dur": 9, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648233, "dur": 8, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648243, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648264, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648275, "dur": 24, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648303, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648305, "dur": 44, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648351, "dur": 3, "ph": "X", "name": "ProcessMessages 1640", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648356, "dur": 27, "ph": "X", "name": "ReadAsync 1640", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648385, "dur": 3, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648388, "dur": 23, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648413, "dur": 3, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648417, "dur": 16, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648435, "dur": 11, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648448, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648459, "dur": 9, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648469, "dur": 9, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648479, "dur": 45, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648529, "dur": 1, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648531, "dur": 19, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648552, "dur": 3, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648557, "dur": 20, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648578, "dur": 3, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648582, "dur": 20, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648604, "dur": 3, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648608, "dur": 17, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648627, "dur": 3, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648630, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648653, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648672, "dur": 3, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648675, "dur": 19, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648697, "dur": 12, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648710, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648730, "dur": 2, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648734, "dur": 19, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648754, "dur": 4, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648760, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648783, "dur": 3, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648788, "dur": 19, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648808, "dur": 3, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648813, "dur": 16, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648830, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648834, "dur": 18, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648860, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648884, "dur": 12, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648898, "dur": 9, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648909, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648934, "dur": 19, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648957, "dur": 26, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488648990, "dur": 15, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649008, "dur": 2, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649011, "dur": 16, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649029, "dur": 2, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649033, "dur": 19, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649054, "dur": 3, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649058, "dur": 23, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649086, "dur": 11, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649099, "dur": 9, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649110, "dur": 9, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649121, "dur": 10, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649132, "dur": 9, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649143, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649163, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649196, "dur": 16, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649214, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649235, "dur": 34, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649281, "dur": 1, "ph": "X", "name": "ProcessMessages 1480", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649283, "dur": 42, "ph": "X", "name": "ReadAsync 1480", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649337, "dur": 1, "ph": "X", "name": "ProcessMessages 1744", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649338, "dur": 32, "ph": "X", "name": "ReadAsync 1744", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649377, "dur": 1, "ph": "X", "name": "ProcessMessages 1031", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649379, "dur": 15, "ph": "X", "name": "ReadAsync 1031", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649407, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649437, "dur": 3, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649441, "dur": 27, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649480, "dur": 33, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649666, "dur": 52, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649719, "dur": 2, "ph": "X", "name": "ProcessMessages 5600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649724, "dur": 48, "ph": "X", "name": "ReadAsync 5600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649773, "dur": 2, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649777, "dur": 49, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649829, "dur": 1, "ph": "X", "name": "ProcessMessages 1043", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649830, "dur": 37, "ph": "X", "name": "ReadAsync 1043", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649869, "dur": 22, "ph": "X", "name": "ProcessMessages 1506", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649892, "dur": 20, "ph": "X", "name": "ReadAsync 1506", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649914, "dur": 28, "ph": "X", "name": "ReadAsync 1682", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649943, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649954, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649972, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649983, "dur": 9, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488649994, "dur": 11, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650006, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650029, "dur": 13, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650043, "dur": 13, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650058, "dur": 21, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650081, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650085, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650248, "dur": 54, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650304, "dur": 4, "ph": "X", "name": "ProcessMessages 2945", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650310, "dur": 35, "ph": "X", "name": "ReadAsync 2945", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650396, "dur": 1, "ph": "X", "name": "ProcessMessages 1266", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650398, "dur": 23, "ph": "X", "name": "ReadAsync 1266", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650427, "dur": 18, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650556, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650557, "dur": 55, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650614, "dur": 1, "ph": "X", "name": "ProcessMessages 1755", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650616, "dur": 58, "ph": "X", "name": "ReadAsync 1755", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650676, "dur": 1, "ph": "X", "name": "ProcessMessages 1067", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650677, "dur": 26, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650705, "dur": 3, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650751, "dur": 17, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650770, "dur": 4, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650775, "dur": 39, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650817, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650821, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650870, "dur": 3, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488650874, "dur": 28, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651077, "dur": 34, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651112, "dur": 1, "ph": "X", "name": "ProcessMessages 3127", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651114, "dur": 42, "ph": "X", "name": "ReadAsync 3127", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651158, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651172, "dur": 20, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651194, "dur": 11, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651206, "dur": 34, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651241, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651261, "dur": 10, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651273, "dur": 9, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651284, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651322, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651334, "dur": 9, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651345, "dur": 9, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651356, "dur": 9, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651366, "dur": 140, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651617, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651697, "dur": 3, "ph": "X", "name": "ProcessMessages 3322", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651700, "dur": 32, "ph": "X", "name": "ReadAsync 3322", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651735, "dur": 45, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651781, "dur": 26, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651809, "dur": 5, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651815, "dur": 74, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651892, "dur": 22, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651922, "dur": 23, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488651948, "dur": 54, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652028, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652029, "dur": 24, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652056, "dur": 3, "ph": "X", "name": "ProcessMessages 994", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652060, "dur": 25, "ph": "X", "name": "ReadAsync 994", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652087, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652091, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652124, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652126, "dur": 29, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652157, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652159, "dur": 26, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652187, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652189, "dur": 28, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652219, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652220, "dur": 52, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652274, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652278, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652307, "dur": 17, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652330, "dur": 25, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652357, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652361, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652380, "dur": 3, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652384, "dur": 18, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652404, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652408, "dur": 29, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652440, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652443, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652463, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652466, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652486, "dur": 2, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652490, "dur": 30, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652523, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652554, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652556, "dur": 26, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652585, "dur": 58, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652649, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652672, "dur": 3, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652676, "dur": 18, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652696, "dur": 3, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652700, "dur": 26, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652729, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652732, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652755, "dur": 3, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652758, "dur": 18, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652779, "dur": 3, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652782, "dur": 17, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652801, "dur": 3, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652805, "dur": 27, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652834, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652837, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652857, "dur": 3, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652861, "dur": 19, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652882, "dur": 3, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652886, "dur": 18, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652906, "dur": 3, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652909, "dur": 15, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652927, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652930, "dur": 18, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652954, "dur": 19, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652975, "dur": 3, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488652979, "dur": 27, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653007, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653011, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653031, "dur": 3, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653035, "dur": 17, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653054, "dur": 3, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653058, "dur": 23, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653082, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653086, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653105, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653109, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653129, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653133, "dur": 23, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653158, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653162, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653183, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653187, "dur": 17, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653206, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653209, "dur": 46, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653257, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653261, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653280, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653284, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653304, "dur": 3, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653307, "dur": 24, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653333, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653336, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653356, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653359, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653383, "dur": 24, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653409, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653412, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653434, "dur": 3, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653438, "dur": 18, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653458, "dur": 2, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653462, "dur": 22, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653485, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653489, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653508, "dur": 3, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653512, "dur": 136, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653654, "dur": 28, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653687, "dur": 1, "ph": "X", "name": "ProcessMessages 1610", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653688, "dur": 18, "ph": "X", "name": "ReadAsync 1610", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653712, "dur": 22, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653736, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653740, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653759, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653763, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653783, "dur": 3, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653787, "dur": 25, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653813, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653817, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653840, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653859, "dur": 3, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653863, "dur": 25, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653889, "dur": 29, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653919, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653941, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653943, "dur": 31, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653977, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488653978, "dur": 22, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654003, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654035, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654037, "dur": 23, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654065, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654067, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654093, "dur": 20, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654115, "dur": 3, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654119, "dur": 16, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654137, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654141, "dur": 15, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654158, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654176, "dur": 2, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654180, "dur": 17, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654198, "dur": 2, "ph": "X", "name": "ProcessMessages 110", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654202, "dur": 18, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654221, "dur": 3, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654225, "dur": 18, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654245, "dur": 3, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654248, "dur": 19, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654269, "dur": 3, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654273, "dur": 16, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654290, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654294, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654311, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654315, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654332, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654336, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654356, "dur": 3, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654359, "dur": 17, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654383, "dur": 24, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654408, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654412, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654429, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654432, "dur": 18, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654452, "dur": 2, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654456, "dur": 17, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654475, "dur": 3, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654479, "dur": 23, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654503, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654507, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654526, "dur": 3, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654530, "dur": 17, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654548, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654552, "dur": 24, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654578, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654581, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654607, "dur": 3, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654611, "dur": 16, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654629, "dur": 3, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654632, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654657, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654677, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654681, "dur": 18, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654700, "dur": 2, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654704, "dur": 23, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654729, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654732, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654747, "dur": 59, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654810, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654840, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654842, "dur": 25, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654869, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654870, "dur": 65, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654938, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654975, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488654977, "dur": 30, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655010, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655012, "dur": 37, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655054, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655089, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655091, "dur": 21, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655116, "dur": 28, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655148, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655183, "dur": 1, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655185, "dur": 20, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655208, "dur": 92, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655304, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655331, "dur": 62, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655397, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655433, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655435, "dur": 28, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655465, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655467, "dur": 30, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655499, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655501, "dur": 19, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655524, "dur": 19, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655545, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655547, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655587, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655614, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655615, "dur": 26, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655643, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655645, "dur": 24, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655672, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655693, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655716, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655751, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655753, "dur": 19, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655775, "dur": 19, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655798, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655826, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655854, "dur": 103, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655962, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488655976, "dur": 318, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656298, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656333, "dur": 4, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656338, "dur": 21, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656361, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656363, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656377, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656391, "dur": 8, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656401, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656413, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656439, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656441, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656506, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656521, "dur": 13, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656538, "dur": 10, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656551, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656565, "dur": 10, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656577, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656597, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656599, "dur": 10, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656612, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656623, "dur": 9, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656635, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656672, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656686, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656688, "dur": 10, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656700, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656719, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656736, "dur": 9, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656748, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656758, "dur": 8, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656769, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656786, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656788, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656817, "dur": 10, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656830, "dur": 9, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656842, "dur": 9, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656853, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656870, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656873, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656890, "dur": 10, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656903, "dur": 9, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656914, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656935, "dur": 12, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656951, "dur": 9, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656963, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656973, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488656986, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657003, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657017, "dur": 11, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657030, "dur": 13, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657046, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657061, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657071, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657094, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657095, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657110, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657122, "dur": 15, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657139, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657141, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657153, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657155, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657179, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657192, "dur": 9, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657204, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657220, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657221, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657237, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657250, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657261, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657279, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657290, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657302, "dur": 9, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657314, "dur": 7, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657323, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657336, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657352, "dur": 10, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657366, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657380, "dur": 8, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657390, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657407, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657408, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657424, "dur": 9, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657437, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657448, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657465, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657478, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657496, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657515, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657517, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657531, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657560, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657573, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657575, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657607, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657617, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657636, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657638, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657653, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657675, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657692, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657693, "dur": 13, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657709, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657730, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657742, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657757, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657772, "dur": 8, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657783, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657801, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657803, "dur": 10, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657816, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657828, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657839, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657849, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488657870, "dur": 1649, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488659523, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488659526, "dur": 47, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488659575, "dur": 4, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488659581, "dur": 1222, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488660806, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488660808, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488660824, "dur": 856, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488661685, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488661700, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488661702, "dur": 2530, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488664236, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488664238, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488664255, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488664267, "dur": 901, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488665172, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488665185, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488665202, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488665214, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488665230, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488665241, "dur": 73, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488665318, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488665338, "dur": 842, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666189, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666214, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666237, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666240, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666265, "dur": 171, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666439, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666460, "dur": 134, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666597, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666611, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666703, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666716, "dur": 7, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666726, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666754, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488666767, "dur": 304, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667074, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667086, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667104, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667117, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667138, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667152, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667182, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667195, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667207, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667217, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667237, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667249, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667259, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667294, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667309, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667326, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667337, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667440, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667453, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667455, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667502, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667515, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667532, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667737, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667753, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667755, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667774, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667789, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667884, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667898, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667916, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667928, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667939, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667951, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667969, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488667980, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668090, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668110, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668179, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668190, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668267, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668278, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668316, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668331, "dur": 8, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668341, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668354, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668373, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668397, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668408, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668431, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668445, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668464, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668597, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668615, "dur": 13, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668632, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668644, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668797, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668874, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668885, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668947, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668957, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668974, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488668987, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669003, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669014, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669024, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669047, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669059, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669075, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669078, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669094, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669123, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669133, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669162, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669174, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669205, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669218, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669244, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669269, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669279, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669303, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669320, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669336, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669347, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669359, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669386, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669398, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669479, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669489, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669672, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669684, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669798, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669828, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669860, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669869, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669892, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669893, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669913, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669927, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669973, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488669989, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670035, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670052, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670103, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670116, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670218, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670230, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670270, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670284, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670293, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670324, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670335, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670451, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670453, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670466, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670476, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670524, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670535, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670560, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670575, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670632, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670634, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670665, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670687, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670701, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670713, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670733, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670745, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670799, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670813, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670823, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670842, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670854, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670953, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488670965, "dur": 664, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488671635, "dur": 31, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488671669, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488671671, "dur": 39, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488671718, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488671735, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488671755, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488671772, "dur": 349, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672125, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672138, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672169, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672187, "dur": 96, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672287, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672302, "dur": 300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672607, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672632, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672656, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672786, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672789, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488672803, "dur": 364, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488673171, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488673173, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488673197, "dur": 821, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488674023, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488674055, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488674058, "dur": 128003, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488802069, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488802072, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488802088, "dur": 1817, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488803908, "dur": 1901, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488805815, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488805818, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488805838, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488805850, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488805883, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488805906, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806061, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806063, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806080, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806207, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806223, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806313, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806328, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806430, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806443, "dur": 351, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806798, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488806813, "dur": 546, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488807364, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488807391, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488807557, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488807570, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488807715, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488807729, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488807796, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488807817, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488807948, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488807967, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488808179, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488808195, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488808215, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488808233, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488808425, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488808438, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488808473, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488808484, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488808550, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488808564, "dur": 534, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488809103, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488809105, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488809132, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488809134, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488809247, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488809259, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488809261, "dur": 714, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488809980, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488809995, "dur": 323, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810321, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810344, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810376, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810394, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810564, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810579, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810640, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810661, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810676, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810687, "dur": 252, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810943, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488810955, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811038, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811050, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811067, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811080, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811095, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811117, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811135, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811232, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811255, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811294, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811315, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811329, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811364, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811380, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811382, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811393, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811410, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811424, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811443, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811456, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811471, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811474, "dur": 510, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811988, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488811999, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812018, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812038, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812172, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812173, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812192, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812211, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812227, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812244, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812261, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812293, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812310, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812324, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812338, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812355, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812373, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812376, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812417, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812437, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812448, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812464, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812482, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812502, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812503, "dur": 16, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812522, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812535, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812537, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812556, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812572, "dur": 57, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812632, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812634, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812664, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812679, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812681, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812700, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812714, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812726, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812784, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812799, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812855, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812868, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812890, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812904, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812925, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488812986, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813014, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813034, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813055, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813086, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813106, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813132, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813133, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813161, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813185, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813186, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813265, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813291, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813293, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813317, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813350, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813353, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813490, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813492, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813514, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488813516, "dur": 42492, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488856018, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488856023, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488856061, "dur": 3562, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488859627, "dur": 33296, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488892930, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488892934, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488892967, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617488892971, "dur": 113254, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489006231, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489006235, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489006265, "dur": 26, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489006292, "dur": 4568, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489010865, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489010868, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489010891, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489010894, "dur": 1481, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489012380, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489012383, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489012429, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489012455, "dur": 34452, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489046915, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489046919, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489046935, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489046938, "dur": 500, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489047444, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489047447, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489047465, "dur": 51, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489047517, "dur": 846, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489048370, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489048424, "dur": 499, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749617489048926, "dur": 8426, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26084, "tid": 742, "ts": 1749617489070148, "dur": 1556, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26084, "tid": 8589934592, "ts": 1749617488609387, "dur": 49786, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": 1749617488659179, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": 1749617488659187, "dur": 1230, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26084, "tid": 742, "ts": 1749617489071706, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26084, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26084, "tid": 4294967296, "ts": 1749617488565791, "dur": 492894, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749617488568419, "dur": 34876, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749617489058714, "dur": 4061, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749617489061174, "dur": 106, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749617489062849, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26084, "tid": 742, "ts": 1749617489071711, "dur": 26, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749617488639556, "dur": 1529, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617488641093, "dur": 540, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617488641762, "dur": 66, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749617488641829, "dur": 410, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617488642964, "dur": 532, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_3AF52CA4203FDAC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749617488644221, "dur": 949, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749617488646996, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_C275F416F58A86FE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749617488642255, "dur": 13680, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617488655946, "dur": 391667, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617489047617, "dur": 343, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617489047960, "dur": 171, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617489048147, "dur": 57, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617489048329, "dur": 82, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617489048435, "dur": 1107, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749617488642657, "dur": 13309, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488656015, "dur": 326, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1749617488655968, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749617488656449, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EE2E1C75DDFB128E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749617488656534, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488656532, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749617488656876, "dur": 1258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488658134, "dur": 997, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749617488659132, "dur": 3644, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-environment-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488662889, "dur": 2221, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488665111, "dur": 741, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-string-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488659131, "dur": 6921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488666052, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488666315, "dur": 1105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488667421, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749617488667553, "dur": 549, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488667545, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749617488668388, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488668538, "dur": 689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488669231, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488669812, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488670433, "dur": 987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488671420, "dur": 134016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488805468, "dur": 878, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488806434, "dur": 1499, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488808306, "dur": 501, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488808899, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.Abstractions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488809591, "dur": 399, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488810095, "dur": 586, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488810693, "dur": 705, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488811448, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488811539, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488812075, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749617488805438, "dur": 6961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749617488812400, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488812658, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749617488812656, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749617488812726, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749617488812897, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749617488813610, "dur": 234073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488642704, "dur": 13278, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488656030, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1749617488655985, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21B473704285E949.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749617488656088, "dur": 701, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488656801, "dur": 1313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488658114, "dur": 640, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749617488658913, "dur": 3839, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.JSInterop.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617488658756, "dur": 4173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488662929, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488663143, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488663495, "dur": 2199, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1.1.8\\Editor\\Common\\InternalBridge\\TexturePlatformSettingsHelper.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749617488663362, "dur": 2415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488665777, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488666310, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488666578, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749617488666675, "dur": 2429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488669140, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617488669279, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617488669106, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749617488669734, "dur": 1052, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488670818, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749617488670927, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749617488671376, "dur": 132374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488805427, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617488803753, "dur": 2161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749617488805915, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488806340, "dur": 1687, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617488808512, "dur": 863, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617488809895, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617488810312, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749617488805992, "dur": 5171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749617488811164, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488811533, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488812129, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488812635, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749617488812635, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749617488812816, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749617488813250, "dur": 234416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488642873, "dur": 13258, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488656193, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488656192, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_37A60B8D84ADA88C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749617488656459, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488656736, "dur": 1378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488658115, "dur": 987, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1749617488659104, "dur": 860, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Connections.Abstractions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488660232, "dur": 3280, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488659104, "dur": 4465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488663569, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488663787, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488663990, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488664201, "dur": 1494, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Merge\\Developer\\DirectoryConflicts\\LoadedTwiceMenu.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749617488664200, "dur": 1678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488665878, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488666301, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488667300, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749617488667402, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749617488668422, "dur": 396, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488668196, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749617488668997, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488669095, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488669807, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488670440, "dur": 973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488671413, "dur": 134062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488805806, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488806339, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488807469, "dur": 1867, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488809351, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488810093, "dur": 424, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488810555, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488810676, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488805477, "dur": 5269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749617488811028, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488811380, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488811590, "dur": 491, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488812306, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.Abstractions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488812538, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Razor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749617488810798, "dur": 2728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749617488813529, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749617488813598, "dur": 234025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488642628, "dur": 13323, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488655973, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488656106, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488656370, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488656430, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_B7F0DFA76BCD38A9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617488656486, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749617488656485, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_66549C0209C53F09.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617488656737, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488657808, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488658079, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749617488658419, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488658641, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617488659055, "dur": 364, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749617488659540, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749617488659809, "dur": 899, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749617488661994, "dur": 675, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749617488662669, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Button.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749617488663392, "dur": 853, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749617488658743, "dur": 5521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749617488664265, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488664383, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488664582, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488664779, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488664964, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488665242, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488665572, "dur": 725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488666297, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488666579, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617488666679, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488666829, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749617488667143, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488667221, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488667290, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617488667404, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617488667549, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749617488667882, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488668053, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488668405, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488668578, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617488668716, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749617488669067, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488669196, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488669347, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488669444, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488670062, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617488670207, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749617488670527, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488670632, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488670846, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749617488670962, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749617488671298, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488671381, "dur": 132383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488803765, "dur": 2173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749617488805939, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488806417, "dur": 1843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749617488808261, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488810556, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\Editor\\DOTweenEditor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749617488808325, "dur": 2341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749617488810667, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488812095, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749617488810748, "dur": 2203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749617488812956, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749617488813213, "dur": 234444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488642774, "dur": 13238, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488656015, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5AA67F5B53C44BAD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617488656101, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488656432, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_DA132F3FE5E28708.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617488656498, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617488656492, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_20B214415B84035D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617488656702, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488657616, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488658110, "dur": 366, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749617488658477, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488659059, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488659844, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488660070, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488660272, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488660479, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488661091, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488661322, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488661560, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488662001, "dur": 677, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Lists\\ListContainsItem.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749617488661849, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488662755, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488662961, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488663158, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488663366, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488663595, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488663802, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488664009, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488664229, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488664485, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488664677, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488664867, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488665065, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488665233, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488665373, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488665894, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488666295, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488666704, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617488666824, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749617488667219, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488667323, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617488667428, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749617488667831, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488667908, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749617488668041, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488668409, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488668577, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617488668812, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617488668750, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749617488669225, "dur": 670, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488669901, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488670068, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488670879, "dur": 1863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488672744, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749617488672892, "dur": 132540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488805436, "dur": 1960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749617488807397, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488807471, "dur": 382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617488809591, "dur": 319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749617488807470, "dur": 2901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749617488810371, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488810429, "dur": 2087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749617488812517, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488812687, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749617488812819, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749617488813279, "dur": 234351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488642827, "dur": 13242, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488656110, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488656390, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488656488, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749617488656487, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_6C566B45F0C804AD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617488656729, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488656861, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488657000, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488657056, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488657402, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488657465, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488657806, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488658024, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749617488658096, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749617488658429, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488659063, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488660425, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488660666, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488660964, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488661190, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488661407, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488661621, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488662001, "dur": 674, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\TernaryExpression.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749617488661861, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488662968, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488663174, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488663496, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\NativeHashSet.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749617488663377, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488664142, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488664389, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488664586, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488664781, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488664979, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488665196, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488665498, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488665801, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488666296, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488666699, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617488666788, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488667249, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\PickingTool.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749617488666860, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617488667628, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488668055, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488668425, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488668575, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617488669138, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\FixedStringParseMethods.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749617488668705, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617488669255, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488669353, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617488669800, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Mathematics.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749617488669493, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617488669887, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488670073, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488670878, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488671737, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749617488671862, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749617488672156, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488672272, "dur": 131484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488803757, "dur": 2043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749617488805801, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488805891, "dur": 1858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749617488807749, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488807819, "dur": 2041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749617488809861, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488810622, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749617488810084, "dur": 2214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749617488812298, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488812404, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488812786, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749617488813022, "dur": 234588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488642646, "dur": 13315, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488655973, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488656097, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488656453, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749617488656522, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1749617488656452, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_038A17E6D6B77576.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617488657281, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488657404, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488657614, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488657896, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488658095, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488658238, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16942202937359522267.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749617488658437, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488658733, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488659564, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488659807, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488660051, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488660255, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488660454, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488660674, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488660906, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488661148, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488661357, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488661577, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488662000, "dur": 672, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Control\\SelectUnit_T.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749617488661806, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488662720, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488662931, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488663137, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488663346, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488663573, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488663788, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488664002, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488664200, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488664426, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488664618, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488664807, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488664999, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488665272, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488665462, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488665935, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488666299, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488666573, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617488666703, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749617488667133, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488667226, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617488667363, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749617488667728, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488667849, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488667997, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488668060, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488668421, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488668576, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617488668728, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749617488669157, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488669364, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749617488669800, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749617488669506, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749617488669873, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488669966, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488670063, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488670883, "dur": 132871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488803755, "dur": 2049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749617488805804, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488806167, "dur": 1795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749617488807963, "dur": 688, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488810675, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749617488808657, "dur": 2097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749617488810763, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488811051, "dur": 1938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749617488812990, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749617488813143, "dur": 234531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488642676, "dur": 13295, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488655983, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_094A556A01D249DF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617488656078, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488656512, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488656796, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488656966, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488657135, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488657198, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749617488657307, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488657622, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488657798, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488658002, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11344994280883157806.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749617488658098, "dur": 902, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488659393, "dur": 3288, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Ini.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617488662719, "dur": 1519, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.CommandLine.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617488659004, "dur": 5479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488664483, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488664693, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488664897, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488665148, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488665491, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488666016, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488666294, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488666708, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617488666821, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617488667221, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488667346, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617488667482, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488667561, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617488668396, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488668473, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488668537, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488668592, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488669189, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617488669303, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488669802, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\BoltProduct.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749617488669370, "dur": 1228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617488670598, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488670682, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488670738, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488670874, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488670954, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617488671074, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617488671647, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488671734, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617488671839, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617488672292, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617488672387, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617488672648, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488672740, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749617488672894, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617488673201, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488673268, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617488674142, "dur": 181998, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749617488857147, "dur": 32732, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617488857145, "dur": 33908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749617488892630, "dur": 333, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749617488893001, "dur": 113284, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749617489010815, "dur": 36119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617489010814, "dur": 36124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749617489046965, "dur": 578, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617488642696, "dur": 13281, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488656032, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1749617488655979, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_699CA9E1D209A267.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617488656182, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617488656180, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_EF9DC8C923ADA8E7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617488656494, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488656719, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488656797, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488657168, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749617488657223, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488657693, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488658099, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3043913547040211301.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749617488658450, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488659728, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488659947, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488660152, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488660357, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488660558, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488660785, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488660983, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488661208, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488661449, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488661667, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488662005, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Connections\\UnitRelation.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749617488662734, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Connections\\IUnitRelation.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749617488661888, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488663459, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488663689, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488663899, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488664221, "dur": 1360, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_CoroutineTween.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749617488664102, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488665673, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488666296, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488666590, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617488666696, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488667404, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617488667244, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749617488667951, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488668076, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617488668287, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749617488668696, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488668792, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488668981, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488669355, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617488669807, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617488669585, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749617488669997, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488670078, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488670875, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488671340, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749617488671461, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749617488671751, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488671846, "dur": 131913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488803760, "dur": 2110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749617488805870, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488807475, "dur": 389, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617488807924, "dur": 395, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617488808517, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617488805949, "dur": 3010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749617488808960, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488810623, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749617488809201, "dur": 2081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749617488811338, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488811578, "dur": 886, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488812701, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749617488812832, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749617488813386, "dur": 234302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488642729, "dur": 13259, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488656055, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1749617488655991, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749617488656317, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488656439, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B2BE6A90D4C71D9E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749617488656522, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488656741, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488656935, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749617488657651, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488658097, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749617488658424, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488659004, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488659533, "dur": 1168, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488660701, "dur": 2243, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.AccessControl.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488662982, "dur": 1241, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488664223, "dur": 1469, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488658489, "dur": 7416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488665905, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488666312, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488666574, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749617488666676, "dur": 1201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488668086, "dur": 320, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488667881, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749617488668958, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488669187, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749617488669252, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488669946, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Graph\\GetGraphs.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749617488669463, "dur": 1097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749617488670560, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488670952, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749617488671064, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749617488671477, "dur": 132567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488804514, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488804768, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-datetime-l1-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488805473, "dur": 2006, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488804045, "dur": 3724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749617488807769, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488808309, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488807823, "dur": 1759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749617488809583, "dur": 1178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749617488811027, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488812307, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488812665, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749617488810766, "dur": 2440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749617488813272, "dur": 234366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488642768, "dur": 13226, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488655997, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0998A984E55471CE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749617488656203, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1749617488656188, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_AC9CD2324DD83C0A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749617488656345, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488656485, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617488656484, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_743DCD7F83EC4AAC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749617488656600, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488657213, "dur": 814, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488658102, "dur": 368, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13445476680238899994.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749617488658471, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Metadata.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617488659033, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617488658471, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488659814, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488659948, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488660076, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488660188, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488660313, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488660441, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488660587, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488660739, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488660869, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488661016, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488661156, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488661299, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488661452, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488661598, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488661768, "dur": 912, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics2D\\OnCollisionStay2D.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749617488661754, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488662937, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugins\\PluginMigration.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749617488662809, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488663530, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488663749, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488663953, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488664156, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488664404, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488664592, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488664786, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488664977, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488665568, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488666306, "dur": 1116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488667423, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749617488667525, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488667891, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749617488668090, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617488668813, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617488667992, "dur": 1355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749617488669347, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488669431, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488670168, "dur": 1028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488671196, "dur": 132564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488805427, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617488803761, "dur": 2252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749617488806014, "dur": 1875, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488807928, "dur": 391, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617488808511, "dur": 460, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617488810093, "dur": 536, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617488810676, "dur": 360, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749617488807899, "dur": 3198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749617488811098, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488811181, "dur": 1094, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488812287, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488812665, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488812795, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488813093, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749617488813163, "dur": 234516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488642797, "dur": 13248, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488656047, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_006DC43AA5DD858A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749617488656174, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488656458, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488656457, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_5A19F49AB3243238.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749617488656590, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488657823, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488658127, "dur": 979, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749617488659108, "dur": 1389, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488659107, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488660829, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488661031, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488661249, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488661472, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488661679, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488661997, "dur": 665, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\FlowGraph.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749617488662663, "dur": 855, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Flow.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749617488661858, "dur": 1660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488663518, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488663734, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488663957, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488664223, "dur": 1416, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\CoverageFormats\\OpenCover\\Model\\Class.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749617488664156, "dur": 1566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488665722, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488666311, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488666575, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749617488666673, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488666815, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749617488667122, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488667216, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749617488667625, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749617488667985, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488668071, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488668410, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488668822, "dur": 994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488669816, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488670063, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749617488670146, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749617488670399, "dur": 1042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488671441, "dur": 132326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488803769, "dur": 1613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749617488805383, "dur": 2271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488808134, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488807660, "dur": 1514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749617488809351, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488810093, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488810392, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-sysinfo-l1-1-0.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488810744, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.DependencyInjection.Abstractions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488811027, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.TypeExtensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488811449, "dur": 683, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488812454, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488812752, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749617488809207, "dur": 3715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749617488812923, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749617488813012, "dur": 234596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488642827, "dur": 13247, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488656315, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488656781, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F45A17676A5D3A15.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749617488656980, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488657279, "dur": 4503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488661818, "dur": 1695, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\PointerEventUnit.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749617488661789, "dur": 1845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488663634, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488663773, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488663907, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488664049, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488664201, "dur": 1497, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\PendingChanges\\Dialogs\\LaunchDependenciesDialog.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749617488664189, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488665812, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488666305, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488666700, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749617488666811, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749617488667157, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488667214, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749617488667885, "dur": 843, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617488668813, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager@2.0.1\\Editor\\UserSettingsRepository.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749617488667630, "dur": 1474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749617488669104, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488669168, "dur": 1219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488670389, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488671448, "dur": 133989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488806340, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617488806698, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617488806800, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617488805440, "dur": 1801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749617488807242, "dur": 1331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488809008, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617488809350, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617488809895, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.DataAnnotations.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617488810693, "dur": 538, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Unity.ILPP.Runner.exe"}}, {"pid": 12345, "tid": 13, "ts": 1749617488811299, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617488811388, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617488812075, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749617488808580, "dur": 3819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749617488812454, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749617488812453, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749617488812672, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749617488812797, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749617488813152, "dur": 234518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488642848, "dur": 13260, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488656495, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488656494, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_425C305CB5B8A325.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749617488656591, "dur": 1269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488658108, "dur": 430, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_425C305CB5B8A325.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749617488658545, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749617488658886, "dur": 1359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488660474, "dur": 3765, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488665084, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector2EqualityComparer.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749617488658642, "dur": 6566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749617488665209, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488665315, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749617488665560, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488665766, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488665425, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749617488666361, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749617488666612, "dur": 817, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488667552, "dur": 540, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488668398, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Runtime\\SafeStringArrayHelper.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749617488667433, "dur": 1055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749617488668623, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488668835, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488669088, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488668719, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749617488669229, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488669432, "dur": 809, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488669320, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1749617488670621, "dur": 109, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488671077, "dur": 131092, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1749617488805000, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-namedpipe-l1-1-0.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488803751, "dur": 2732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749617488806532, "dur": 1677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749617488808209, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488810029, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749617488808528, "dur": 1992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749617488810520, "dur": 986, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488811567, "dur": 767, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488812355, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488812888, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749617488813431, "dur": 234188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488642892, "dur": 13305, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488656308, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488656307, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_B5228D2EF183D9AF.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749617488656501, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488656905, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488657615, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488658075, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749617488658437, "dur": 1314, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488659935, "dur": 2821, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488658437, "dur": 4426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488662863, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488663074, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488663269, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488663495, "dur": 2101, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Utilities\\CoroutineRunner.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749617488663484, "dur": 2298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488665782, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488666312, "dur": 1112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488667553, "dur": 540, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488667424, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749617488668317, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488668382, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488668723, "dur": 1306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488670029, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488670423, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488671434, "dur": 132318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488805467, "dur": 884, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488806435, "dur": 1422, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488807924, "dur": 1409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488809356, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488803763, "dur": 6274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749617488810093, "dur": 486, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488810622, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488810938, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488811212, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488812311, "dur": 365, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749617488810089, "dur": 2952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749617488813095, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749617488813213, "dur": 234449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488642923, "dur": 13288, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488656388, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488656499, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488656631, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749617488656734, "dur": 1378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488658113, "dur": 378, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749617488658492, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488659710, "dur": 850, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.TagHelpers.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617488659014, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488660573, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488660763, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488661088, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488661305, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488661541, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488661998, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics2D\\OnTriggerStay2D.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749617488661751, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488662684, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488663093, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488663240, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488663498, "dur": 1974, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\BurstCompatibleAttribute.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749617488663397, "dur": 2105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488665573, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488666307, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488666574, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749617488666679, "dur": 1757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488668817, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617488669084, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\Interface\\ITexture.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749617488668439, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749617488669296, "dur": 737, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488670077, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749617488670322, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749617488670759, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488670905, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749617488671331, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749617488671398, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749617488671634, "dur": 132132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488806159, "dur": 1321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617488803766, "dur": 3853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749617488807620, "dur": 3520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488811228, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488811541, "dur": 774, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617488811540, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749617488812316, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488812659, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617488812961, "dur": 197855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749617489010819, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749617489010818, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749617489010948, "dur": 1529, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749617489012486, "dur": 35129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488642958, "dur": 13398, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488656373, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488656439, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_270C5DEE22A99EDB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749617488656530, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617488656529, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749617488656609, "dur": 1067, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488657810, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488658231, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749617488658377, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14355918427286719942.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749617488658460, "dur": 973, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488659436, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488659708, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488659866, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488659997, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488660132, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488660250, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488660399, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488660546, "dur": 4474, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488665020, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488665169, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488665351, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488665861, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488666301, "dur": 1565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488667866, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749617488668184, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488668463, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488668715, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488669439, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488670149, "dur": 1061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488671210, "dur": 132547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488803759, "dur": 1950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749617488805710, "dur": 1187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488807473, "dur": 458, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617488808306, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.WebUtilities.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617488806908, "dur": 2580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749617488809489, "dur": 988, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749617488810675, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617488811028, "dur": 521, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617488812076, "dur": 310, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Mail.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617488812387, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617488812536, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749617488810482, "dur": 2693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749617488813251, "dur": 234402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488642982, "dur": 13414, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488656536, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488656670, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749617488656796, "dur": 1067, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488658017, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749617488658086, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749617488658285, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488658859, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488659422, "dur": 1346, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Xml.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488658438, "dur": 2441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488660879, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488661033, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488661177, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488661322, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488661462, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488661609, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488661755, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488662001, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\Sidebars\\Sidebar.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749617488661913, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488662785, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488662988, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488663195, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488663499, "dur": 2084, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Variables\\Variables.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749617488663407, "dur": 2230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488665637, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488666303, "dur": 1120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488667424, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749617488667486, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488667613, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749617488667853, "dur": 647, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488668509, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488668719, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488669438, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488670156, "dur": 1047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488671203, "dur": 132560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488804988, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488805428, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488803763, "dur": 2318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749617488806081, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488806331, "dur": 1764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749617488808096, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488808306, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488808512, "dur": 890, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488808285, "dur": 2324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749617488810610, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488811028, "dur": 368, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488811455, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488812307, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.Xml.Linq.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488812634, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488812751, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749617488810672, "dur": 2633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749617488813306, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749617488813384, "dur": 234258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488643005, "dur": 13484, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488656505, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488657487, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488657778, "dur": 977, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488658890, "dur": 604, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Embedded.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617488658757, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488659736, "dur": 2097, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\ProjectGeneration\\FileIOProvider.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749617488659635, "dur": 2328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488661999, "dur": 3114, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\BackupWindow\\BackupPage.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749617488661963, "dur": 3311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488665274, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488665491, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488665967, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488666316, "dur": 939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488667256, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749617488667416, "dur": 664, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617488668707, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\PendingChanges\\PendingMergeLinks\\MergeLinksListView.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749617488667359, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749617488668862, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488669119, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749617488669186, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488669432, "dur": 603, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617488669268, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749617488670160, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488670558, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488670846, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749617488670916, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749617488671183, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488671754, "dur": 132613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488804396, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617488805427, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.AccessControl.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617488805795, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipes.AccessControl.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617488804368, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749617488806778, "dur": 1243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488808027, "dur": 1852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749617488809880, "dur": 1359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488811243, "dur": 1047, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488812347, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488812533, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488812674, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749617488812813, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749617488813225, "dur": 234423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488643027, "dur": 13494, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488656523, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488657092, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488657674, "dur": 3162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488660854, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488661124, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488661336, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488661569, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488662004, "dur": 1511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Application\\OnApplicationPause.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749617488661795, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488663527, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488663741, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488663945, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488664157, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488664392, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488664581, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488664782, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488665071, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488665139, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488665350, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488665634, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488666311, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488666577, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749617488666679, "dur": 2773, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488669454, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749617488669784, "dur": 641, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488670431, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488671427, "dur": 132349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488804552, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.Abstractions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488804782, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.ViewFeatures.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488805146, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.ConfigurationExtensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488803777, "dur": 2474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749617488806252, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488806339, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488807845, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488808306, "dur": 795, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488806312, "dur": 2902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749617488809214, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488809590, "dur": 517, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488810311, "dur": 320, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488810693, "dur": 343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488811112, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488811522, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Metadata.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488812075, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Loader.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488812467, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488812665, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749617488809354, "dur": 3987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749617488813341, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749617488813422, "dur": 234212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749617489053751, "dur": 3038, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26084, "tid": 742, "ts": 1749617489072289, "dur": 3540, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26084, "tid": 742, "ts": 1749617489075906, "dur": 2715, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26084, "tid": 742, "ts": 1749617489068185, "dur": 11066, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}