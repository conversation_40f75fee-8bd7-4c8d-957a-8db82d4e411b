{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 16392, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 16392, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 16392, "tid": 19, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 16392, "tid": 19, "ts": 1749548039508255, "dur": 15, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 16392, "tid": 19, "ts": 1749548039508282, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 16392, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 16392, "tid": 1, "ts": 1749548039172781, "dur": 2295, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 16392, "tid": 1, "ts": 1749548039175079, "dur": 14279, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 16392, "tid": 1, "ts": 1749548039189361, "dur": 23220, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 16392, "tid": 19, "ts": 1749548039508287, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 16392, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039172746, "dur": 28069, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039200818, "dur": 306980, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039201314, "dur": 33, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039201350, "dur": 6, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039201357, "dur": 2646, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204006, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204008, "dur": 150, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204161, "dur": 11, "ph": "X", "name": "ProcessMessages 18715", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204174, "dur": 26, "ph": "X", "name": "ReadAsync 18715", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204203, "dur": 3, "ph": "X", "name": "ProcessMessages 1619", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204207, "dur": 22, "ph": "X", "name": "ReadAsync 1619", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204236, "dur": 24, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204267, "dur": 17, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204286, "dur": 3, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204290, "dur": 33, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204329, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204354, "dur": 3, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204359, "dur": 21, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204386, "dur": 27, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204414, "dur": 4, "ph": "X", "name": "ProcessMessages 1101", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204419, "dur": 43, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204464, "dur": 4, "ph": "X", "name": "ProcessMessages 1621", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204469, "dur": 20, "ph": "X", "name": "ReadAsync 1621", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204888, "dur": 5, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204894, "dur": 102, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039204999, "dur": 8, "ph": "X", "name": "ProcessMessages 10614", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205009, "dur": 39, "ph": "X", "name": "ReadAsync 10614", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205049, "dur": 1, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205051, "dur": 32, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205085, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205087, "dur": 27, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205117, "dur": 3, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205122, "dur": 23, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205146, "dur": 4, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205151, "dur": 26, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205179, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205184, "dur": 38, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205228, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205250, "dur": 22, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205274, "dur": 4, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205278, "dur": 20, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205301, "dur": 4, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205306, "dur": 19, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205327, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205331, "dur": 26, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205359, "dur": 4, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205364, "dur": 26, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205391, "dur": 3, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205396, "dur": 38, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205439, "dur": 490, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205935, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039205937, "dur": 136, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206075, "dur": 11, "ph": "X", "name": "ProcessMessages 15126", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206088, "dur": 24, "ph": "X", "name": "ReadAsync 15126", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206115, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206120, "dur": 23, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206145, "dur": 4, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206150, "dur": 17, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206169, "dur": 3, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206173, "dur": 34, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206213, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206236, "dur": 1, "ph": "X", "name": "ProcessMessages 892", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206240, "dur": 30, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206273, "dur": 1, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206274, "dur": 24, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206300, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206302, "dur": 31, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206335, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206336, "dur": 28, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206366, "dur": 1, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039206371, "dur": 1317, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207691, "dur": 9, "ph": "X", "name": "ProcessMessages 20522", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207702, "dur": 25, "ph": "X", "name": "ReadAsync 20522", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207729, "dur": 4, "ph": "X", "name": "ProcessMessages 1133", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207734, "dur": 15, "ph": "X", "name": "ReadAsync 1133", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207751, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207756, "dur": 33, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207792, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207824, "dur": 3, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207828, "dur": 24, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207855, "dur": 3, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207859, "dur": 20, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207886, "dur": 20, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207907, "dur": 4, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207912, "dur": 25, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207939, "dur": 4, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207943, "dur": 16, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207961, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207966, "dur": 14, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039207986, "dur": 19, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208007, "dur": 4, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208012, "dur": 19, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208033, "dur": 4, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208037, "dur": 18, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208058, "dur": 3, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208063, "dur": 18, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208083, "dur": 3, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208087, "dur": 22, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208111, "dur": 4, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208116, "dur": 16, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208134, "dur": 3, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208138, "dur": 16, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208156, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208160, "dur": 106, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208269, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208273, "dur": 38, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208313, "dur": 5, "ph": "X", "name": "ProcessMessages 3248", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208319, "dur": 18, "ph": "X", "name": "ReadAsync 3248", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208338, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208342, "dur": 20, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208364, "dur": 3, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208368, "dur": 20, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208390, "dur": 3, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208395, "dur": 17, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208414, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208418, "dur": 23, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208443, "dur": 3, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208447, "dur": 19, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208468, "dur": 4, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208473, "dur": 17, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208496, "dur": 16, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208513, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208518, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208537, "dur": 4, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208542, "dur": 23, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208567, "dur": 4, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208572, "dur": 19, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208592, "dur": 4, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208597, "dur": 18, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208617, "dur": 4, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208622, "dur": 21, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208644, "dur": 4, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208649, "dur": 17, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208667, "dur": 3, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208672, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208694, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208716, "dur": 3, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208721, "dur": 19, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208742, "dur": 3, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208747, "dur": 19, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208768, "dur": 4, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208773, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208795, "dur": 3, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208799, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208819, "dur": 4, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208824, "dur": 19, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208844, "dur": 4, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208849, "dur": 16, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208867, "dur": 3, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208871, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208890, "dur": 3, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208895, "dur": 26, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208923, "dur": 18, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208942, "dur": 3, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208947, "dur": 18, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208967, "dur": 3, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208972, "dur": 25, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039208999, "dur": 4, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209003, "dur": 18, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209023, "dur": 3, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209028, "dur": 14, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209048, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209070, "dur": 4, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209075, "dur": 21, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209098, "dur": 4, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209102, "dur": 18, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209122, "dur": 3, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209126, "dur": 20, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209148, "dur": 4, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209153, "dur": 20, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209174, "dur": 4, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209179, "dur": 18, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209198, "dur": 3, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209203, "dur": 16, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209220, "dur": 3, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209225, "dur": 20, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209246, "dur": 3, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209251, "dur": 19, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209272, "dur": 3, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209277, "dur": 20, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209298, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209303, "dur": 20, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209325, "dur": 3, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209329, "dur": 23, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209354, "dur": 4, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209359, "dur": 19, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209379, "dur": 3, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209384, "dur": 16, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209405, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209426, "dur": 3, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209431, "dur": 23, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209455, "dur": 4, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209460, "dur": 19, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209481, "dur": 4, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209486, "dur": 19, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209521, "dur": 24, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209548, "dur": 4, "ph": "X", "name": "ProcessMessages 1346", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209553, "dur": 16, "ph": "X", "name": "ReadAsync 1346", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209571, "dur": 5, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209577, "dur": 14, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209593, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209597, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209619, "dur": 3, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209623, "dur": 19, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209644, "dur": 4, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209649, "dur": 18, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209669, "dur": 3, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209674, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209694, "dur": 3, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209699, "dur": 27, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209728, "dur": 3, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209732, "dur": 18, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209752, "dur": 3, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209756, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209784, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209804, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209809, "dur": 19, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209830, "dur": 3, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209835, "dur": 19, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209855, "dur": 4, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209860, "dur": 17, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209878, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209883, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209904, "dur": 3, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209909, "dur": 20, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209930, "dur": 3, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209935, "dur": 14, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209955, "dur": 16, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209973, "dur": 3, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209977, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039209998, "dur": 4, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210003, "dur": 28, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210033, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210036, "dur": 34, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210072, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210073, "dur": 21, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210098, "dur": 27, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210126, "dur": 4, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210132, "dur": 18, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210151, "dur": 3, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210156, "dur": 42, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210204, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210223, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210227, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210250, "dur": 4, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210255, "dur": 19, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210275, "dur": 4, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210280, "dur": 22, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210304, "dur": 4, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210309, "dur": 20, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210330, "dur": 4, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210335, "dur": 27, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210364, "dur": 3, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210368, "dur": 19, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210389, "dur": 3, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210393, "dur": 15, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210410, "dur": 3, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210415, "dur": 31, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210451, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210471, "dur": 3, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210476, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210496, "dur": 3, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210500, "dur": 15, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210518, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210522, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210538, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210559, "dur": 3, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210564, "dur": 18, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210583, "dur": 3, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210587, "dur": 34, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210627, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210648, "dur": 4, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210652, "dur": 18, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210673, "dur": 3, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210677, "dur": 25, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210708, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210728, "dur": 3, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210733, "dur": 15, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210750, "dur": 3, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210754, "dur": 16, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210771, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210776, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210807, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210827, "dur": 3, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210832, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210851, "dur": 4, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210856, "dur": 25, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210886, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210906, "dur": 3, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210911, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210932, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210937, "dur": 24, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210967, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210987, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039210992, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211012, "dur": 3, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211016, "dur": 22, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211044, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211066, "dur": 3, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211070, "dur": 18, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211090, "dur": 3, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211095, "dur": 47, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211147, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211168, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211172, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211192, "dur": 3, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211197, "dur": 23, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211225, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211245, "dur": 3, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211250, "dur": 18, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211270, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211274, "dur": 22, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211302, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211324, "dur": 3, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211329, "dur": 18, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211348, "dur": 3, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211353, "dur": 22, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211380, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211401, "dur": 4, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211406, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211428, "dur": 3, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211432, "dur": 41, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211479, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211499, "dur": 3, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211504, "dur": 19, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211524, "dur": 3, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211529, "dur": 23, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211558, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211578, "dur": 3, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211583, "dur": 19, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211603, "dur": 3, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211607, "dur": 22, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211636, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211656, "dur": 3, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211661, "dur": 23, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211686, "dur": 23, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211714, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211735, "dur": 3, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211739, "dur": 16, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211757, "dur": 3, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211761, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211780, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211784, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211811, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211835, "dur": 3, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211839, "dur": 18, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211859, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211863, "dur": 22, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211891, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211913, "dur": 3, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211917, "dur": 20, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211939, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211943, "dur": 23, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211972, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211994, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039211995, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212019, "dur": 3, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212024, "dur": 21, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212050, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212071, "dur": 3, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212075, "dur": 19, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212096, "dur": 4, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212101, "dur": 44, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212150, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212171, "dur": 3, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212175, "dur": 18, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212195, "dur": 4, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212200, "dur": 22, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212225, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212248, "dur": 3, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212253, "dur": 19, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212273, "dur": 3, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212278, "dur": 21, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212302, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212325, "dur": 4, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212329, "dur": 18, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212349, "dur": 3, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212354, "dur": 20, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212380, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212403, "dur": 3, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212408, "dur": 18, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212427, "dur": 3, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212431, "dur": 42, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212475, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212480, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212509, "dur": 3, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212513, "dur": 16, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212531, "dur": 3, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212536, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212561, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212582, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212584, "dur": 27, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212614, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212616, "dur": 20, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212637, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212656, "dur": 3, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212660, "dur": 48, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212711, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212715, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212735, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212740, "dur": 24, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212766, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212768, "dur": 24, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212794, "dur": 24, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212821, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212847, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212852, "dur": 17, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212874, "dur": 14, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212891, "dur": 3, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212895, "dur": 20, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212921, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212944, "dur": 3, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212949, "dur": 17, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212967, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039212972, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213000, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213021, "dur": 3, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213026, "dur": 23, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213052, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213054, "dur": 25, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213081, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213114, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213116, "dur": 26, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213144, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213145, "dur": 47, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213195, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213224, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213226, "dur": 24, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213251, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213252, "dur": 25, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213280, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213309, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213310, "dur": 29, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213341, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213343, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213365, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213393, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213395, "dur": 26, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213424, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213449, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213481, "dur": 19, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213505, "dur": 19, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213526, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213575, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213606, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213608, "dur": 26, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213636, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213638, "dur": 51, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213691, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213723, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213725, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213746, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213748, "dur": 23, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213778, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213800, "dur": 4, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213805, "dur": 18, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213825, "dur": 3, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213830, "dur": 27, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213863, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213888, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213890, "dur": 24, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213915, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213917, "dur": 30, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213948, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213953, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213971, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213977, "dur": 16, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039213999, "dur": 20, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214020, "dur": 4, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214025, "dur": 23, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214050, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214055, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214080, "dur": 4, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214085, "dur": 19, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214106, "dur": 4, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214111, "dur": 19, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214132, "dur": 3, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214136, "dur": 17, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214155, "dur": 10, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214166, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214196, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214211, "dur": 14, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214227, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214240, "dur": 10, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214252, "dur": 18, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214272, "dur": 13, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214286, "dur": 11, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214298, "dur": 10, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214310, "dur": 10, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214322, "dur": 9, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214332, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214343, "dur": 32, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214376, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214391, "dur": 12, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214405, "dur": 13, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214419, "dur": 8, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214429, "dur": 29, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214459, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214471, "dur": 19, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214491, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214504, "dur": 11, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214517, "dur": 9, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214529, "dur": 32, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214562, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214582, "dur": 11, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214594, "dur": 10, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214606, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214641, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214657, "dur": 17, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214675, "dur": 10, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214687, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214723, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214736, "dur": 18, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214756, "dur": 11, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214768, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214802, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214814, "dur": 18, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214834, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214846, "dur": 12, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214859, "dur": 10, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214870, "dur": 33, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214904, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214930, "dur": 10, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214942, "dur": 9, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214953, "dur": 31, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214985, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039214997, "dur": 13, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215012, "dur": 11, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215024, "dur": 9, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215034, "dur": 30, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215066, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215079, "dur": 32, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215113, "dur": 25, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215139, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215159, "dur": 10, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215170, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215195, "dur": 17, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215214, "dur": 10, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215226, "dur": 15, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215244, "dur": 12, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215258, "dur": 9, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215268, "dur": 9, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215279, "dur": 9, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215289, "dur": 31, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215321, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215334, "dur": 12, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215347, "dur": 10, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215358, "dur": 10, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215370, "dur": 11, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215382, "dur": 10, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215394, "dur": 20, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215416, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215428, "dur": 10, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215440, "dur": 10, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215451, "dur": 13, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215465, "dur": 8, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215475, "dur": 30, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215507, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215518, "dur": 80, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215601, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215623, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215652, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215670, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215685, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215687, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215709, "dur": 195, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215906, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215908, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215953, "dur": 3, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215957, "dur": 14, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039215974, "dur": 464, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216440, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216505, "dur": 14, "ph": "X", "name": "ProcessMessages 2924", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216520, "dur": 33, "ph": "X", "name": "ReadAsync 2924", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216553, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216555, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216575, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216577, "dur": 14, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216592, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216610, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216612, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216630, "dur": 15, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216649, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216666, "dur": 14, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216683, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216685, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216704, "dur": 19, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216726, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216743, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216760, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216777, "dur": 13, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216793, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216813, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216816, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039216837, "dur": 184, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039217025, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039217056, "dur": 2, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039217059, "dur": 68, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039217133, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039217158, "dur": 201, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039217362, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039217371, "dur": 2959, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039220334, "dur": 444, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039220781, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039220784, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039220809, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039220813, "dur": 1151, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039221967, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039221969, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039221993, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039221996, "dur": 440, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039222441, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039222470, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039222490, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039222492, "dur": 17, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039222513, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039222702, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039222725, "dur": 2661, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225391, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225441, "dur": 6, "ph": "X", "name": "ProcessMessages 1272", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225449, "dur": 288, "ph": "X", "name": "ReadAsync 1272", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225741, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225773, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225776, "dur": 23, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225803, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225821, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225845, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225846, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225865, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225936, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225950, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225952, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225970, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225984, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225986, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039225999, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226027, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226041, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226042, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226064, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226083, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226104, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226126, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226150, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226152, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226176, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226178, "dur": 35, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226217, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226234, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226333, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226358, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226458, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226474, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226506, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226532, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226557, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226582, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226677, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226699, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226701, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226723, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226750, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226783, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226800, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226820, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226879, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226899, "dur": 33, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226934, "dur": 16, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039226953, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227016, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227050, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227052, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227111, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227137, "dur": 18, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227157, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227217, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227237, "dur": 25, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227263, "dur": 37, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227303, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227330, "dur": 16, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227348, "dur": 9, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227358, "dur": 16, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227376, "dur": 4, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227381, "dur": 15, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227399, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227481, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227500, "dur": 5, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227506, "dur": 15, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227522, "dur": 33, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227557, "dur": 115, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227676, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227695, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227705, "dur": 17, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227726, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227837, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227858, "dur": 7, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227867, "dur": 18, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227886, "dur": 5, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227893, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227912, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227914, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227940, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039227967, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228137, "dur": 7, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228145, "dur": 35, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228184, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228209, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228211, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228234, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228237, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228319, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228353, "dur": 271, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228633, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228658, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228758, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228783, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228785, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228819, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039228843, "dur": 169, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229014, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229035, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229058, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229081, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229111, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229134, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229251, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229272, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229327, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229346, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229384, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229402, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229483, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229503, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229641, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229659, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039229661, "dur": 86854, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039316529, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039316534, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039316568, "dur": 27, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039316596, "dur": 5723, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039322324, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039322326, "dur": 1291, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039323623, "dur": 6, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039323631, "dur": 28, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039323662, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039323664, "dur": 3377, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039327045, "dur": 384, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039327432, "dur": 5, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039327439, "dur": 568, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039328010, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039328013, "dur": 160, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039328177, "dur": 3, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039328181, "dur": 471, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039328655, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039328658, "dur": 32, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039328848, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039328851, "dur": 190, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039329045, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039329048, "dur": 247, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039329300, "dur": 131, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039329482, "dur": 53, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039329538, "dur": 1016, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039330561, "dur": 35, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039330598, "dur": 94, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039330696, "dur": 124, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039330822, "dur": 1664, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039332495, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039332498, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039332578, "dur": 46, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039332625, "dur": 158, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039332787, "dur": 48, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039332837, "dur": 40, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039332880, "dur": 448, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039333331, "dur": 113, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039333448, "dur": 48, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039333498, "dur": 269, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039333770, "dur": 16, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039333787, "dur": 30, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039333819, "dur": 72, "ph": "X", "name": "ProcessMessages 158", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039333893, "dur": 162, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039334059, "dur": 21, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039334081, "dur": 388, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039334474, "dur": 207, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039334685, "dur": 21, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039334708, "dur": 49, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039334761, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039334764, "dur": 656, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039335430, "dur": 249, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039335685, "dur": 33, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039335720, "dur": 6755, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039342510, "dur": 12, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039342527, "dur": 948, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039343488, "dur": 156, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039343646, "dur": 4793, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039348480, "dur": 698, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039349259, "dur": 4663, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039353940, "dur": 149, "ph": "X", "name": "ProcessMessages 1168", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039354092, "dur": 3042, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039357154, "dur": 157, "ph": "X", "name": "ProcessMessages 1416", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039357313, "dur": 118, "ph": "X", "name": "ReadAsync 1416", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039357434, "dur": 11, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039357446, "dur": 981, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039358434, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039358437, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039358511, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039358530, "dur": 49, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039358584, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039358683, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039358695, "dur": 2493, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039361200, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039361203, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039361281, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039361311, "dur": 132436, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039493759, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039493765, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039493795, "dur": 138, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039493935, "dur": 3012, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039496951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039496954, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039496998, "dur": 3, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 16392, "tid": 25769803776, "ts": 1749548039497002, "dur": 10787, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 16392, "tid": 19, "ts": 1749548039508297, "dur": 2311, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 16392, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 16392, "tid": 21474836480, "ts": 1749548039172687, "dur": 39896, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 16392, "tid": 21474836480, "ts": 1749548039212583, "dur": 19, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 16392, "tid": 19, "ts": 1749548039510611, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 16392, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 16392, "tid": 17179869184, "ts": 1749548039138275, "dur": 369560, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 16392, "tid": 17179869184, "ts": 1749548039138343, "dur": 34297, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 16392, "tid": 17179869184, "ts": 1749548039507839, "dur": 86, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 16392, "tid": 17179869184, "ts": 1749548039507871, "dur": 17, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 16392, "tid": 17179869184, "ts": 1749548039507927, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 16392, "tid": 19, "ts": 1749548039510653, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749548039203957, "dur": 1464, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749548039205431, "dur": 570, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749548039206126, "dur": 56, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749548039206183, "dur": 367, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749548039207175, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_37DC11C6B8FE5C8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749548039208064, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749548039209097, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1749548039210401, "dur": 436, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1749548039206561, "dur": 12106, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749548039218677, "dur": 279882, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749548039498561, "dur": 470, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749548039499983, "dur": 73, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749548039500085, "dur": 1967, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749548039206719, "dur": 12004, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039218726, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21B473704285E949.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749548039219083, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039219588, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039219708, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749548039219775, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039219906, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039219969, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749548039220103, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039220709, "dur": 590, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-private-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749548039221300, "dur": 2643, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-multibyte-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749548039220486, "dur": 3515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039224001, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039224157, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039224297, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039224559, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\math_unity_conversion.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749548039224453, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039225175, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039225626, "dur": 4669, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749548039230296, "dur": 90856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039323182, "dur": 7022, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749548039330512, "dur": 398, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749548039321154, "dur": 9832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749548039330987, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039331072, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039331312, "dur": 4273, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749548039335671, "dur": 1844, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749548039337576, "dur": 10011, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749548039347604, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039347720, "dur": 4664, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749548039352393, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039352473, "dur": 65, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039352540, "dur": 2338, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749548039354887, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039357130, "dur": 1783, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749548039358916, "dur": 2796, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749548039361715, "dur": 136884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039206635, "dur": 12062, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039219050, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039219531, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039219636, "dur": 642, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749548039220606, "dur": 689, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cors.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749548039221295, "dur": 2230, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.CookiePolicy.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749548039220278, "dur": 3309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039223587, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039223819, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039224074, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039224293, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039224553, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditorModule\\SpriteFrameModule\\DefaultSpriteNameFileIdDataProvider.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749548039224519, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039225488, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039225616, "dur": 4765, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749548039230383, "dur": 90763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039321147, "dur": 2315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749548039324391, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCompression.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749548039325323, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749548039325466, "dur": 4677, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749548039330156, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749548039330580, "dur": 506, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749548039323494, "dur": 7715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749548039331264, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039331637, "dur": 134, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039331781, "dur": 4178, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749548039335967, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039336128, "dur": 3947, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749548039340121, "dur": 11878, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749548039352024, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749548039352105, "dur": 4280, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749548039356387, "dur": 142194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039206659, "dur": 12044, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039218707, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749548039218808, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749548039218807, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3C3E151BE5CA996B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749548039219046, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749548039219045, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_20B214415B84035D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749548039219607, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749548039219753, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749548039219868, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749548039219926, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039220600, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\DocCodeSamples.Tests\\CollectionsAllocationExamples.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749548039220098, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039221321, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039221553, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039221871, "dur": 1476, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Connections\\UnitConnectionWidget.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749548039221794, "dur": 1685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039223486, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039225405, "dur": 5132, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749548039230541, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749548039230702, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039231006, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749548039230863, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749548039231347, "dur": 94130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039326746, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749548039325478, "dur": 2724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749548039328202, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039328255, "dur": 1836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749548039330294, "dur": 2712, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749548039333107, "dur": 4337, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749548039337454, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749548039337592, "dur": 24494, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749548039362091, "dur": 136472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039206628, "dur": 12062, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039219057, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039219405, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039219627, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749548039220121, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039220723, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039220933, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039221145, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039221379, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039221608, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039221877, "dur": 751, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Path\\Editor\\EditorTool\\ScriptablePath.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749548039222628, "dur": 557, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.4\\Path\\Editor\\EditorTool\\ScriptableData.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749548039221833, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039223781, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039224322, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039224549, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039224795, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039225036, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039225586, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039225873, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749548039226028, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749548039226490, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749548039226604, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039226821, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749548039227254, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039227846, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749548039228260, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749548039228695, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039228903, "dur": 1766, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749548039230670, "dur": 90490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039323181, "dur": 2987, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749548039321160, "dur": 5190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749548039326746, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749548039326374, "dur": 2101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749548039328476, "dur": 1075, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039329558, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039329840, "dur": 3320, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749548039333164, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039333312, "dur": 2722, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749548039336042, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039336227, "dur": 4206, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749548039344205, "dur": 7082, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749548039351299, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749548039351407, "dur": 2255, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749548039353705, "dur": 1692, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749548039355398, "dur": 143155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039206601, "dur": 12084, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039218687, "dur": 1828, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039220699, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Unity.Burst.CodeGen\\FunctionPointerInvokeTransform.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749548039221289, "dur": 1357, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Unity.Burst.CodeGen\\CecilExtensions.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749548039220515, "dur": 2185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039222700, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039222925, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039223159, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039223543, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039223762, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039223995, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039224229, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039224461, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039224689, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039224761, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039225115, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039225582, "dur": 1047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039226630, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749548039227133, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749548039227259, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749548039227388, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749548039227517, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749548039227987, "dur": 947, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039228955, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039229178, "dur": 1649, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749548039230828, "dur": 90327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039323183, "dur": 2302, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749548039321156, "dur": 4514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749548039326745, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.VisualBasic.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749548039325704, "dur": 1702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749548039327406, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039327536, "dur": 1230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749548039328786, "dur": 1180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749548039330037, "dur": 2703, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749548039332745, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039335657, "dur": 221, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039333163, "dur": 2730, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749548039335906, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039336020, "dur": 4042, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749548039340112, "dur": 9829, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749548039350008, "dur": 1432, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749548039351451, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749548039351657, "dur": 3014, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749548039354727, "dur": 1571, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749548039356299, "dur": 142285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039206682, "dur": 12029, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039218713, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_094A556A01D249DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749548039219052, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039219311, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039220119, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039220566, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039220797, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039221027, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039221242, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039221498, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039221880, "dur": 1294, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\SuperUnitWidget.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749548039221731, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039223246, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039225406, "dur": 4858, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749548039230266, "dur": 90878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039321146, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749548039323473, "dur": 1937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749548039326746, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Loader.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749548039325453, "dur": 2202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749548039327656, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039327845, "dur": 1923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749548039329842, "dur": 2533, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749548039332384, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039332667, "dur": 3022, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749548039335695, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749548039335862, "dur": 1784, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749548039337713, "dur": 14021, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749548039351807, "dur": 3223, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749548039355089, "dur": 143466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039206710, "dur": 12007, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039218719, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_699CA9E1D209A267.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749548039219054, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039219297, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749548039219602, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749548039220117, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039220555, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039220859, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039221090, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039221347, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039221567, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039221791, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039222044, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039222650, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039222819, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039222975, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039223119, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039223272, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039223419, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039223623, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039223783, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039223943, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039224155, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039224760, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039225089, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039225584, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039225875, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749548039226011, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749548039226628, "dur": 2233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\GridPaletteUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749548039226173, "dur": 2772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749548039229001, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039229284, "dur": 1771, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749548039231057, "dur": 90096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039323182, "dur": 2325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749548039321157, "dur": 4553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749548039326746, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventSource.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749548039325744, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749548039327891, "dur": 1310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039330115, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.DataAnnotations.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749548039329205, "dur": 1840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749548039331128, "dur": 1856, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749548039333013, "dur": 2843, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749548039335868, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039335980, "dur": 2251, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749548039338241, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039338394, "dur": 7940, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.IK.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749548039346404, "dur": 4246, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749548039350714, "dur": 4157, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749548039354880, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749548039355193, "dur": 1119, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749548039356313, "dur": 142267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039206741, "dur": 11989, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039218797, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749548039219042, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039219220, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749548039219597, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749548039220115, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039220716, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039220931, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039221141, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039221387, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039221617, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039221830, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039222062, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039222645, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039223157, "dur": 805, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\LongInspector.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749548039222992, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039224024, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039224242, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039224460, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039224700, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039225123, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039225581, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039226630, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749548039227121, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039227664, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039228057, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749548039228426, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039228748, "dur": 1835, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749548039230584, "dur": 90558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039321143, "dur": 2250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749548039323434, "dur": 1978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749548039326160, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-time-l1-1-0.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749548039326745, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749548039325473, "dur": 2125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749548039327637, "dur": 1968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749548039329655, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039330299, "dur": 2653, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749548039332984, "dur": 2633, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749548039335687, "dur": 1377, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749548039337073, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039337391, "dur": 8488, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749548039345892, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039346585, "dur": 4843, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749548039351440, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039358374, "dur": 548, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749548039351611, "dur": 7321, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749548039358935, "dur": 139634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039206762, "dur": 11976, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039218850, "dur": 299, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749548039218848, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C06C6D7BC8ACCF56.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749548039219182, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039219615, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749548039220122, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039220598, "dur": 695, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\VisualStudioCodeInstallation.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749548039220547, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039221412, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039221558, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039221720, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039221870, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039222115, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039222656, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039223278, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039223496, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039223741, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039223969, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039224190, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039224407, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039224621, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039225040, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039225585, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039225874, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749548039226036, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749548039226164, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749548039226288, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039226499, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749548039227050, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039227129, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749548039227257, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749548039227389, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749548039227516, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749548039227990, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749548039228365, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749548039228871, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039229175, "dur": 872, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749548039230048, "dur": 2360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039232410, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749548039232531, "dur": 92933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039325467, "dur": 703, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749548039326746, "dur": 3305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749548039325465, "dur": 5792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749548039331296, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039331394, "dur": 1608, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749548039333030, "dur": 3197, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749548039336232, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749548039336416, "dur": 7884, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Aseprite.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749548039344369, "dur": 12051, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749548039356423, "dur": 142167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039206796, "dur": 11948, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039219048, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749548039219047, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_425C305CB5B8A325.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749548039219284, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749548039219639, "dur": 499, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749548039220523, "dur": 778, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Console.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749548039220138, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039221347, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039221568, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039221791, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039222043, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039222272, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039222840, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039223158, "dur": 715, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\EventMachineEditor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749548039223080, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039223967, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039224101, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039224244, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039224401, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039224560, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\Interface\\IGUIUtility.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749548039224546, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039225242, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039225580, "dur": 1052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039226633, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749548039226876, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039226961, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749548039227020, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039227294, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749548039227589, "dur": 1667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039229256, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749548039229349, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749548039229600, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749548039229891, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749548039230368, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039230540, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749548039230888, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDImporterAssetPostProcessor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749548039230608, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749548039231037, "dur": 92153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039323191, "dur": 1192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749548039325459, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749548039324410, "dur": 1257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749548039325667, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039326160, "dur": 3969, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749548039330157, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749548039330427, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749548039330586, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749548039331515, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749548039325873, "dur": 5877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749548039331919, "dur": 3662, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749548039335629, "dur": 1322, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749548039337059, "dur": 7162, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749548039344295, "dur": 10652, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749548039354959, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039358039, "dur": 439, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749548039358485, "dur": 2695, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749548039361191, "dur": 137376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039206813, "dur": 11960, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039218833, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749548039218832, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_2A2DD78CDAF18E7D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749548039218986, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749548039218986, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_F01CBB7E8932E0CB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749548039219438, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039219632, "dur": 512, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1749548039220144, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039220783, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039221292, "dur": 1999, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMPro_FontAssetCreatorWindow.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749548039220945, "dur": 2380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039223325, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039223488, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039223679, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039223925, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\CoverageFormats\\OpenCover\\Model\\Summary.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749548039223842, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039225133, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039225592, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039225868, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749548039226172, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749548039226843, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749548039226984, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749548039227376, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039227818, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749548039227970, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749548039228384, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039228525, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039229083, "dur": 1529, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749548039230613, "dur": 90537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039321151, "dur": 1292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749548039322444, "dur": 1029, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039323477, "dur": 1201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749548039324678, "dur": 2990, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039327670, "dur": 1220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749548039328890, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039329406, "dur": 1088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749548039330529, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039330603, "dur": 2179, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.IK.Runtime.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749548039332787, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039333104, "dur": 2791, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/PsdPlugin.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749548039335905, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039336052, "dur": 69, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039336122, "dur": 3454, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749548039339617, "dur": 8393, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749548039348021, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749548039348386, "dur": 6930, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749548039355318, "dur": 143279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039206836, "dur": 11950, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039218840, "dur": 481, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749548039218839, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_EF9DC8C923ADA8E7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749548039219567, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749548039219953, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13445476680238899994.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749548039220104, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039220195, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749548039220657, "dur": 647, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Identity.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749548039220274, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039221415, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039221643, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039221879, "dur": 759, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Runtime\\SpriteLib\\SpriteLibraryData.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749548039221867, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039222763, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039222924, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039223081, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039223283, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\CollectionHelper.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749548039223225, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039224042, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039224275, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039224556, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3x2.gen.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749548039224488, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039225445, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039225596, "dur": 2936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039228535, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039229119, "dur": 2023, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749548039231143, "dur": 94324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039325471, "dur": 1349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749548039325469, "dur": 3359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749548039328828, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039330112, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipelines.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749548039329166, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749548039331018, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039331293, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039331724, "dur": 199, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039331927, "dur": 4094, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749548039336029, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749548039336130, "dur": 4315, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749548039344208, "dur": 5728, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749548039349987, "dur": 3698, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749548039353728, "dur": 1622, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749548039355351, "dur": 143243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039206865, "dur": 11989, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039218942, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039219421, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749548039220166, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749548039220252, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749548039221278, "dur": 620, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandState.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749548039222282, "dur": 348, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\RestoreTestContextAfterDomainReload.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749548039219503, "dur": 3931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749548039223506, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749548039224849, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\Analytics\\RunFinishedData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749548039225244, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\TestListBuilder\\ResultSummarizer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749548039223626, "dur": 1910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749548039225643, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749548039225866, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749548039225987, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749548039226343, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039226436, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749548039226536, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039226610, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749548039227088, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1749548039227679, "dur": 259, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039227942, "dur": 91699, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1749548039321144, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749548039322445, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039322528, "dur": 1515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749548039324044, "dur": 1639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039326689, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordaccore_amd64_amd64_6.0.1322.58009.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749548039325687, "dur": 2036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749548039327724, "dur": 2412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039330180, "dur": 2959, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749548039333143, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039333317, "dur": 2410, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749548039335758, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039335865, "dur": 2484, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749548039338375, "dur": 9246, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749548039347628, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039348072, "dur": 3179, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749548039351261, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749548039351402, "dur": 2062, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749548039353496, "dur": 4549, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-firstpass.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749548039358054, "dur": 140521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039206884, "dur": 11974, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039219043, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749548039219042, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2A9C520E2391C187.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749548039219170, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039219613, "dur": 569, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749548039220184, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039220688, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039220909, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039221127, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039221357, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039221526, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039221682, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039221821, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039221991, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039222151, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039222649, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039222880, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039223118, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039223343, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039223562, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039223918, "dur": 1236, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Events\\Marker.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749548039223779, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039225217, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039225583, "dur": 1062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039226646, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749548039226793, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749548039227397, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749548039227511, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749548039227661, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749548039228053, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039228322, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749548039228451, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749548039228890, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039229161, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039229213, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Editor.ref.dll_8C62A80A07075E58.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749548039229276, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039229353, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749548039229820, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749548039230157, "dur": 90999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039321158, "dur": 1962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749548039323120, "dur": 2369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039326745, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749548039325494, "dur": 1980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749548039327513, "dur": 1921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749548039329513, "dur": 3461, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749548039333006, "dur": 3760, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749548039336804, "dur": 13408, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749548039350225, "dur": 1063, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749548039351385, "dur": 3276, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749548039354719, "dur": 1434, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749548039356155, "dur": 142433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039206915, "dur": 12150, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039219068, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039219653, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749548039220185, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039220604, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749548039221316, "dur": 590, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.ClientFactory.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749548039220477, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039221969, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039222245, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039222840, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039223084, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039223328, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039223559, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039223919, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Playables\\TimeNotificationBehaviour.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749548039223774, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039224700, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039225129, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039225592, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039225873, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749548039226031, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749548039226189, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749548039226638, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749548039226768, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749548039227261, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749548039227386, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749548039227508, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749548039227657, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749548039228061, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749548039228507, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749548039228925, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749548039228618, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749548039229324, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039229865, "dur": 2049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039231972, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749548039232172, "dur": 91015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039323193, "dur": 1969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749548039325460, "dur": 4507, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749548039330158, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749548039330633, "dur": 532, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749548039331214, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749548039331444, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749548039325204, "dur": 7140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749548039332390, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039333158, "dur": 3717, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Animation.Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1749548039336883, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749548039337061, "dur": 27067, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749548039364134, "dur": 134427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039206939, "dur": 12135, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039219079, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039219209, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039219622, "dur": 502, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749548039220124, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039220584, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039220762, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039220916, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039221070, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039221222, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039221426, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039221642, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039221862, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039222041, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039222180, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039222680, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039222896, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039223326, "dur": 1769, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\UnsafeBitArray.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749548039223135, "dur": 2017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039225152, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039225591, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039226625, "dur": 1862, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749548039226519, "dur": 2234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749548039228788, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749548039228925, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749548039228893, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749548039229224, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749548039229285, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039229477, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749548039229649, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039230620, "dur": 90528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039321151, "dur": 2225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749548039323376, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039323457, "dur": 1963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749548039325420, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039326160, "dur": 4011, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-sysinfo-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749548039330424, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.DataAnnotations.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749548039330580, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749548039330780, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749548039325491, "dur": 5847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749548039331378, "dur": 2032, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749548039333475, "dur": 3054, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749548039336536, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039336628, "dur": 9070, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Common.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749548039345711, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749548039346111, "dur": 3833, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749548039349985, "dur": 2910, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749548039352939, "dur": 2557, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749548039355498, "dur": 143094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039206965, "dur": 12112, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039219084, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749548039219078, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749548039219318, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749548039219465, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749548039220099, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039220636, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039220885, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039221104, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039221345, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039221486, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039221641, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039221783, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039221993, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039222250, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039222890, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039223078, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039223227, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039223389, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039223530, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039223695, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039223842, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039224108, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039224325, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039224553, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039224772, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039225232, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039225579, "dur": 1121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039226720, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749548039226797, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039227209, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749548039227821, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749548039227883, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039228435, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749548039228733, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749548039228814, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749548039229139, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039229531, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039229969, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749548039230054, "dur": 1001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749548039231095, "dur": 92097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039323196, "dur": 1975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749548039325172, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039325373, "dur": 1189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749548039326562, "dur": 1208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039327774, "dur": 1960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749548039329762, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039330144, "dur": 2674, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749548039332820, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749548039332819, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749548039333118, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039333222, "dur": 2420, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749548039335675, "dur": 1386, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1749548039337070, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039337388, "dur": 8485, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Collections.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749548039345879, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039345994, "dur": 5412, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1749548039351418, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749548039351620, "dur": 3049, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1749548039354723, "dur": 1494, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1749548039356219, "dur": 142331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039206997, "dur": 12083, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039219091, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749548039219082, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749548039219385, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039219624, "dur": 488, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1749548039220112, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039220501, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039220768, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039220997, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039221270, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.spriteshape@9.0.5\\Editor\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749548039221224, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039222067, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039222649, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039222865, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039223097, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039223320, "dur": 1255, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\RenamedAssemblyAttribute.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749548039223320, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039224713, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039225093, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039225592, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039225870, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749548039226033, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749548039226498, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039227070, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039227510, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749548039227625, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039227704, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749548039228088, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039228163, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749548039228974, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039229301, "dur": 1767, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749548039231068, "dur": 92120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039323189, "dur": 1959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749548039325460, "dur": 710, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749548039326745, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.FileExtensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749548039325196, "dur": 2696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749548039327921, "dur": 1933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749548039329907, "dur": 2761, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749548039332673, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039333108, "dur": 3099, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Animation.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749548039336219, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039336411, "dur": 9420, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749548039345847, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039346111, "dur": 4916, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749548039351073, "dur": 3581, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749548039354666, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749548039354728, "dur": 1292, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1749548039356023, "dur": 142533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039207016, "dur": 12070, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039219098, "dur": 463, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749548039219088, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749548039219643, "dur": 536, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749548039220703, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Protocols.Json.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749548039221313, "dur": 595, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Core.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749548039220180, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039221939, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039222087, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039222619, "dur": 2500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\EventUnit.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749548039222241, "dur": 2999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039225240, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039225597, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039225869, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749548039226625, "dur": 2432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Ensure\\Ensure.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749548039229413, "dur": 325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticFunctionInvoker_5.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749548039225998, "dur": 3891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749548039229957, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749548039230377, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\FunctionExpression.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749548039230803, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Lifecycle\\Update.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749548039230055, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749548039231356, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749548039231441, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749548039231779, "dur": 89379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039321159, "dur": 2258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749548039323418, "dur": 667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039324090, "dur": 1987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749548039326078, "dur": 4864, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039330999, "dur": 2419, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749548039333421, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039333484, "dur": 3039, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749548039336530, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039336632, "dur": 8862, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Aseprite.Common.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1749548039345507, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039345614, "dur": 5844, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1749548039351467, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039351655, "dur": 3212, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749548039354876, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749548039354984, "dur": 1252, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1749548039356238, "dur": 142348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749548039207041, "dur": 12054, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749548039219101, "dur": 491, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749548039219096, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749548039219606, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749548039219664, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749548039219934, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749548039220257, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749548039221273, "dur": 2071, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749548039223917, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749548039219760, "dur": 4544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749548039224367, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749548039224584, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749548039224798, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749548039225154, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749548039225584, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749548039225900, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749548039226044, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749548039226625, "dur": 2422, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749548039226465, "dur": 2773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749548039229349, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749548039229716, "dur": 1645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749548039231362, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749548039231464, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749548039231965, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749548039232254, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749548039232406, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749548039232470, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749548039232631, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749548039232802, "dur": 264065, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1749548039506765, "dur": 3842, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 16392, "tid": 19, "ts": 1749548039510738, "dur": 2296, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 16392, "tid": 19, "ts": 1749548039513117, "dur": 8346, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 16392, "tid": 19, "ts": 1749548039508271, "dur": 13268, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}