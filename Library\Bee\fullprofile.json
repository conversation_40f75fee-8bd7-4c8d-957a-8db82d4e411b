{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26084, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26084, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26084, "tid": 564, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26084, "tid": 564, "ts": 1749616268193308, "dur": 443, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26084, "tid": 564, "ts": 1749616268196625, "dur": 823, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26084, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26084, "tid": 1, "ts": 1749616267116147, "dur": 3515, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749616267119665, "dur": 17119, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749616267136792, "dur": 21248, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26084, "tid": 564, "ts": 1749616268197455, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 26084, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267114902, "dur": 31064, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267145970, "dur": 1041074, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267146699, "dur": 1882, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267148585, "dur": 965, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149551, "dur": 198, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149751, "dur": 8, "ph": "X", "name": "ProcessMessages 20522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149760, "dur": 28, "ph": "X", "name": "ReadAsync 20522", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149794, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149795, "dur": 31, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149829, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149830, "dur": 62, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149894, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149916, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149942, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267149978, "dur": 24, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150009, "dur": 19, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150030, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150037, "dur": 34, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150074, "dur": 17, "ph": "X", "name": "ReadAsync 1161", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150093, "dur": 9, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150104, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150133, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150157, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150160, "dur": 23, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150186, "dur": 16, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150207, "dur": 20, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150229, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150233, "dur": 19, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150255, "dur": 3, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150259, "dur": 16, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150277, "dur": 2, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150281, "dur": 16, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150298, "dur": 9, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150309, "dur": 9, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150321, "dur": 14, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150336, "dur": 8, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150346, "dur": 39, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150389, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150419, "dur": 3, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150424, "dur": 19, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150445, "dur": 3, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150449, "dur": 20, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150471, "dur": 3, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150476, "dur": 18, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150496, "dur": 3, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150501, "dur": 18, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150521, "dur": 2, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150525, "dur": 16, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150544, "dur": 12, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150558, "dur": 9, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150569, "dur": 23, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150593, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150594, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150606, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150629, "dur": 34, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150694, "dur": 1, "ph": "X", "name": "ProcessMessages 1080", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150697, "dur": 50, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150749, "dur": 4, "ph": "X", "name": "ProcessMessages 2333", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150754, "dur": 14, "ph": "X", "name": "ReadAsync 2333", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150770, "dur": 11, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150783, "dur": 9, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150794, "dur": 9, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150804, "dur": 56, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150862, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150886, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150888, "dur": 26, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150916, "dur": 4, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150922, "dur": 23, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150947, "dur": 3, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150953, "dur": 23, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150978, "dur": 13, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267150993, "dur": 12, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151006, "dur": 39, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151049, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151082, "dur": 3, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151086, "dur": 25, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151118, "dur": 16, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151136, "dur": 3, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151140, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151163, "dur": 4, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151168, "dur": 19, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151189, "dur": 12, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151204, "dur": 6, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151212, "dur": 30, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151243, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151247, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151272, "dur": 31, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151310, "dur": 18, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151330, "dur": 4, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151334, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151352, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151356, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151376, "dur": 3, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151380, "dur": 23, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151405, "dur": 18, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151425, "dur": 41, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151468, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151485, "dur": 32, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151519, "dur": 4, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151525, "dur": 23, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151550, "dur": 3, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151554, "dur": 19, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151575, "dur": 3, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151579, "dur": 24, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151606, "dur": 3, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151611, "dur": 21, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151634, "dur": 10, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151647, "dur": 11, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151659, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151671, "dur": 68, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151741, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151745, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151765, "dur": 3, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151769, "dur": 24, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151795, "dur": 3, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151799, "dur": 17, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151823, "dur": 13, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151837, "dur": 10, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267151848, "dur": 636, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152488, "dur": 122, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152613, "dur": 5, "ph": "X", "name": "ProcessMessages 12869", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152620, "dur": 27, "ph": "X", "name": "ReadAsync 12869", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152650, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152652, "dur": 25, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152684, "dur": 31, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152720, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152741, "dur": 2, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152745, "dur": 20, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152767, "dur": 3, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152772, "dur": 20, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152794, "dur": 3, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152798, "dur": 16, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152816, "dur": 3, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152820, "dur": 25, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152850, "dur": 20, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152872, "dur": 3, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152877, "dur": 15, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152898, "dur": 16, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152916, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152920, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152942, "dur": 3, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152946, "dur": 25, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152973, "dur": 18, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152992, "dur": 3, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267152996, "dur": 16, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153014, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153018, "dur": 24, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153044, "dur": 3, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153048, "dur": 18, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153068, "dur": 3, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153072, "dur": 16, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153090, "dur": 3, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153094, "dur": 14, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153115, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153133, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153137, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153160, "dur": 3, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153164, "dur": 18, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153184, "dur": 2, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153187, "dur": 20, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153210, "dur": 3, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153213, "dur": 18, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153238, "dur": 18, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153258, "dur": 3, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153262, "dur": 18, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153281, "dur": 2, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153285, "dur": 21, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153307, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153311, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153329, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153333, "dur": 18, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153352, "dur": 3, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153356, "dur": 26, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153385, "dur": 3, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153388, "dur": 16, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153406, "dur": 3, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153409, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153436, "dur": 3, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153440, "dur": 17, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153459, "dur": 3, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153463, "dur": 15, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153480, "dur": 3, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153484, "dur": 19, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153504, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153507, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153529, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153533, "dur": 18, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153553, "dur": 3, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153557, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153580, "dur": 3, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153584, "dur": 17, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153603, "dur": 3, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153607, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153631, "dur": 3, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153635, "dur": 18, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153654, "dur": 3, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153658, "dur": 15, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153677, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153700, "dur": 2, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153704, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153724, "dur": 3, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153728, "dur": 18, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153748, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153752, "dur": 16, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153770, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153773, "dur": 19, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153794, "dur": 3, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153798, "dur": 18, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153818, "dur": 3, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153822, "dur": 18, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153842, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153845, "dur": 18, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153865, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153869, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153888, "dur": 3, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153892, "dur": 19, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153913, "dur": 3, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153917, "dur": 21, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153940, "dur": 3, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153944, "dur": 19, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153968, "dur": 18, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153988, "dur": 3, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267153992, "dur": 18, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154012, "dur": 3, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154015, "dur": 15, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154033, "dur": 3, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154036, "dur": 15, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154053, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154057, "dur": 15, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154074, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154077, "dur": 17, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154097, "dur": 3, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154101, "dur": 29, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154131, "dur": 3, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154135, "dur": 17, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154155, "dur": 2, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154158, "dur": 19, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154179, "dur": 2, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154183, "dur": 18, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154203, "dur": 2, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154206, "dur": 14, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154222, "dur": 2, "ph": "X", "name": "ProcessMessages 94", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154225, "dur": 12, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154239, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154243, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154265, "dur": 3, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154268, "dur": 17, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154288, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154291, "dur": 23, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154316, "dur": 3, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154320, "dur": 19, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154341, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154359, "dur": 22, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154383, "dur": 3, "ph": "X", "name": "ProcessMessages 1346", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154389, "dur": 15, "ph": "X", "name": "ReadAsync 1346", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154406, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154409, "dur": 14, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154425, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154429, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154449, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154452, "dur": 21, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154475, "dur": 3, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154479, "dur": 19, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154500, "dur": 3, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154504, "dur": 15, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154521, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154525, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154544, "dur": 3, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154548, "dur": 19, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154568, "dur": 3, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154572, "dur": 17, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154591, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154595, "dur": 20, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154617, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154620, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154639, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154643, "dur": 20, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154665, "dur": 4, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154670, "dur": 18, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154690, "dur": 3, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154694, "dur": 15, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154711, "dur": 3, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154715, "dur": 32, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154749, "dur": 3, "ph": "X", "name": "ProcessMessages 1351", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154752, "dur": 16, "ph": "X", "name": "ReadAsync 1351", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154771, "dur": 2, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154774, "dur": 15, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154791, "dur": 2, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154794, "dur": 15, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154812, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154815, "dur": 17, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154834, "dur": 2, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154838, "dur": 19, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154858, "dur": 2, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154862, "dur": 18, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154882, "dur": 3, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154886, "dur": 18, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154905, "dur": 3, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154909, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154935, "dur": 3, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154939, "dur": 17, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154957, "dur": 3, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154962, "dur": 20, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267154984, "dur": 21, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155007, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155009, "dur": 30, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155042, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155043, "dur": 22, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155067, "dur": 25, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155094, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155096, "dur": 26, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155125, "dur": 32, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155159, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155162, "dur": 25, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155192, "dur": 27, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155221, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155223, "dur": 25, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155251, "dur": 17, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155271, "dur": 21, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155293, "dur": 2, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155296, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155319, "dur": 24, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155344, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155346, "dur": 19, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155368, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155389, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155391, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155422, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155424, "dur": 21, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155449, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155500, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155526, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155528, "dur": 27, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155557, "dur": 16, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155577, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155612, "dur": 1, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155614, "dur": 17, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155635, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155660, "dur": 29, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155691, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155727, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155731, "dur": 24, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155759, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155788, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155818, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155819, "dur": 23, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155845, "dur": 34, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155882, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155910, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155911, "dur": 29, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155943, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155972, "dur": 18, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267155994, "dur": 26, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156022, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156024, "dur": 21, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156048, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156109, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156138, "dur": 22, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156163, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156181, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156214, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156243, "dur": 22, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156268, "dur": 16, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156287, "dur": 31, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156320, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156362, "dur": 1, "ph": "X", "name": "ProcessMessages 1027", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156364, "dur": 17, "ph": "X", "name": "ReadAsync 1027", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156384, "dur": 36, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156423, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156454, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156455, "dur": 24, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156482, "dur": 63, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156548, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156580, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156581, "dur": 22, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156607, "dur": 37, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156646, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156648, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156678, "dur": 23, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156704, "dur": 33, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156740, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156771, "dur": 22, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156795, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156797, "dur": 30, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156830, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156860, "dur": 19, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156882, "dur": 16, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156901, "dur": 17, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156921, "dur": 26, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156949, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267156982, "dur": 21, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157005, "dur": 19, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157026, "dur": 69, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157098, "dur": 212, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157312, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157314, "dur": 31, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157346, "dur": 2, "ph": "X", "name": "ProcessMessages 1500", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157349, "dur": 47, "ph": "X", "name": "ReadAsync 1500", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157401, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157437, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157439, "dur": 20, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157461, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157463, "dur": 52, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157518, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157547, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157548, "dur": 24, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157575, "dur": 16, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157594, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157621, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157655, "dur": 1, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157656, "dur": 23, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157681, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157683, "dur": 37, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157725, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157755, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157757, "dur": 21, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157780, "dur": 19, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157801, "dur": 26, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157829, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157832, "dur": 24, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157859, "dur": 40, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157902, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157908, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157934, "dur": 1, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157935, "dur": 30, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157969, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157971, "dur": 24, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267157998, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158037, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158040, "dur": 32, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158076, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158099, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158100, "dur": 93, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158197, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158222, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158223, "dur": 35, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158262, "dur": 24, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158288, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158290, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158314, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158316, "dur": 29, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158348, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158385, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158388, "dur": 22, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158412, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158414, "dur": 18, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158436, "dur": 33, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158473, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158509, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158511, "dur": 22, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158537, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158574, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158640, "dur": 22, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158667, "dur": 45, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158714, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158747, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158749, "dur": 22, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158773, "dur": 2, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158776, "dur": 67, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158845, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158870, "dur": 16, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158890, "dur": 21, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158914, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158954, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158983, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267158985, "dur": 25, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159013, "dur": 16, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159032, "dur": 27, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159071, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159104, "dur": 1, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159106, "dur": 19, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159126, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159128, "dur": 59, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159189, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159221, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159223, "dur": 21, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159247, "dur": 70, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159320, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159356, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159358, "dur": 22, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159383, "dur": 60, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159447, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159478, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159479, "dur": 22, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159505, "dur": 38, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159546, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159581, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159583, "dur": 23, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159609, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159611, "dur": 42, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159657, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159689, "dur": 1, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159691, "dur": 23, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159716, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159718, "dur": 36, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159757, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159806, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159837, "dur": 21, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159862, "dur": 29, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159893, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159928, "dur": 1, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159930, "dur": 26, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159960, "dur": 26, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267159989, "dur": 28, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160020, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160022, "dur": 21, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160046, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160069, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160094, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160118, "dur": 19, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160140, "dur": 35, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160177, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160178, "dur": 26, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160206, "dur": 4, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160211, "dur": 21, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160233, "dur": 3, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160238, "dur": 18, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160257, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160262, "dur": 14, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160282, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160304, "dur": 3, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160309, "dur": 19, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160330, "dur": 3, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160334, "dur": 20, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160360, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160382, "dur": 19, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160408, "dur": 19, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160428, "dur": 3, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160433, "dur": 21, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160460, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160483, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160486, "dur": 29, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160516, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160518, "dur": 19, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160542, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160578, "dur": 4, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160583, "dur": 19, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160604, "dur": 3, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160609, "dur": 23, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160639, "dur": 23, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160664, "dur": 3, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160668, "dur": 16, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160687, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160708, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160730, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160732, "dur": 26, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160761, "dur": 22, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160785, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160791, "dur": 16, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160809, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160857, "dur": 4, "ph": "X", "name": "ProcessMessages 1061", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160862, "dur": 14, "ph": "X", "name": "ReadAsync 1061", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160882, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160913, "dur": 20, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160935, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160940, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160966, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160993, "dur": 3, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267160997, "dur": 18, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161021, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161044, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161062, "dur": 3, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161067, "dur": 17, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161090, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161113, "dur": 3, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161117, "dur": 19, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161139, "dur": 4, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161145, "dur": 21, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161167, "dur": 3, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161172, "dur": 16, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161194, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161212, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161216, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161245, "dur": 3, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161249, "dur": 19, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161271, "dur": 3, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161275, "dur": 16, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161294, "dur": 3, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161298, "dur": 49, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161353, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161374, "dur": 3, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161378, "dur": 19, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161399, "dur": 3, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161404, "dur": 15, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161420, "dur": 3, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161425, "dur": 13, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161440, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161462, "dur": 82, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161548, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161568, "dur": 264, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267161835, "dur": 214, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162051, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162054, "dur": 92, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162148, "dur": 3, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162153, "dur": 31, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162186, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162189, "dur": 19, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162209, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162211, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162231, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162233, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162256, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162275, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162301, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162303, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162326, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162345, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162347, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162383, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162385, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162411, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162413, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162437, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162439, "dur": 18, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162459, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162461, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162486, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162512, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162514, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162540, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162542, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162565, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162567, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162605, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162607, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162630, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162653, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162655, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162680, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162683, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162707, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162709, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162736, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162738, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162763, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162766, "dur": 20, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162788, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162790, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162816, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162818, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162838, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162861, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162885, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162911, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162913, "dur": 24, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162940, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162968, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162970, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162992, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267162994, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163019, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163022, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163050, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163052, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163073, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163074, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163095, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163097, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163124, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163126, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163148, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163150, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163170, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163195, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163214, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163236, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163257, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163276, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163297, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163298, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163321, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163322, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163342, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163343, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163362, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163385, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163405, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163434, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163476, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163500, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163518, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163520, "dur": 18, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163541, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163543, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163568, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163570, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163590, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163612, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163614, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163635, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163659, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163684, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163743, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163744, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163764, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163784, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163802, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267163823, "dur": 5605, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169435, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169438, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169471, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169497, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169499, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169530, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169562, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169581, "dur": 290, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169875, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169906, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169932, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169950, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267169972, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170121, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170125, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170145, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170147, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170220, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170240, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170261, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170279, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170299, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170402, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170422, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170442, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170465, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170486, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170572, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170594, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170613, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170663, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170709, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170739, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170755, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170757, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170774, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170905, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170930, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267170947, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171025, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171044, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171061, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171159, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171174, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171176, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171193, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171298, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171316, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171333, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171350, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171367, "dur": 311, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171681, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171704, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171706, "dur": 44, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171753, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171771, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171789, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171817, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171837, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171855, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171875, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171893, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171925, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171959, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267171986, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172043, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172045, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172094, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172121, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172142, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172197, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172221, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172223, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172247, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172249, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172288, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172320, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172343, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172345, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172438, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172470, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172493, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172521, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172545, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172561, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172627, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172650, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172668, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172684, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172709, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172729, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172746, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172749, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172776, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172792, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172811, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172832, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172850, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172851, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172871, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172898, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172958, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172978, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267172980, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173006, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173128, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173148, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173169, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173192, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173242, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173263, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173284, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173304, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173323, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173351, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173371, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173393, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173406, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173438, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173461, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173484, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173523, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173544, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173564, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173566, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173602, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173624, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173644, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173664, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173689, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173802, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173826, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173844, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173882, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173903, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267173906, "dur": 132, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267174045, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267174064, "dur": 980, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175048, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175083, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175087, "dur": 22, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175114, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175149, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175176, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175241, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175263, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175329, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175353, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175382, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175441, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175468, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175622, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267175647, "dur": 606, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267176256, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267176282, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267176289, "dur": 110632, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267286930, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267286934, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267286968, "dur": 1547, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267288518, "dur": 1090, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267289614, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267289630, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267289687, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267289696, "dur": 925, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290623, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290634, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290769, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290778, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290793, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290804, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290874, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290887, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290951, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290960, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267290975, "dur": 873, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267291850, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267291858, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292010, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292033, "dur": 142, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292179, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292192, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292226, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292236, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292240, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292260, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292271, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292326, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292338, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292340, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292673, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292688, "dur": 244, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292934, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267292942, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293075, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293086, "dur": 278, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293368, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293381, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293486, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293488, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293499, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293567, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293582, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293634, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293645, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293750, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267293760, "dur": 495, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267294257, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267294270, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267294279, "dur": 230, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267294512, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267294521, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267294535, "dur": 309, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267294846, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267294863, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267294965, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267294980, "dur": 186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295167, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295175, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295178, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295212, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295223, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295234, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295308, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295322, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295353, "dur": 263, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295622, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295659, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295678, "dur": 10, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295690, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295711, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295725, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295765, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295778, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295789, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295861, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295893, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295895, "dur": 12, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295909, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295923, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295946, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295959, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295970, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295980, "dur": 9, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267295992, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296007, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296026, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296030, "dur": 13, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296047, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296059, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296077, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296095, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296110, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296141, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296157, "dur": 8, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296167, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296178, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296187, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296192, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296208, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296219, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296285, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296304, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296317, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296343, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296356, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296416, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296431, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296458, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296470, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296485, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296486, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296502, "dur": 9, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296514, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296527, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296553, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296555, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296581, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296647, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296725, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296727, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296754, "dur": 89, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296848, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296869, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296901, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616267296917, "dur": 735946, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268032875, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268032878, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268032920, "dur": 3206, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268036130, "dur": 22859, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268059003, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268059006, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268059030, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268059034, "dur": 89834, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268148881, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268148884, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268148908, "dur": 31, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268148940, "dur": 4048, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268152992, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268152994, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268153007, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268153012, "dur": 828, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268153845, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268153865, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268153883, "dur": 24894, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268178789, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268178794, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268178810, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268178815, "dur": 423, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268179243, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268179245, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268179296, "dur": 40, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268179337, "dur": 708, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268180050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268180052, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268180097, "dur": 456, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26084, "tid": 12884901888, "ts": 1749616268180556, "dur": 6219, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26084, "tid": 564, "ts": 1749616268197469, "dur": 1557, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26084, "tid": 8589934592, "ts": 1749616267112597, "dur": 45970, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": 1749616267158573, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26084, "tid": 8589934592, "ts": 1749616267158577, "dur": 1140, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26084, "tid": 564, "ts": 1749616268199028, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26084, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26084, "tid": 4294967296, "ts": 1749616267068087, "dur": 1119820, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749616267070732, "dur": 37055, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749616268187926, "dur": 3303, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749616268190003, "dur": 89, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26084, "tid": 4294967296, "ts": 1749616268191299, "dur": 15, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26084, "tid": 564, "ts": 1749616268199033, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749616267143933, "dur": 1268, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616267145207, "dur": 616, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616267145966, "dur": 57, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749616267146023, "dur": 356, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616267147037, "dur": 1042, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_3AF52CA4203FDAC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749616267148803, "dur": 1071, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749616267152634, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749616267146395, "dur": 15182, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616267161587, "dur": 1017991, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616268179580, "dur": 274, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616268179854, "dur": 51, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616268180042, "dur": 89, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616268180160, "dur": 1067, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749616267146587, "dur": 15006, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267161595, "dur": 6486, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267168081, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267168217, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267168368, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267168520, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267168690, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267169047, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267169342, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267169641, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267170018, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267170454, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749616267170789, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267171428, "dur": 602, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749616267172157, "dur": 461, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749616267172685, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.34\\Rider\\Editor\\Debugger\\RiderDebuggerProvider.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749616267171061, "dur": 1840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749616267172978, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267173231, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267173529, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267173844, "dur": 114660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267288506, "dur": 1162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749616267289669, "dur": 3945, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616267294387, "dur": 1392, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749616267295903, "dur": 470, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749616267293624, "dur": 3350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749616267297039, "dur": 882407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267146642, "dur": 14972, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267161633, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616267161617, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749616267161721, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267161853, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749616267162026, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616267162191, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1749616267162025, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_9CA00EB8472AEF4E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749616267162325, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267162640, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267162762, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749616267163057, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749616267163200, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267163626, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267163737, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267163873, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267164843, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267165088, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267165323, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267165564, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267165797, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267165951, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267166113, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267166288, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267166732, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267166889, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267167034, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267167175, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267167334, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267167472, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267167631, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267167772, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267168196, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267168436, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267168663, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267169357, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267169641, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267170021, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267170471, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267171040, "dur": 778, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616267170868, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749616267172058, "dur": 506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267172606, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267172680, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749616267172834, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749616267173169, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267173258, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267173518, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267173840, "dur": 1649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267175490, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749616267175584, "dur": 114118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267290378, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616267291447, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Dataflow.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616267292040, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616267292364, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616267289704, "dur": 2859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749616267292564, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267292807, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749616267294326, "dur": 652, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616267295479, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616267295845, "dur": 530, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Session.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616267294999, "dur": 1943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749616267296985, "dur": 882454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267146733, "dur": 14905, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267161642, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616267161740, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267161873, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616267162073, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749616267162073, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_F01CBB7E8932E0CB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616267162158, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_20B214415B84035D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616267162266, "dur": 504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267162773, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267163012, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267163115, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267163590, "dur": 324, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1749616267163915, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267164707, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267164910, "dur": 840, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_StyleSheetEditor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749616267165750, "dur": 937, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_StyleAssetMenu.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749616267164876, "dur": 1985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267166861, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267167083, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267167293, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267167517, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267167720, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267167968, "dur": 762, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Merge\\Developer\\DirectoryConflicts\\DivergentMoveMenu.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749616267167936, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267168847, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267169552, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267169642, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267170025, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267170282, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616267170334, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267170418, "dur": 304, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749616267170735, "dur": 703, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749616267170390, "dur": 1314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749616267171704, "dur": 1134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267172859, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267173133, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616267173269, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267173372, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749616267173711, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267173854, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616267174021, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749616267174353, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267174452, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616267174557, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749616267174947, "dur": 113564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267288899, "dur": 414, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749616267289348, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749616267291661, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749616267288515, "dur": 3489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749616267292005, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267292153, "dur": 1264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749616267293417, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267293705, "dur": 1130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749616267294835, "dur": 1940, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616267296798, "dur": 882659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267146609, "dur": 14991, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267161613, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267161813, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267161873, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_3787B1E93F84ED7A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616267162153, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267162954, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749616267163788, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267164446, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267164686, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267164925, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267165158, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267165386, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267165611, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267165854, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267166084, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267166314, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267166844, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267167063, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267167299, "dur": 686, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Serialization\\SerializationVersionAttribute.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749616267167286, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267168180, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267168410, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267168628, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267168775, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267169462, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267169627, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267170025, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267170288, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616267170606, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749616267170996, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267171485, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267172142, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267172690, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267172866, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267172968, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267173133, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616267173259, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749616267173666, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616267173730, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749616267173941, "dur": 114559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267290775, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749616267288505, "dur": 2502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749616267291007, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267292089, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749616267291089, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749616267293776, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267294381, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749616267295718, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749616267293887, "dur": 2170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749616267296057, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267296189, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267296355, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267296444, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267296625, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616267296857, "dur": 882579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267146635, "dur": 14973, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267161619, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267161718, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267161793, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267161863, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_834724766976A551.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616267162151, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267162305, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_66549C0209C53F09.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616267162721, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267162779, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749616267163051, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749616267163577, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3258459190130463912.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749616267164451, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.OAuth.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267163938, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267165188, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267165438, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267165698, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267165937, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267166176, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267166880, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267167096, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267167301, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267167589, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267167804, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267168011, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267168228, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267168458, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267168661, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267168914, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267169232, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267169630, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267170022, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267170289, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616267170588, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267170550, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749616267171101, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267171161, "dur": 641, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267171806, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616267171963, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267172170, "dur": 1231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749616267173402, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267173568, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616267173675, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749616267174365, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616267174485, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749616267174969, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616267175051, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749616267175271, "dur": 113243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267288580, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267288763, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267289219, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267289322, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267291061, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267288516, "dur": 3456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749616267291972, "dur": 2435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267295233, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267295767, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267295846, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267296205, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616267294412, "dur": 2182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749616267296600, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616267296673, "dur": 882742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267146662, "dur": 14958, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267161673, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1749616267161623, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_094A556A01D249DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616267161800, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_88B56BCE4990CA03.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616267162143, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267162213, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267162752, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267163041, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1749616267163228, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267163366, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267163749, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267163956, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267164631, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267164967, "dur": 765, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_EditorPanelUI.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749616267164906, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267165888, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267166153, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267166378, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267166895, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267167300, "dur": 976, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\Memory.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749616267167115, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267168296, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267168518, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267168771, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267169001, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267169243, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267169626, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267170026, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267170288, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616267170380, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267170735, "dur": 310, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616267171183, "dur": 627, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616267170575, "dur": 1320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616267171973, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616267172344, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616267172099, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616267172544, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267172690, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616267172867, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616267173305, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267173535, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267173850, "dur": 118127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267294017, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616267291978, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749616267294321, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267295766, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616267295904, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616267294401, "dur": 2136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749616267296538, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267296622, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616267296748, "dur": 882690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267146698, "dur": 14928, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267161679, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1749616267161629, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_699CA9E1D209A267.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749616267161746, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267162170, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267162606, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267162678, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267163170, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267163589, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11344994280883157806.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749616267163721, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267164451, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Core.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749616267163777, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267164983, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267165261, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267165482, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267165802, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267166031, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267166388, "dur": 929, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\BinaryExpression.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749616267166274, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267167423, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267167642, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267167848, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267168081, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267168294, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267168517, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267168755, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267168960, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267169218, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267169630, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267170022, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267170303, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749616267170420, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749616267171183, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749616267171429, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMPro_Private.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749616267170901, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749616267171568, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267171629, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267171692, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749616267171788, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267172004, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749616267172653, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267172857, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267172976, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267173224, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267173525, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267173842, "dur": 114675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267289714, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.KeyPerFile.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749616267288519, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749616267291049, "dur": 5487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267296647, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616267297041, "dur": 882382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267146725, "dur": 14907, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267161676, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1749616267161635, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21B473704285E949.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616267161732, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267161940, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749616267161939, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_188ECFE2CFFCF3FE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616267162050, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267162355, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267162441, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267162536, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267162592, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749616267162951, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749616267163046, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749616267163583, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749616267163771, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267164462, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267164718, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267164971, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267165216, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267165439, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267165694, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267165944, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267166173, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267166676, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267166916, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267167141, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267167354, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267167578, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267167801, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267168007, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267168229, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267168454, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267168677, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267168922, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267169221, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267169628, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267170023, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267170287, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616267170386, "dur": 665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267171051, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616267171190, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749616267171618, "dur": 793, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267172439, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267172691, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267172862, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267172972, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267173219, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267173514, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267173837, "dur": 1244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616267175081, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616267175215, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749616267175487, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616267175566, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749616267175748, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749616267176401, "dur": 856609, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749616268034150, "dur": 22130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749616268034149, "dur": 23069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749616268058838, "dur": 187, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616268059080, "dur": 89883, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749616268153013, "dur": 25830, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749616268153011, "dur": 25835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749616268178866, "dur": 502, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749616267146753, "dur": 14892, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267161692, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1749616267161649, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0998A984E55471CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616267161813, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267161869, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_2A2DD78CDAF18E7D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616267162144, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267162306, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_5A19F49AB3243238.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616267162825, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267163029, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749616267163213, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267163499, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749616267163773, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267164345, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267164539, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267164777, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267165016, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267165272, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267165714, "dur": 1609, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Editor\\LayoutOverlay\\LayoutOverlayUtility.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749616267165504, "dur": 1921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267167425, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267167664, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267167875, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267168097, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267168315, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267168853, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267169577, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267169631, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267170022, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267170415, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616267170627, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267170793, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749616267171162, "dur": 1196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267172459, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616267172578, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749616267173024, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267173229, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267173510, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267173835, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616267173950, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749616267174300, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267174406, "dur": 116672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267291079, "dur": 2358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749616267293437, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267293503, "dur": 1948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749616267295451, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267295515, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749616267295815, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267296011, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749616267296315, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267296426, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616267296657, "dur": 856356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616268153015, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749616268153015, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749616268153119, "dur": 847, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749616268153972, "dur": 25472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267146793, "dur": 14859, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267161655, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5AA67F5B53C44BAD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749616267161990, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_CFED14670A739CE6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749616267162177, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267162370, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267162615, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267163049, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749616267163217, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267163383, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267163578, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749616267163718, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267163916, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267164679, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267164840, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267164992, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267165148, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267165293, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267165445, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267165830, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267165979, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267166130, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267166268, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267166723, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267166937, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267167155, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267167388, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267167602, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267167822, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267168030, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267168245, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267169228, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267169633, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267170027, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267170286, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749616267170736, "dur": 457, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749616267170725, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749616267171587, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267171674, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749616267172137, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.Sprite.Editor.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749616267171808, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749616267172232, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267172334, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267172401, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267172692, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267172859, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267172992, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267173216, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267173519, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267173848, "dur": 114828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267288677, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749616267289819, "dur": 1074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749616267291971, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749616267290930, "dur": 1302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749616267292232, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267292364, "dur": 2033, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749616267292312, "dur": 3090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749616267295446, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267296321, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267296591, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616267296667, "dur": 882746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267146824, "dur": 14843, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267161683, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_006DC43AA5DD858A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616267161736, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267162162, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267162305, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2A9C520E2391C187.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616267162721, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267162943, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1749616267163256, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267163575, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749616267163775, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267164495, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267164669, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267165031, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\Replacing\\PathReplacing.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749616267164910, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267165597, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267165748, "dur": 619, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Description\\UnitPortDescription.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749616267165735, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267166863, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267167079, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267167374, "dur": 1544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\IOptimizedInvoker.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749616267167300, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267169063, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267169328, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267169636, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267170018, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267170425, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616267170826, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749616267170536, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749616267170879, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267171429, "dur": 288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.ref.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616267171428, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616267171718, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267171817, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616267171901, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749616267172148, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267172260, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267172698, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267172963, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267173210, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267173526, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267173834, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616267174151, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616267174007, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749616267174353, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267174463, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616267174582, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749616267175143, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749616267175380, "dur": 114317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267289701, "dur": 1158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749616267291972, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616267290906, "dur": 1300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749616267292207, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267292814, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616267293801, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616267292461, "dur": 2141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749616267294603, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267294662, "dur": 1084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749616267295843, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267296022, "dur": 601, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616267296623, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616267296840, "dur": 882609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267146839, "dur": 14876, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267161738, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267161923, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267162037, "dur": 481, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616267162037, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_270C5DEE22A99EDB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749616267162520, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267162656, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267162716, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267163078, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749616267163450, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267163577, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749616267163765, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267164337, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-util-l1-1-0.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616267164964, "dur": 3438, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-1-0.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616267164277, "dur": 4356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267168634, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267169018, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267169217, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267169634, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267170027, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267170426, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749616267170541, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749616267170592, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749616267170735, "dur": 454, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616267171428, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616267170729, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749616267171786, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267171888, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267172060, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267172697, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267172871, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267172987, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267173196, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267173533, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267173854, "dur": 114658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267288527, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616267288515, "dur": 2523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749616267291038, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267292088, "dur": 284, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616267291099, "dur": 1909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749616267293008, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267293070, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749616267294160, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616267294388, "dur": 1336, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InternalAPIEditorBridge.001.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616267295767, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616267294388, "dur": 2411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749616267296842, "dur": 882600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267146860, "dur": 14860, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267161750, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267161877, "dur": 314, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_C7A46CFE1C6B2861.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749616267162204, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267162608, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749616267162945, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749616267163032, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749616267163120, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267163592, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1749616267164411, "dur": 1344, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Cors.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749616267163930, "dur": 2377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267166344, "dur": 972, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Utilities\\SearchUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749616267166308, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267167504, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267167716, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267167966, "dur": 1272, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Merge\\DrawMergeOverview.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749616267167921, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267169385, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267169629, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267170021, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267170291, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749616267170470, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267170585, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749616267170734, "dur": 465, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749616267170531, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749616267171603, "dur": 655, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267172268, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267172343, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267172746, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267172901, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267173004, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267173238, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267173519, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267173836, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267174369, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749616267174484, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749616267174792, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267174893, "dur": 113623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267289142, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749616267289249, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749616267289860, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpLogging.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749616267290137, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Json.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749616267291520, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749616267288519, "dur": 3449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749616267291968, "dur": 4751, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616267296743, "dur": 882710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267146884, "dur": 14864, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267161777, "dur": 1007, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267162831, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267163673, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749616267163864, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749616267164043, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749616267164627, "dur": 3355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749616267168067, "dur": 1182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749616267163116, "dur": 6449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749616267169689, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749616267170080, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749616267170418, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749616267170717, "dur": 473, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749616267170346, "dur": 1030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749616267171377, "dur": 975, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267172390, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267172676, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749616267172844, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749616267172978, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749616267173394, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267173507, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1749616267173849, "dur": 100, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267174178, "dur": 112869, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1749616267288500, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749616267289670, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267289820, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749616267290938, "dur": 1150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749616267292088, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267292142, "dur": 1933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749616267294075, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267295053, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749616267294649, "dur": 1938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749616267296587, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616267296671, "dur": 882746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267146907, "dur": 14847, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267161787, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267161851, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3C3E151BE5CA996B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749616267162006, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749616267162005, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_B5228D2EF183D9AF.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749616267162288, "dur": 501, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749616267162791, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267163094, "dur": 659, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1749616267163753, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267164734, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267165109, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267165369, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267165593, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267165845, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267166064, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267166296, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267166815, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267167044, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267167273, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267167479, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267167687, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267167901, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267168108, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267168326, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267168708, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749616267168534, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267169251, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267169640, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267170017, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267170593, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749616267170704, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749616267171231, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267171319, "dur": 1235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267172554, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267172742, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267172913, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267173002, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267173224, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267173530, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267173852, "dur": 114654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267288507, "dur": 1660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749616267290167, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267291008, "dur": 1231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749616267292239, "dur": 4387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616267296677, "dur": 882748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267146936, "dur": 14822, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267161785, "dur": 505, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267162290, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E5F938C6E667C796.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749616267162947, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749616267163179, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267163553, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749616267163637, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267164442, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Json.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616267164976, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encodings.Web.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616267163756, "dur": 2009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267165765, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267166037, "dur": 2391, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Sum.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749616267166002, "dur": 2566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267168568, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267168751, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267168907, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267169479, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267169637, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267170031, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267170424, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749616267170598, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749616267170991, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267171051, "dur": 384, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749616267171463, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749616267171586, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267172138, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Animation\\AnimationClipCurveCache.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749616267171675, "dur": 981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749616267172657, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267172935, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267172985, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267173203, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267173526, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267173849, "dur": 114658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267288783, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616267288925, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616267289343, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616267289874, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616267288517, "dur": 3235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749616267291752, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267292363, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616267292360, "dur": 1232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749616267293592, "dur": 2116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267295709, "dur": 891, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749616267296614, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616267296736, "dur": 882711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267146969, "dur": 14833, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267161818, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267161930, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267162021, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B63D4BDCF9722657.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749616267162466, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267162859, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267163123, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267163583, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749616267163763, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267164620, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267164784, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267164947, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267165108, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267165265, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267165421, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267165574, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267165708, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267165871, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267166013, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267166163, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267166307, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267166867, "dur": 2048, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_5.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749616267166808, "dur": 2218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267169027, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267169216, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267169635, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267170026, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267170284, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749616267170418, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\Editor\\DOTweenEditor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616267170584, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616267171183, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\Action_6.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749616267170416, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749616267171667, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267171755, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749616267172065, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616267172342, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616267171883, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749616267173007, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749616267173081, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749616267173336, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267173426, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267173509, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267173669, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749616267173779, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749616267174076, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267174281, "dur": 117699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267291981, "dur": 1191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749616267293206, "dur": 1101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749616267294308, "dur": 785, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267295480, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-2-0.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616267296199, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616267295096, "dur": 1274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749616267296427, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616267296664, "dur": 882757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267146990, "dur": 14842, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267161847, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616267161835, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_37A60B8D84ADA88C.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749616267161905, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267162165, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616267162164, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_425C305CB5B8A325.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749616267162298, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749616267162609, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267162901, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267163055, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1749616267163167, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267163586, "dur": 347, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749616267164589, "dur": 1645, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cryptography.KeyDerivation.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616267166274, "dur": 2604, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.CookiePolicy.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616267163934, "dur": 4944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267168878, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267169449, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267169636, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267170019, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267170585, "dur": 461, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616267171182, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616267170424, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749616267171697, "dur": 864, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267172596, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749616267172878, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749616267173127, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267173467, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267173533, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267173857, "dur": 114669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267288527, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749616267289675, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267289736, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749616267290873, "dur": 1110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267292364, "dur": 2033, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\dbgshim.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616267291988, "dur": 3064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749616267295052, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267295614, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749616267296170, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616267296667, "dur": 882752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267147009, "dur": 14827, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267161930, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267162474, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_965F5DAE5AE88371.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749616267162585, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267163003, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267163087, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749616267163261, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267163663, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267163802, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267164333, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267165084, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267165224, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267165378, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267165736, "dur": 2138, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Options\\IUnitOption.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749616267165604, "dur": 2277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267167881, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267168027, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267168171, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267168316, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267168472, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267168615, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267168773, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267169231, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267169632, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267170028, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267170426, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749616267170507, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267171062, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749616267171734, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749616267171796, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267172444, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749616267172437, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749616267172863, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267172992, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267173134, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749616267173369, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749616267173774, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749616267174152, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749616267173927, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749616267174441, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267174527, "dur": 114016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267288544, "dur": 1124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749616267289669, "dur": 7036, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616267296733, "dur": 882702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267147029, "dur": 14814, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267161990, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9C559E57E505F9F5.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749616267162306, "dur": 577, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749616267162890, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749616267163045, "dur": 555, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749616267163602, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616267163704, "dur": 577, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\Editor\\DOTweenEditor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616267164629, "dur": 289, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616267164918, "dur": 3160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616267168087, "dur": 647, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616267169073, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616267163601, "dur": 5895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749616267169563, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267169644, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267170017, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267170736, "dur": 734, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616267170595, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749616267171863, "dur": 805, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267172668, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749616267172904, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267173003, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267173141, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749616267173289, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749616267173499, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267173593, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267173843, "dur": 114666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267288510, "dur": 1159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749616267289669, "dur": 1086, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267290759, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749616267291867, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267292394, "dur": 1073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749616267293467, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267293767, "dur": 1480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749616267295247, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267295367, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267295478, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616267295744, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267295997, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267296333, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267296470, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267296646, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616267296992, "dur": 882442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616268184053, "dur": 2503, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26084, "tid": 564, "ts": 1749616268199506, "dur": 1708, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26084, "tid": 564, "ts": 1749616268201252, "dur": 2776, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26084, "tid": 564, "ts": 1749616268195344, "dur": 9220, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}