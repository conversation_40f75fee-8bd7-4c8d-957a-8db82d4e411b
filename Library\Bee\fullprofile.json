{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26084, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26084, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26084, "tid": 603, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26084, "tid": 603, "ts": 1749616503192525, "dur": 13, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26084, "tid": 603, "ts": 1749616503192548, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26084, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26084, "tid": 1, "ts": 1749616502800402, "dur": 1327, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749616502801730, "dur": 11651, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26084, "tid": 1, "ts": 1749616502813385, "dur": 18291, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26084, "tid": 603, "ts": 1749616503192555, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 26084, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502800355, "dur": 37242, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502837600, "dur": 354534, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502837612, "dur": 31, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502837646, "dur": 12, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502837660, "dur": 1896, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839559, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839602, "dur": 1, "ph": "X", "name": "ProcessMessages 1738", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839604, "dur": 29, "ph": "X", "name": "ReadAsync 1738", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839637, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839639, "dur": 48, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839688, "dur": 1, "ph": "X", "name": "ProcessMessages 1728", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839690, "dur": 25, "ph": "X", "name": "ReadAsync 1728", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839718, "dur": 6, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839726, "dur": 27, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839758, "dur": 1, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839761, "dur": 50, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839814, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839851, "dur": 1, "ph": "X", "name": "ProcessMessages 1420", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839853, "dur": 23, "ph": "X", "name": "ReadAsync 1420", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839878, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839882, "dur": 31, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839915, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839917, "dur": 31, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839954, "dur": 23, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839980, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502839983, "dur": 28, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840016, "dur": 1, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840018, "dur": 24, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840048, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840050, "dur": 21, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840072, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840074, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840110, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840114, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840143, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840146, "dur": 36, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840184, "dur": 1, "ph": "X", "name": "ProcessMessages 1116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840186, "dur": 28, "ph": "X", "name": "ReadAsync 1116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840216, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840218, "dur": 28, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840250, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840252, "dur": 30, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840284, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840286, "dur": 25, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840315, "dur": 32, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840349, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840351, "dur": 25, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840379, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840407, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840409, "dur": 34, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840445, "dur": 1, "ph": "X", "name": "ProcessMessages 1193", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840447, "dur": 28, "ph": "X", "name": "ReadAsync 1193", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840477, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840478, "dur": 23, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840505, "dur": 32, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840539, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840541, "dur": 27, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840570, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840572, "dur": 37, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840611, "dur": 1, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840613, "dur": 24, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840640, "dur": 45, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840687, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840721, "dur": 1, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840723, "dur": 27, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840754, "dur": 28, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840785, "dur": 32, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840820, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840822, "dur": 26, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840851, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840877, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840878, "dur": 17, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840898, "dur": 31, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840932, "dur": 1, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840933, "dur": 25, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840961, "dur": 18, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840981, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502840982, "dur": 23, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841008, "dur": 34, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841048, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841089, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841091, "dur": 158, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841251, "dur": 3, "ph": "X", "name": "ProcessMessages 4292", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841255, "dur": 23, "ph": "X", "name": "ReadAsync 4292", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841280, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841281, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841304, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841332, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841334, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841356, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841358, "dur": 30, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841391, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841392, "dur": 31, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841425, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841427, "dur": 22, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841452, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841479, "dur": 2, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841483, "dur": 30, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841514, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841516, "dur": 21, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841541, "dur": 19, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841564, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841592, "dur": 73, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841668, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841701, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841702, "dur": 22, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841727, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841728, "dur": 29, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841759, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841761, "dur": 18, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841784, "dur": 21, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841808, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841842, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841844, "dur": 31, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841878, "dur": 24, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841904, "dur": 4, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841909, "dur": 75, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502841987, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842012, "dur": 29, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842043, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842044, "dur": 22, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842071, "dur": 29, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842101, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842103, "dur": 23, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842130, "dur": 45, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842177, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842179, "dur": 23, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842203, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842206, "dur": 31, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842238, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842240, "dur": 18, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842261, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842289, "dur": 20, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842313, "dur": 32, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842346, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842348, "dur": 22, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842372, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842373, "dur": 28, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842403, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842405, "dur": 17, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842425, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842451, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842452, "dur": 39, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842493, "dur": 1, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842495, "dur": 22, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842521, "dur": 41, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842565, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842591, "dur": 30, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842623, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842625, "dur": 28, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842654, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842656, "dur": 26, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842684, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842686, "dur": 28, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842716, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842718, "dur": 27, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842749, "dur": 27, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842778, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842779, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842803, "dur": 18, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842824, "dur": 22, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842849, "dur": 28, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842879, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842881, "dur": 27, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842911, "dur": 29, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842942, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842943, "dur": 29, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842974, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842977, "dur": 19, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502842998, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843020, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843022, "dur": 19, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843043, "dur": 20, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843067, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843091, "dur": 83, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843177, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843203, "dur": 1, "ph": "X", "name": "ProcessMessages 1392", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843206, "dur": 31, "ph": "X", "name": "ReadAsync 1392", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843240, "dur": 64, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843306, "dur": 1, "ph": "X", "name": "ProcessMessages 891", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843308, "dur": 35, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843345, "dur": 1, "ph": "X", "name": "ProcessMessages 1288", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843347, "dur": 15, "ph": "X", "name": "ReadAsync 1288", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843365, "dur": 17, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843386, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843424, "dur": 33, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843464, "dur": 2, "ph": "X", "name": "ProcessMessages 1481", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843467, "dur": 36, "ph": "X", "name": "ReadAsync 1481", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843506, "dur": 2, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843509, "dur": 37, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843549, "dur": 1, "ph": "X", "name": "ProcessMessages 1081", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843552, "dur": 27, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843582, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843584, "dur": 57, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843651, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843686, "dur": 1, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843688, "dur": 25, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843715, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843718, "dur": 31, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843750, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843752, "dur": 40, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843794, "dur": 1, "ph": "X", "name": "ProcessMessages 1278", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843796, "dur": 21, "ph": "X", "name": "ReadAsync 1278", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843821, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843842, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843844, "dur": 24, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843870, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843872, "dur": 40, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843916, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843954, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843956, "dur": 28, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843986, "dur": 4, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502843991, "dur": 21, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844014, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844016, "dur": 39, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844057, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844058, "dur": 28, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844088, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844090, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844110, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844135, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844137, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844160, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844161, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844185, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844186, "dur": 56, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844246, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844275, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844277, "dur": 19, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844298, "dur": 25, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844328, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844330, "dur": 24, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844356, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844357, "dur": 25, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844385, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844413, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844415, "dur": 20, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844436, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844438, "dur": 28, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844470, "dur": 24, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844496, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844497, "dur": 25, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844525, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844552, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844577, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844579, "dur": 21, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844603, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844605, "dur": 37, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844645, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844647, "dur": 24, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844673, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844674, "dur": 32, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844708, "dur": 1, "ph": "X", "name": "ProcessMessages 1010", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844710, "dur": 29, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844741, "dur": 2, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844744, "dur": 27, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844773, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844774, "dur": 184, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844964, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844985, "dur": 13, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502844999, "dur": 11, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845012, "dur": 12, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845026, "dur": 10, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845038, "dur": 12, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845052, "dur": 10, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845063, "dur": 10, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845075, "dur": 12, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845088, "dur": 11, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845100, "dur": 23, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845127, "dur": 18, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845146, "dur": 17, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845166, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845181, "dur": 15, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845197, "dur": 11, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845209, "dur": 11, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845222, "dur": 10, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845233, "dur": 10, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845244, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845246, "dur": 8, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845255, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845298, "dur": 10, "ph": "X", "name": "ReadAsync 1292", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845310, "dur": 10, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845322, "dur": 11, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845335, "dur": 30, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845366, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845378, "dur": 10, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845390, "dur": 10, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845402, "dur": 10, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845413, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845414, "dur": 11, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845427, "dur": 10, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845438, "dur": 10, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845450, "dur": 9, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845460, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845472, "dur": 10, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845484, "dur": 10, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845495, "dur": 10, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845507, "dur": 13, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845522, "dur": 9, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845533, "dur": 20, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845555, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845566, "dur": 9, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845576, "dur": 10, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845588, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845605, "dur": 11, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845617, "dur": 25, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845644, "dur": 10, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845655, "dur": 8, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845665, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845685, "dur": 13, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845700, "dur": 12, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845713, "dur": 13, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845728, "dur": 9, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845738, "dur": 21, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845761, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845774, "dur": 12, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845787, "dur": 11, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845799, "dur": 11, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845811, "dur": 14, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845827, "dur": 10, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845839, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845840, "dur": 11, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845852, "dur": 11, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845864, "dur": 9, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845875, "dur": 14, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845890, "dur": 12, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845903, "dur": 11, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845915, "dur": 10, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845927, "dur": 16, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845945, "dur": 33, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845979, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502845991, "dur": 8, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846001, "dur": 11, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846013, "dur": 12, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846027, "dur": 11, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846040, "dur": 12, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846053, "dur": 10, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846065, "dur": 17, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846084, "dur": 10, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846095, "dur": 10, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846107, "dur": 11, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846120, "dur": 12, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846134, "dur": 11, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846146, "dur": 14, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846161, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846185, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846198, "dur": 13, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846213, "dur": 11, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846225, "dur": 11, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846237, "dur": 10, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846249, "dur": 10, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846260, "dur": 38, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846300, "dur": 11, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846313, "dur": 11, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846325, "dur": 12, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846339, "dur": 12, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846352, "dur": 9, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846363, "dur": 12, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846377, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846401, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846416, "dur": 24, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846443, "dur": 12, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846456, "dur": 7, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846464, "dur": 9, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846475, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846493, "dur": 12, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846507, "dur": 10, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846519, "dur": 13, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846534, "dur": 11, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846546, "dur": 10, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846558, "dur": 37, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846597, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846608, "dur": 10, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846620, "dur": 10, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846632, "dur": 11, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846644, "dur": 11, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846656, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846657, "dur": 14, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846673, "dur": 9, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846684, "dur": 13, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846698, "dur": 10, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846710, "dur": 10, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846722, "dur": 10, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846734, "dur": 10, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846746, "dur": 10, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846758, "dur": 11, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846770, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846787, "dur": 36, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846824, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846836, "dur": 14, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846852, "dur": 11, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846865, "dur": 11, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846877, "dur": 11, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846889, "dur": 9, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846900, "dur": 13, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846915, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846927, "dur": 10, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846938, "dur": 10, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846950, "dur": 10, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846962, "dur": 13, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846977, "dur": 12, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502846990, "dur": 9, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847001, "dur": 34, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847037, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847052, "dur": 10, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847064, "dur": 16, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847082, "dur": 13, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847097, "dur": 12, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847110, "dur": 10, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847122, "dur": 11, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847134, "dur": 14, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847149, "dur": 10, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847172, "dur": 16, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847190, "dur": 10, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847202, "dur": 11, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847214, "dur": 9, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847224, "dur": 47, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847272, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847298, "dur": 39, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847339, "dur": 11, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847351, "dur": 9, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847362, "dur": 1, "ph": "X", "name": "ProcessMessages 125", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847363, "dur": 11, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847376, "dur": 63, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847440, "dur": 1, "ph": "X", "name": "ProcessMessages 1606", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847441, "dur": 32, "ph": "X", "name": "ReadAsync 1606", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847475, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847500, "dur": 10, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847512, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847532, "dur": 11, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847544, "dur": 11, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847557, "dur": 16, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847574, "dur": 10, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847586, "dur": 10, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847598, "dur": 11, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847610, "dur": 13, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847625, "dur": 9, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847635, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847658, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847670, "dur": 11, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847682, "dur": 11, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847695, "dur": 13, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847709, "dur": 11, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847722, "dur": 10, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847734, "dur": 10, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847745, "dur": 10, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847757, "dur": 10, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847768, "dur": 11, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847780, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847799, "dur": 10, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847811, "dur": 11, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847824, "dur": 10, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847836, "dur": 62, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847900, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847912, "dur": 10, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847923, "dur": 26, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847950, "dur": 18, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847970, "dur": 12, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847983, "dur": 10, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502847995, "dur": 11, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848007, "dur": 24, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848033, "dur": 14, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848048, "dur": 12, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848062, "dur": 14, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848077, "dur": 11, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848090, "dur": 10, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848102, "dur": 48, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848152, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848165, "dur": 11, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848177, "dur": 11, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848190, "dur": 10, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848201, "dur": 8, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848211, "dur": 10, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848222, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848234, "dur": 24, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848260, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848273, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848296, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848298, "dur": 10, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848309, "dur": 45, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848355, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848368, "dur": 33, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848402, "dur": 32, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848435, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848447, "dur": 18, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848466, "dur": 8, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848476, "dur": 50, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848527, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848539, "dur": 37, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848578, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848597, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848600, "dur": 10, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848612, "dur": 11, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848624, "dur": 35, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848661, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848676, "dur": 11, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848689, "dur": 13, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848704, "dur": 8, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848713, "dur": 32, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848747, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848766, "dur": 13, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848780, "dur": 10, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848792, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848828, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848844, "dur": 10, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848856, "dur": 11, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848869, "dur": 9, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848879, "dur": 104, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848984, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502848996, "dur": 11, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849008, "dur": 10, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849019, "dur": 10, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849031, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849069, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849081, "dur": 11, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849094, "dur": 10, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849105, "dur": 10, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849118, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849157, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849171, "dur": 16, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849189, "dur": 9, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849199, "dur": 37, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849237, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849249, "dur": 38, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849289, "dur": 84, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849375, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849388, "dur": 14, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849403, "dur": 10, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849415, "dur": 10, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849427, "dur": 32, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849461, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849477, "dur": 10, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849488, "dur": 11, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849501, "dur": 10, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849512, "dur": 32, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849546, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849561, "dur": 10, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849573, "dur": 11, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849586, "dur": 9, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849597, "dur": 33, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849631, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849645, "dur": 10, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849656, "dur": 9, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849667, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849689, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849701, "dur": 33, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849735, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849749, "dur": 10, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849760, "dur": 22, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849784, "dur": 31, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849816, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849829, "dur": 12, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849844, "dur": 13, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849858, "dur": 45, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849908, "dur": 29, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849938, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849940, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502849969, "dur": 39, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850012, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850049, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850051, "dur": 18, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850073, "dur": 97, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850173, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850179, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850214, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850216, "dur": 17, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850234, "dur": 4, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850239, "dur": 21, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850267, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850289, "dur": 4, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850294, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850315, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850317, "dur": 28, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850348, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850353, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850381, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850383, "dur": 28, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850414, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850417, "dur": 13, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850431, "dur": 12, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850445, "dur": 11, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850457, "dur": 11, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850469, "dur": 9, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850479, "dur": 61, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850543, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850561, "dur": 13, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850576, "dur": 10, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850587, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850588, "dur": 34, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850624, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850637, "dur": 10, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850648, "dur": 10, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850660, "dur": 10, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850672, "dur": 13, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850686, "dur": 9, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850696, "dur": 32, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850730, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850743, "dur": 18, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850763, "dur": 14, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850778, "dur": 17, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850796, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850819, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850831, "dur": 11, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850844, "dur": 9, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850855, "dur": 12, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850868, "dur": 35, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850905, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850924, "dur": 15, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850940, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850943, "dur": 9, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850954, "dur": 29, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850985, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502850997, "dur": 11, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851009, "dur": 15, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851026, "dur": 8, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851035, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851037, "dur": 30, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851068, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851082, "dur": 10, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851093, "dur": 18, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851113, "dur": 56, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851171, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851184, "dur": 24, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851210, "dur": 9, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851220, "dur": 31, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851253, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851266, "dur": 14, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851282, "dur": 11, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851295, "dur": 8, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851304, "dur": 31, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851337, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851349, "dur": 13, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851364, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851384, "dur": 30, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851416, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851430, "dur": 12, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851444, "dur": 17, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851462, "dur": 56, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851519, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851532, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851534, "dur": 11, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851546, "dur": 12, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851560, "dur": 9, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851570, "dur": 32, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851603, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851616, "dur": 10, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851627, "dur": 11, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851640, "dur": 9, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851651, "dur": 31, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851683, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851697, "dur": 11, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851709, "dur": 16, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851726, "dur": 32, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851761, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851773, "dur": 10, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851785, "dur": 14, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851801, "dur": 8, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851810, "dur": 32, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851844, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851855, "dur": 20, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851876, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851888, "dur": 10, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851900, "dur": 11, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851912, "dur": 32, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851946, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851958, "dur": 11, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851970, "dur": 12, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851984, "dur": 10, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502851995, "dur": 10, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852006, "dur": 11, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852019, "dur": 11, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852031, "dur": 10, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852043, "dur": 10, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852054, "dur": 13, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852069, "dur": 9, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852079, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852108, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852120, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852142, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852159, "dur": 12, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852172, "dur": 12, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852185, "dur": 10, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852197, "dur": 13, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852212, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852238, "dur": 10, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852249, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852282, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852296, "dur": 11, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852309, "dur": 11, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852322, "dur": 9, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852333, "dur": 31, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852366, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852377, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852399, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852411, "dur": 10, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852422, "dur": 10, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852434, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852468, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852469, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852485, "dur": 13, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852500, "dur": 11, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852512, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852547, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852562, "dur": 10, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852574, "dur": 16, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852591, "dur": 34, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852626, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852638, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852661, "dur": 10, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852673, "dur": 33, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852708, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852719, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852740, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852753, "dur": 13, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852767, "dur": 8, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852778, "dur": 32, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852812, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852827, "dur": 16, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852845, "dur": 10, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852856, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852892, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852912, "dur": 10, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852924, "dur": 10, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852936, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852972, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852985, "dur": 11, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502852998, "dur": 10, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853009, "dur": 9, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853020, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853053, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853070, "dur": 39, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853111, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853127, "dur": 17, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853146, "dur": 11, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853159, "dur": 10, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853170, "dur": 11, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853183, "dur": 11, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853195, "dur": 9, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853206, "dur": 12, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853220, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853255, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853279, "dur": 18, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853299, "dur": 12, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853312, "dur": 9, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853323, "dur": 21, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853346, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853358, "dur": 10, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853369, "dur": 10, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853380, "dur": 8, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853389, "dur": 10, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853401, "dur": 2, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853403, "dur": 29, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853434, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853445, "dur": 92, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853541, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853563, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853565, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853593, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853596, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853618, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853621, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853643, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853666, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853681, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853699, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853721, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853742, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853761, "dur": 15, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853778, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853780, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853800, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853802, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853825, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853827, "dur": 18, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853847, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853864, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853865, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853886, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853891, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853911, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853948, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853975, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853977, "dur": 17, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502853999, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854017, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854019, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854036, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854054, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854056, "dur": 19, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854077, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854078, "dur": 20, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854100, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854102, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854121, "dur": 16, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854138, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854140, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854163, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854184, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854185, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854205, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854229, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854271, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854273, "dur": 21, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854295, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854297, "dur": 23, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854323, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854345, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854347, "dur": 16, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854366, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854386, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854387, "dur": 13, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854402, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854420, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854421, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854444, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854446, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854467, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854485, "dur": 15, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854504, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854529, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854531, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854556, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854558, "dur": 16, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854576, "dur": 15, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854594, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854595, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854620, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854621, "dur": 19, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854642, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854644, "dur": 15, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854661, "dur": 16, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854679, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854681, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854701, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854703, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854720, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854735, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854755, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854775, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854776, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854797, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854820, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854822, "dur": 20, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854846, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854865, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854884, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854901, "dur": 14, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854918, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854919, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854942, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854963, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854981, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502854997, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855016, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855037, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855058, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855073, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855092, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855094, "dur": 18, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855115, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855139, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855159, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855196, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855221, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855243, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502855260, "dur": 1644, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502856908, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502856926, "dur": 2592, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502859525, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502859545, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502859547, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502859570, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502859649, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502859670, "dur": 1143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502860817, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502860844, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502860868, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502860881, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861044, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861070, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861072, "dur": 154, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861228, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861248, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861270, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861288, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861301, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861339, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861357, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861407, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861421, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861514, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861531, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861662, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861676, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861692, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861772, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861791, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861806, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861822, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861838, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861855, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861868, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861889, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861906, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861924, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861937, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861958, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861972, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502861988, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862035, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862052, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862089, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862105, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862174, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862193, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862206, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862223, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862242, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862262, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862277, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862337, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862356, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862373, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862458, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862477, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862479, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862497, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862523, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862537, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862554, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862649, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862666, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862830, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862846, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862864, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862881, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862894, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862974, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502862993, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863021, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863036, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863056, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863074, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863103, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863115, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863136, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863152, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863179, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863193, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863209, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863233, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863257, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863270, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863289, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863305, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863323, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863341, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863359, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863372, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863390, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863407, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863423, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863442, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863458, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863476, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863493, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863591, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863607, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863647, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863664, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863731, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863748, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863766, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863783, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863795, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863851, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863870, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863886, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863899, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863933, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863946, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863963, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502863980, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864035, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864051, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864079, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864095, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864121, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864138, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864219, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864232, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864347, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864350, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864367, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864387, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864419, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864435, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864450, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864469, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864501, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864520, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864558, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864576, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864599, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864619, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864668, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864691, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864692, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864715, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864738, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864758, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864966, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864988, "dur": 7, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502864996, "dur": 20, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865018, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865020, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865056, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865076, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865113, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865132, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865299, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865324, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865345, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865365, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865367, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865432, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865452, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865473, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865474, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865550, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865583, "dur": 132, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865719, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865736, "dur": 67, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865808, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865835, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865859, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865970, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502865992, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502866131, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502866160, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502866164, "dur": 106555, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502972731, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502972735, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502972753, "dur": 24, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502972778, "dur": 3623, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976410, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976413, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976432, "dur": 82, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976520, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976534, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976598, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976613, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976627, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976636, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976811, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502976824, "dur": 368, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502977194, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502977208, "dur": 553, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502977763, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502977780, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502977876, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502977885, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502977894, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978157, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978168, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978270, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978278, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978351, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978364, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978534, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978542, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978619, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978629, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978637, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978645, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978792, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502978804, "dur": 290, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502979096, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502979104, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502979159, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502979169, "dur": 400, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502979571, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502979579, "dur": 207, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502979789, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502979800, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502979918, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502979927, "dur": 185, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980113, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980138, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980160, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980170, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980172, "dur": 457, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980631, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980642, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980789, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980797, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980967, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502980977, "dur": 529, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981507, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981516, "dur": 247, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981765, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981774, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981842, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981850, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981932, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981938, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981951, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981961, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981974, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981982, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502981994, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982011, "dur": 7, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982019, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982029, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982039, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982050, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982059, "dur": 7, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982067, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982079, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982089, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982097, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982106, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982116, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982126, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982135, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982148, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982159, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982169, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982177, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982186, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982197, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982208, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982219, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982227, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982251, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982261, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982262, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982274, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982283, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982294, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982303, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982322, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982331, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982341, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982344, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982354, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982363, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982374, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982385, "dur": 8, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982395, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982404, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982414, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982424, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982433, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982442, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982450, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982458, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982468, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982477, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982485, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982536, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982546, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982558, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982571, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982581, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982591, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982622, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982632, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982652, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982665, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982739, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982747, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982812, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982822, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982839, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982847, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982857, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982866, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982883, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982893, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982905, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982939, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982950, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982958, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982977, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982988, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502982999, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983008, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983019, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983185, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983199, "dur": 213, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983419, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983422, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983469, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983471, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983504, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983582, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983583, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983656, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983667, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983685, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983702, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983713, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502983728, "dur": 290, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502984021, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502984030, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502984123, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616502984125, "dur": 26745, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503010886, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503010890, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503010908, "dur": 359, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503011268, "dur": 35379, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503046663, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503046671, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503046696, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503046702, "dur": 105462, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503152180, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503152183, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503152210, "dur": 28, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503152239, "dur": 4422, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503156670, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503156674, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503156686, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503156689, "dur": 1151, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503157849, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503157853, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503157900, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503157930, "dur": 26206, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503184152, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503184159, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503184184, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503184188, "dur": 511, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503184708, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503184714, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503184770, "dur": 61, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503184833, "dur": 869, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503185707, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503185744, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26084, "tid": 51539607552, "ts": 1749616503185746, "dur": 6380, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26084, "tid": 603, "ts": 1749616503192564, "dur": 2814, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26084, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26084, "tid": 47244640256, "ts": 1749616502800293, "dur": 31404, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749616502831699, "dur": 5278, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26084, "tid": 47244640256, "ts": 1749616502836979, "dur": 29, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26084, "tid": 603, "ts": 1749616503195380, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26084, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26084, "tid": 42949672960, "ts": 1749616502769042, "dur": 423130, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26084, "tid": 42949672960, "ts": 1749616502769118, "dur": 31098, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26084, "tid": 42949672960, "ts": 1749616503192176, "dur": 63, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26084, "tid": 42949672960, "ts": 1749616503192195, "dur": 22, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26084, "tid": 42949672960, "ts": 1749616503192242, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26084, "tid": 603, "ts": 1749616503195388, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749616502838136, "dur": 1534, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616502839677, "dur": 462, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616502840245, "dur": 52, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749616502840298, "dur": 350, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616502844926, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1749616502840662, "dur": 13933, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616502854605, "dur": 331315, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616503185924, "dur": 457, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616503186382, "dur": 59, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616503186441, "dur": 113, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616503186621, "dur": 79, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616503186763, "dur": 60, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616503186846, "dur": 957, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749616502840699, "dur": 13932, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502854633, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_EF2B337EBFC8EEA7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749616502855033, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502855190, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1749616502855754, "dur": 598, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749616502856352, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502856814, "dur": 969, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\FrameRateDisplayUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749616502857783, "dur": 3635, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\FileUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749616502856724, "dur": 4782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502861506, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502861987, "dur": 1702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502863690, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502864283, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749616502864352, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749616502864612, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502864932, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502865141, "dur": 116168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502981559, "dur": 275, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749616502981310, "dur": 1813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749616502983289, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502983738, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749616502984343, "dur": 201602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502840633, "dur": 13980, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502854615, "dur": 1784, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502856400, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502857118, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502857324, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502857748, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502858057, "dur": 1897, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Ports\\IUnitPortWidget.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749616502857981, "dur": 2078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502860059, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502860324, "dur": 1015, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\PendingChanges\\PendingChangesStatusSuccessNotificationContent.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749616502860209, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502861487, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502861988, "dur": 1514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502863681, "dur": 521, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616502864275, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616502863504, "dur": 1367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749616502864871, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502865227, "dur": 112475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502978946, "dur": 4355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749616502977704, "dur": 6273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749616502983978, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749616502984110, "dur": 201860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502840658, "dur": 13959, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502854712, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1749616502854686, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B87B178D29F0F556.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616502855005, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502855102, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749616502855101, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616502855530, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749616502855647, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749616502856051, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1410676725171572189.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749616502856375, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502856957, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\TimelineDataSource.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749616502856813, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502857523, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502857761, "dur": 844, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Editor\\SkinningModule\\VisibilityTool\\VisibilityToolColumnHeader.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749616502857760, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502859220, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502859480, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502859707, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502859971, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502860342, "dur": 1020, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\CoverageFormats\\OpenCover\\Model\\File.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749616502860206, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502861761, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502861976, "dur": 1387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502863364, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749616502863495, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749616502863490, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749616502864122, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749616502864286, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502864342, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502864408, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502864887, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502865129, "dur": 110574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502975704, "dur": 1911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749616502977689, "dur": 1267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749616502977663, "dur": 2503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749616502980166, "dur": 3182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502983353, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502983716, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749616502984071, "dur": 201894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502840688, "dur": 13937, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502854776, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502855105, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749616502855104, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616502855200, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749616502855292, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749616502855353, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749616502855448, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749616502855705, "dur": 251, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749616502856068, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13445476680238899994.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749616502856257, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502856575, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502857161, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502857642, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502857885, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502858203, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502858598, "dur": 1195, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarMultiply.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749616502858434, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502859879, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502860128, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502860439, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502860761, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502861359, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502861489, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502861982, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502862225, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616502862381, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616502862487, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749616502863010, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616502863127, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616502863242, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616502863354, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616502863682, "dur": 343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749616502864276, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749616502863612, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749616502864559, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502864920, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749616502865039, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749616502865478, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502865568, "dur": 110151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502975720, "dur": 1770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749616502977543, "dur": 1782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749616502979326, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502979428, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749616502981317, "dur": 1778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749616502983190, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502983374, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502983575, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502983967, "dur": 1195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749616502985195, "dur": 200728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502840730, "dur": 13907, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502854638, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_094A556A01D249DF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616502855035, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1749616502855021, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2A9C520E2391C187.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616502855328, "dur": 315, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1749616502855657, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1749616502855944, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749616502856212, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12697856024727907050.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749616502856392, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502856996, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502857164, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502857625, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502857865, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502858088, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502858323, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502859104, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502859341, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502859588, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502859815, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502860054, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502860320, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502860614, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502860844, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502861090, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502861794, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502861982, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502862229, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616502862412, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502862557, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749616502862931, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502863382, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502863507, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616502863682, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616502864276, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749616502863623, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749616502864517, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502864921, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749616502865225, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749616502865749, "dur": 109968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502975718, "dur": 1787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749616502977548, "dur": 1841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749616502979390, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502979507, "dur": 1736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749616502981272, "dur": 1791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749616502983631, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502983715, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749616502984019, "dur": 201896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502840757, "dur": 13885, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502854676, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616502854645, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_699CA9E1D209A267.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616502854764, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502855040, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502855239, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_965F5DAE5AE88371.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616502855362, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749616502855448, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1749616502855661, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749616502855799, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502855973, "dur": 387, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749616502857244, "dur": 765, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventLog.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616502856361, "dur": 1683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502858044, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502858299, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502858570, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502859221, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502859478, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502859625, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502859781, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502860382, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502860839, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502861080, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502861836, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502861984, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502862236, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616502862372, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502862430, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616502863032, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502863113, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502863681, "dur": 343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616502863399, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616502864136, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502864202, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616502864440, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616502864909, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616502865179, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616502865551, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502865658, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616502865764, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616502865885, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616502866474, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616502866578, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616502866885, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749616502866949, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616502867115, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616502867300, "dur": 144677, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749616503012969, "dur": 31178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616503012966, "dur": 32606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749616503047318, "dur": 370, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749616503047744, "dur": 105531, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749616503157587, "dur": 27580, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616503157585, "dur": 27585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749616503185200, "dur": 650, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749616502840781, "dur": 13867, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502854651, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21B473704285E949.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749616502855021, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502855375, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749616502855437, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749616502855758, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749616502856062, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749616502856239, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502856599, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502856988, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502857212, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502857665, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502858232, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502858487, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502859083, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502859222, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502859383, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502859624, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502859995, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502860272, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502860533, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502861233, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502861413, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502861465, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502861520, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502861977, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502862230, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749616502862402, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749616502862812, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749616502863248, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502863339, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749616502864157, "dur": 366, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Utils\\ManipulatorsUtils.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749616502864627, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\AnimatedParameterCache.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749616502863613, "dur": 1434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749616502865119, "dur": 110590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502975713, "dur": 1935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749616502977649, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502977965, "dur": 1801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749616502979767, "dur": 542, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616502980314, "dur": 1757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749616502982122, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749616502984009, "dur": 173578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749616503157590, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749616503157589, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749616503157795, "dur": 1176, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749616503158980, "dur": 26938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502840808, "dur": 13847, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502854657, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F3718B55843C9DB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616502854817, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502855142, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749616502855238, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_BE343FF0452B4331.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616502855444, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749616502855755, "dur": 642, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749616502856398, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Connections.Abstractions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749616502856398, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502857185, "dur": 1277, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\Events\\SessionMode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749616502857185, "dur": 2084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502859269, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502859541, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502859775, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502859985, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502860199, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502860602, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502860749, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502860884, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502861086, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502861690, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502861977, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502862232, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616502862396, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749616502862829, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616502862952, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749616502863338, "dur": 1097, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502864457, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616502864634, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749616502865034, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749616502864745, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749616502865447, "dur": 661, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502866130, "dur": 109584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502975717, "dur": 1917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749616502977634, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502977755, "dur": 1786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749616502979542, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502979948, "dur": 1802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749616502981784, "dur": 1870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749616502983730, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749616502984171, "dur": 201785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502840831, "dur": 13830, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502854685, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749616502854664, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0998A984E55471CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616502854781, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502855048, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502855399, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749616502855946, "dur": 439, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749616502856386, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502856931, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502857486, "dur": 979, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\CommandLineManager.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749616502857187, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502858602, "dur": 817, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Control\\Once.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749616502858592, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502859622, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502859834, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502860052, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502860208, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502860450, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502860619, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502860837, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502861105, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502861635, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502862002, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502862218, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616502862405, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749616502862946, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616502863357, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749616502863109, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749616502863676, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502864031, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502864169, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502864280, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616502864390, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749616502864627, "dur": 412, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749616502864567, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749616502865401, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502865512, "dur": 110185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502975713, "dur": 1826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749616502977539, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502977783, "dur": 1780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749616502979564, "dur": 683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502980253, "dur": 1810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749616502982064, "dur": 1091, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502983287, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502983636, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502983808, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749616502984057, "dur": 201856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502840867, "dur": 13800, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502854670, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5AA67F5B53C44BAD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749616502854977, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749616502854976, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_ADBEF0390C22E5A8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749616502855200, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502855327, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749616502855441, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749616502855943, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749616502856210, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749616502856659, "dur": 875, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.TagHelpers.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749616502856379, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502857542, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502857679, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502857849, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502857999, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502858578, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502859162, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502859317, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502859498, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502859664, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502859820, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502860471, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502860637, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502860844, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502860999, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502861136, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502861702, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502862004, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502862219, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749616502862283, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502862447, "dur": 886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749616502863368, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502863423, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749616502863494, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749616502864019, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Expression.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749616502863485, "dur": 1285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749616502864801, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502864884, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749616502865132, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502865226, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749616502865577, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502865746, "dur": 109954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502975702, "dur": 1644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749616502977346, "dur": 2340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502979690, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749616502980828, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502981292, "dur": 571, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749616502981075, "dur": 1681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749616502982757, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502983273, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502983461, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502983728, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749616502984165, "dur": 201817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502840900, "dur": 13772, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502855033, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502855362, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_20B214415B84035D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616502855486, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616502855885, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616502856080, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616502856326, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616502855580, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749616502858065, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502858328, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502858543, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502859091, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502859247, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502859439, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502859659, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502859871, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502860119, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502860705, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502860842, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502861214, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502861608, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502861983, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502862220, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616502862422, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749616502862845, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502863076, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616502863494, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616502863187, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749616502863729, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502864004, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502864177, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502864282, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749616502864424, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502864627, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616502865034, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616502864480, "dur": 1010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749616502865531, "dur": 110163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502975698, "dur": 1239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749616502976939, "dur": 720, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749616502977689, "dur": 1266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616502977666, "dur": 3025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749616502981293, "dur": 1822, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-libraryloader-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749616502980728, "dur": 3391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749616502984157, "dur": 201830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502840921, "dur": 13758, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502854681, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7027E12F917228B0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749616502854807, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1749616502854785, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C06C6D7BC8ACCF56.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749616502855392, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749616502855588, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749616502856252, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502856581, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502857011, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502857151, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502857888, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502858138, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502858285, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502858470, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502859097, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502859335, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502859530, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502859712, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502859993, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502860155, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502860962, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502861205, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502861492, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502861985, "dur": 1703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502863688, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502864281, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749616502864392, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749616502864636, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749616502865014, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749616502865292, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616502865119, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749616502865836, "dur": 109876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502977692, "dur": 1268, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616502975714, "dur": 3288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749616502979002, "dur": 769, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502980877, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ComponentModel.DataAnnotations.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616502981292, "dur": 1823, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616502983363, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749616502979774, "dur": 3981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749616502983803, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502984554, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749616502984654, "dur": 201278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502840943, "dur": 13746, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502854732, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502855032, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502855446, "dur": 333, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749616502855966, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1749616502856250, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502857069, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502857306, "dur": 3316, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.34\\Rider\\Editor\\ProjectGeneration\\IFileIO.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749616502857212, "dur": 3793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502861006, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502861318, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502861489, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502861987, "dur": 1555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502863542, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502864415, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502864536, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502865106, "dur": 1781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502866888, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749616502866980, "dur": 109916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502976897, "dur": 1459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749616502978357, "dur": 669, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502979033, "dur": 1837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749616502980871, "dur": 2688, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502983609, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502983893, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749616502984869, "dur": 201094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502840973, "dur": 13762, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502854762, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749616502854737, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_B4C1A84D8F19D0AB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749616502855030, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502855396, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749616502855698, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749616502856070, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3043913547040211301.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749616502856233, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502856962, "dur": 823, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Messaging\\MessageEventArgs.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749616502856584, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502858058, "dur": 3524, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.3\\Editor\\SkinningModule\\SpriteMeshData\\EditableBoneWeight.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749616502857824, "dur": 3764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502861588, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502861981, "dur": 1383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502863495, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749616502863365, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749616502864171, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502864553, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502865104, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502866477, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749616502866585, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749616502866874, "dur": 108830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502975708, "dur": 1957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749616502977666, "dur": 1372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502979043, "dur": 1871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749616502980943, "dur": 1708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749616502982651, "dur": 555, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502983350, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502983589, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502983724, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749616502984133, "dur": 201779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502840990, "dur": 13821, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502855031, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749616502855030, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_425C305CB5B8A325.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749616502855121, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502855286, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749616502855490, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749616502855683, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749616502856060, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749616502856125, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502856235, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502856676, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502856895, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502857133, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502857345, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502857572, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502857835, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502858095, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502858455, "dur": 1903, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\Vector3PerSecond.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749616502858333, "dur": 2155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502860489, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502860892, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502861150, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502861663, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502861974, "dur": 1440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502863415, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749616502863894, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502864456, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1749616502864541, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502865097, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502866152, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749616502866260, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749616502866616, "dur": 111424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502978041, "dur": 1224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749616502979311, "dur": 1225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749616502980537, "dur": 1401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749616502983106, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749616502981944, "dur": 2151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749616502984165, "dur": 201793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502841017, "dur": 13802, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502854823, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_7BBC845FD6321414.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749616502854885, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502855298, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749616502855640, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749616502856044, "dur": 322, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749616502856531, "dur": 787, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Identity.Stores.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616502856366, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502857406, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502857543, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502857700, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502857881, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502858035, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502858521, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502859123, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502859416, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502859679, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502859938, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502860225, "dur": 651, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_FontFeatureTable.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749616502860178, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502861214, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502861633, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502861975, "dur": 1387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502863362, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749616502863488, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749616502864019, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616502864276, "dur": 357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616502864916, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\Unity\\RectInspector.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749616502865085, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyOptionProvider.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749616502863798, "dur": 1836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749616502865661, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749616502865733, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749616502866200, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749616502866502, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749616502866704, "dur": 109002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502975709, "dur": 1991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749616502977701, "dur": 642, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502978941, "dur": 2417, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616502978349, "dur": 3911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749616502982260, "dur": 733, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749616502983106, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616502983419, "dur": 422, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616502983857, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616502984557, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749616502982998, "dur": 1788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749616502984842, "dur": 201085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616502841045, "dur": 13996, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616502855054, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616502855295, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749616502855488, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749616502855892, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616502856007, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616502856331, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616502856706, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616502856897, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616502857519, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749616502858462, "dur": 602, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\OrderedTestSuiteModifier.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749616502859380, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRenderer.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749616502859741, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749616502859934, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\ScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749616502855589, "dur": 4974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749616502860564, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616502860688, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749616502861382, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616502860799, "dur": 1145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749616502862026, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749616502862266, "dur": 839, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616502863110, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749616502863399, "dur": 777, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616502864191, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749616502864275, "dur": 358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616502864252, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749616502864904, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616502865034, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616502865292, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749616502865024, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 17, "ts": 1749616502865806, "dur": 101, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616502866139, "dur": 107708, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 17, "ts": 1749616502975694, "dur": 1801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749616502977527, "dur": 1395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749616502978923, "dur": 866, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616502979795, "dur": 1521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749616502981317, "dur": 1600, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749616502982922, "dur": 1370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749616502984334, "dur": 201615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502841074, "dur": 13972, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502855075, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502855396, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1749616502855699, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1749616502856247, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502856563, "dur": 797, "ph": "X", "name": "File", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-environment-l1-1-0.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616502856563, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502857470, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502857605, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502857755, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502858222, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502858520, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502859119, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502859366, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502859621, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502859831, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502860092, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502860618, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502860801, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502860941, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502861101, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502861880, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502861978, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502862231, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749616502862419, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749616502862992, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749616502863083, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502863357, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616502863185, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749616502864088, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502864438, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502864998, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749616502865369, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749616502865748, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502865815, "dur": 111882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502978946, "dur": 2396, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Trigger\\Unity.ILPP.Trigger.exe"}}, {"pid": 12345, "tid": 18, "ts": 1749616502977702, "dur": 4244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749616502981946, "dur": 711, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502983106, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616502983364, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616502983824, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordaccore.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749616502982661, "dur": 1847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749616502984548, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749616502984653, "dur": 201286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502841098, "dur": 13952, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502855052, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502855404, "dur": 584, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1749616502855989, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749616502856249, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502856608, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502856990, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502857209, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502857674, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502858167, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502858310, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502858517, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502859154, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502859384, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502860020, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502860323, "dur": 787, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Merge\\Gluon\\ChangeTreeViewItem.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749616502860297, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502861221, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502861542, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502861983, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502862221, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749616502862288, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502862662, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749616502862950, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749616502863037, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749616502863360, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749616502863489, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502864019, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749616502863698, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749616502864210, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749616502864627, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749616502864338, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749616502864970, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502865603, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749616502865712, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749616502866134, "dur": 115167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502982615, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749616502983101, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749616502983418, "dur": 319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749616502983785, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749616502981302, "dur": 2549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749616502983851, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502984042, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749616502984134, "dur": 201843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502841131, "dur": 13924, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502855068, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616502855055, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749616502855629, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502855739, "dur": 572, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1749616502856312, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502856873, "dur": 921, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\Scopes\\PropertyScope.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749616502856678, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502857833, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502858130, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502858456, "dur": 1910, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\Vector3Absolute.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749616502858385, "dur": 2187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502860572, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502860842, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502861063, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502861122, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\Views\\PlayModeTestListGUI.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749616502861122, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502861826, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502861988, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502862244, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749616502862540, "dur": 1856, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502864396, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749616502864596, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749616502865208, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502865273, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502865715, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749616502865838, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749616502866145, "dur": 111558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749616502977706, "dur": 1175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749616502978946, "dur": 4375, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616502983419, "dur": 321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616502984542, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616502984651, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\unity\\unity editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749616502978922, "dur": 6213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749616502985168, "dur": 200800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749616503190787, "dur": 2186, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26084, "tid": 603, "ts": 1749616503195430, "dur": 17, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26084, "tid": 603, "ts": 1749616503195479, "dur": 5981, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26084, "tid": 603, "ts": 1749616503192540, "dur": 9000, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}