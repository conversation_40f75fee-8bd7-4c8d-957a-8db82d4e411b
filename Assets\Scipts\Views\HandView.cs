using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

public class HandView : MonoBehaviour
{
    [Header("手牌设置")]
    [SerializeField] private float cardWidth = 2f;              // 卡牌宽度
    [SerializeField] private float normalSpacing = 2.2f;        // 正常间距
    [SerializeField] private float stackSpacing = 0.3f;         // 堆叠间距（最小间距）
    [SerializeField] private float handY = -4f;                 // 手牌Y坐标（界面底部）
    [SerializeField] private int maxFullCards = 5;              // 最多完整显示的卡牌数量
    [SerializeField] private float maxHandWidth = 15f;          // 手牌区域最大宽度
    [SerializeField] private Vector3 startPosition = new Vector3(-7f, -4f, 0f); // 起始位置（最左边）
    [SerializeField] private Vector3 handCardScale = Vector3.one; // 手牌中卡牌的缩放大小

    [Header("动画设置")]
    [SerializeField] private float animationDuration = 0.3f;    // 动画持续时间

    private List<CardView> cards = new List<CardView>();

    public IEnumerator AddCard(CardView CardView)
    {
        // 将新卡牌添加到列表末尾（从左往右发牌）
        cards.Add(CardView);
        yield return UpdateCardPositions();
    }

    public IEnumerator RemoveCard(CardView CardView)
    {
        if (cards.Contains(CardView))
        {
            cards.Remove(CardView);
            yield return UpdateCardPositions();
        }
    }

    private IEnumerator UpdateCardPositions()
    {
        if (cards.Count == 0) yield break;

        // 预先计算所有卡牌的位置
        Vector3[] positions = CalculateAllCardPositions();

        // 更新每张卡牌的位置
        for (int i = 0; i < cards.Count; i++)
        {
            // 设置卡牌旋转（保持水平）
            Quaternion targetRotation = Quaternion.identity;

            // 先停止当前卡牌的所有动画，避免冲突
            cards[i].transform.DOKill();

            // 使用DOTween创建平滑动画，并设置为可回收
            cards[i].transform.DOMove(positions[i], animationDuration)
                .SetEase(Ease.OutQuart)
                .SetRecyclable(true);

            cards[i].transform.DORotateQuaternion(targetRotation, animationDuration)
                .SetEase(Ease.OutQuart)
                .SetRecyclable(true);

            cards[i].transform.DOScale(handCardScale, animationDuration)
                .SetEase(Ease.OutQuart)
                .SetRecyclable(true);

            // 设置卡牌的渲染顺序（前面的卡牌在上层，覆盖后面的卡牌）
            if (cards[i].TryGetComponent<SpriteRenderer>(out SpriteRenderer sr))
            {
                sr.sortingOrder = i; // 索引越大，渲染层级越高
            }

            // 如果卡牌有Canvas组件，也设置其排序
            if (cards[i].TryGetComponent<Canvas>(out Canvas canvas))
            {
                canvas.sortingOrder = i; // 索引越大，渲染层级越高
            }

            // 更新悬停效果的原始状态
            if (cards[i].TryGetComponent<CardHoverEffect>(out CardHoverEffect hoverEffect))
            {
                hoverEffect.RecordOriginalState();
            }
        }

        yield return new WaitForSeconds(animationDuration);
    }

    /// <summary>
    /// 计算所有卡牌的位置，确保不超出边界
    /// </summary>
    private Vector3[] CalculateAllCardPositions()
    {
        Vector3[] positions = new Vector3[cards.Count];

        if (cards.Count == 0) return positions;

        // 计算实际的完整显示卡牌数量
        int actualFullCards = CalculateActualFullCards();

        float currentX = startPosition.x;

        // 完整显示的卡牌
        for (int i = 0; i < actualFullCards; i++)
        {
            positions[i] = new Vector3(
                currentX,
                handY,
                startPosition.z + i * 0.01f
            );
            currentX += normalSpacing;
        }

        // 堆叠的卡牌
        if (cards.Count > actualFullCards)
        {
            // 第一张堆叠卡牌：与最后一张完整显示卡牌重叠2/3
            float firstStackX = startPosition.x + (actualFullCards - 1) * normalSpacing + cardWidth / 3f;

            for (int i = actualFullCards; i < cards.Count; i++)
            {
                int stackIndex = i - actualFullCards;
                positions[i] = new Vector3(
                    firstStackX + stackIndex * stackSpacing,
                    handY,
                    startPosition.z + i * 0.01f
                );
            }
        }

        return positions;
    }

    /// <summary>
    /// 计算实际的完整显示卡牌数量，确保不超出边界
    /// </summary>
    private int CalculateActualFullCards()
    {
        if (cards.Count <= maxFullCards)
        {
            return cards.Count; // 所有卡牌都完整显示
        }

        // 从最大数量开始，逐步减少，直到能放下所有卡牌
        for (int fullCards = maxFullCards; fullCards >= 1; fullCards--)
        {
            // 计算完整显示区域宽度
            float fullDisplayWidth = (fullCards - 1) * normalSpacing;

            // 计算堆叠区域宽度
            int stackedCards = cards.Count - fullCards;
            float stackWidth = cardWidth / 3f + stackedCards * stackSpacing;

            // 计算总宽度
            float totalWidth = fullDisplayWidth + stackWidth;

            if (totalWidth <= maxHandWidth)
            {
                return fullCards;
            }
        }

        return 1; // 最少保证1张完整显示
    }



    // 获取手牌数量
    public int GetCardCount()
    {
        return cards.Count;
    }

    // 获取指定索引的卡牌
    public CardView GetCard(int index)
    {
        if (index >= 0 && index < cards.Count)
            return cards[index];
        return null;
    }

    // 清空所有手牌
    public void ClearHand()
    {
        cards.Clear();
    }

    // 在编辑器中预览手牌位置
    private void OnDrawGizmos()
    {
        if (cards == null) return;

        Gizmos.color = Color.yellow;

        // 绘制起始位置
        Gizmos.DrawWireCube(startPosition, new Vector3(cardWidth, 1f, 0.1f));

        // 绘制完整显示区域
        float fullDisplayWidth = (maxFullCards - 1) * normalSpacing + cardWidth;
        Vector3 fullAreaCenter = new Vector3(startPosition.x + fullDisplayWidth / 2, handY, startPosition.z);
        Gizmos.color = Color.green;
        Gizmos.DrawWireCube(fullAreaCenter, new Vector3(fullDisplayWidth, 1f, 0.1f));

        // 绘制最大手牌区域边界
        Vector3 maxAreaCenter = new Vector3(startPosition.x + maxHandWidth / 2, handY, startPosition.z);
        Gizmos.color = Color.red;
        Gizmos.DrawWireCube(maxAreaCenter, new Vector3(maxHandWidth, 1.2f, 0.1f));
    }
}
