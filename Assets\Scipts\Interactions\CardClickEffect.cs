using UnityEngine;
using DG.Tweening;

// 卡牌点击效果组件（支持配置）
public class CardClickEffect : MonoBehaviour
{
    // 配置参数
    private Color clickGlowColor = Color.yellow;
    private float clickGlowDuration = 0.5f;
    private float glowScale = 1.1f;
    
    // 组件引用
    private SpriteRenderer spriteRenderer;
    private GameObject glowEffect;
    private SpriteRenderer glowRenderer;
    
    private void Start()
    {
        // 尝试获取SpriteRenderer组件
        spriteRenderer = GetComponent<SpriteRenderer>();
        if (spriteRenderer == null)
        {
            // 如果当前对象没有，尝试从子对象中查找
            spriteRenderer = GetComponentInChildren<SpriteRenderer>();
        }

        if (spriteRenderer == null)
        {
            Debug.LogWarning($"CardClickEffect: 在对象 {gameObject.name} 中未找到SpriteRenderer组件");
            return; // 如果没有SpriteRenderer，不创建发光效果
        }

        CreateGlowEffect();
    }
    
    // 应用全局设置
    public void ApplySettings(CardInteractionSettings settings)
    {
        clickGlowColor = settings.clickGlowColor;
        clickGlowDuration = settings.clickGlowDuration;
        glowScale = settings.glowScale;
        
        // 如果发光效果已创建，更新颜色和缩放
        if (glowRenderer != null)
        {
            glowRenderer.color = clickGlowColor;
            glowEffect.transform.localScale = Vector3.one * glowScale;
        }
    }
    
    // 创建发光效果
    private void CreateGlowEffect()
    {
        if (spriteRenderer == null)
        {
            Debug.LogWarning("CardClickEffect: 无法创建发光效果，SpriteRenderer为空");
            return;
        }

        // 创建发光边框对象
        glowEffect = new GameObject("ClickGlow");
        glowEffect.transform.SetParent(transform);
        glowEffect.transform.localPosition = Vector3.zero;
        glowEffect.transform.localScale = Vector3.one * glowScale;

        // 添加SpriteRenderer组件
        glowRenderer = glowEffect.AddComponent<SpriteRenderer>();
        glowRenderer.sprite = spriteRenderer.sprite;
        glowRenderer.color = clickGlowColor;
        glowRenderer.sortingOrder = spriteRenderer.sortingOrder - 1; // 在卡牌后面

        // 初始时隐藏
        glowEffect.SetActive(false);
    }
    
    // 鼠标点击
    private void OnMouseDown()
    {
        TriggerClickEffect();
    }
    
    // 触发点击效果
    public void TriggerClickEffect()
    {
        // 检查发光效果是否已创建
        if (glowEffect == null || glowRenderer == null)
        {
            Debug.LogWarning("CardClickEffect: 发光效果未创建，无法触发点击效果");
            return;
        }

        // 停止之前的动画
        glowRenderer.DOKill();

        // 显示发光效果
        glowEffect.SetActive(true);

        // 重置颜色和透明度
        glowRenderer.color = new Color(clickGlowColor.r, clickGlowColor.g, clickGlowColor.b, 1f);

        // 淡出动画
        glowRenderer.DOFade(0f, clickGlowDuration).SetEase(Ease.OutQuart).OnComplete(() => {
            if (glowEffect != null) // 再次检查，防止在动画期间被销毁
            {
                glowEffect.SetActive(false);
            }
        });
    }
}
