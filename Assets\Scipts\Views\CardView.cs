·using TMPro;
using UnityEngine;

// 卡牌视图类，继承自MonoBehaviour
public class CardView : MonoBehaviour
{
    // 标题文本组件
    [SerializeField] private TMP_Text title;

    // 描述文本组件
    [SerializeField] private TMP_Text description;

    // 法力值文本组件
    [SerializeField] private TMP_Text mana;

    // 图像精灵渲染器组件
    [SerializeField] private SpriteRenderer imageSR;

    // 包装器游戏对象
    [SerializeField] private GameObject wrapper;

    // 卡牌数据
    public Card card { get; private set; }

    // 设置卡牌数据的方法
    public void SetupCard(Card card)
    {
        this.card = card;
        title.text = card.Title;
        description.text = card.Description;
        mana.text = card.Mana.ToString();
        imageSR.sprite = card.Image;
    }
}
