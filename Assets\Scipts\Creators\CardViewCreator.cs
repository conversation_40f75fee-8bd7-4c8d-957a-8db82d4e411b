using DG.Tweening;
using UnityEngine;

// 卡牌视图创建器类，继承自单例模式
public class CardViewCreator : Singleton<CardViewCreator>
{
    // 卡牌视图预制体
    [SerializeField] private CardView cardViewPrefab;

    // 创建卡牌视图的方法
    public CardView CreateCardView(Card card, Vector3 position, Quaternion rotation)
    {
        // 实例化卡牌视图预制体
        CardView cardView = Instantiate(cardViewPrefab, position, rotation);

        // 设置初始缩放为零
        cardView.transform.localScale = Vector3.zero;

        // 先设置卡牌数据
        cardView.SetupCard(card);

        // 然后添加交互组件（确保卡牌数据已设置）
        if (CardInteractionManager.Instance != null)
        {
            CardInteractionManager.Instance.SetupCardInteraction(cardView.gameObject);
        }

        // 使用DOTween进行缩放动画，设置为可回收
        cardView.transform.DOScale(Vector3.one, 0.15f)
            .SetRecyclable(true)
            .SetEase(Ease.OutBack);

        // 返回创建的卡牌视图
        return cardView;
    }
}