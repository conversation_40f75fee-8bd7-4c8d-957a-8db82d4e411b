using UnityEngine;
using DG.Tweening;

/// <summary>
/// DOTween初始化器 - 在游戏启动时配置DOTween设置
/// 解决频繁创建动画时容量不足的警告问题
/// </summary>
public class DOTweenInitializer : MonoBehaviour
{
    [Header("DOTween容量设置")]
    [SerializeField] private int tweenersCapacity = 1000;    // Tweener容量
    [SerializeField] private int sequencesCapacity = 100;    // Sequence容量
    
    [Header("DOTween基本设置")]
    [SerializeField] private bool recycleAllByDefault = true; // 默认回收所有动画
    [SerializeField] private bool useSafeMode = false;        // 安全模式
    [SerializeField] private LogBehaviour logBehaviour = LogBehaviour.ErrorsOnly; // 日志行为
    
    /// <summary>
    /// 在场景加载前初始化DOTween
    /// </summary>
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    private static void InitializeDOTween()
    {
        // 查找是否有DOTweenInitializer实例
        DOTweenInitializer initializer = FindObjectOfType<DOTweenInitializer>();
        
        if (initializer != null)
        {
            // 使用实例的设置
            initializer.SetupDOTween();
        }
        else
        {
            // 使用默认设置
            SetupDefaultDOTween();
        }
    }
    
    /// <summary>
    /// 使用当前实例的设置配置DOTween
    /// </summary>
    private void SetupDOTween()
    {
        // 初始化DOTween并设置容量
        DOTween.Init(recycleAllByDefault, useSafeMode, logBehaviour)
               .SetCapacity(tweenersCapacity, sequencesCapacity);
        
        Debug.Log($"DOTween initialized with custom settings - Tweeners: {tweenersCapacity}, Sequences: {sequencesCapacity}");
    }
    
    /// <summary>
    /// 使用默认设置配置DOTween
    /// </summary>
    private static void SetupDefaultDOTween()
    {
        // 使用默认设置初始化DOTween
        DOTween.Init(true, false, LogBehaviour.ErrorsOnly)
               .SetCapacity(1000, 100);
        
        Debug.Log("DOTween initialized with default settings - Tweeners: 1000, Sequences: 100");
    }
    
    /// <summary>
    /// 在编辑器中显示当前DOTween状态信息
    /// </summary>
    private void OnGUI()
    {
        if (Application.isEditor && Debug.isDebugBuild)
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 100));
            GUILayout.Label($"DOTween Status:");
            GUILayout.Label($"Active Tweens: {DOTween.TotalActiveTweens()}");
            GUILayout.Label($"Playing Tweens: {DOTween.TotalPlayingTweens()}");
            GUILayout.EndArea();
        }
    }
}
