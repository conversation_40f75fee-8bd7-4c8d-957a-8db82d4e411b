using UnityEngine;

// 卡牌交互全局管理器
public class CardInteractionManager : MonoBehaviour
{
    // 单例实例
    public static CardInteractionManager Instance { get; private set; }
    
    [Header("全局交互配置")]
    [SerializeField] private CardInteractionSettings globalSettings;
    
    private void Awake()
    {
        // 单例模式实现
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject); // 跨场景保持
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    // 为卡牌对象设置交互组件
    public void SetupCardInteraction(GameObject cardObject)
    {
        if (cardObject == null) return;
        
        // 添加悬停效果
        if (globalSettings.enableHover)
        {
            AddHoverEffect(cardObject);
        }
        
        // 添加点击效果
        if (globalSettings.enableClick)
        {
            AddClickEffect(cardObject);
        }
        
        // 添加描述提示
        if (globalSettings.enableTooltip)
        {
            AddTooltipEffect(cardObject);
        }
    }
    
    // 添加悬停效果组件
    private void AddHoverEffect(GameObject cardObject)
    {
        CardHoverEffect hoverEffect = cardObject.GetComponent<CardHoverEffect>();
        if (hoverEffect == null)
        {
            hoverEffect = cardObject.AddComponent<CardHoverEffect>();
        }
        
        // 应用全局设置
        hoverEffect.ApplySettings(globalSettings);
    }
    
    // 添加点击效果组件
    private void AddClickEffect(GameObject cardObject)
    {
        CardClickEffect clickEffect = cardObject.GetComponent<CardClickEffect>();
        if (clickEffect == null)
        {
            clickEffect = cardObject.AddComponent<CardClickEffect>();
        }
        
        // 应用全局设置
        clickEffect.ApplySettings(globalSettings);
    }
    
    // 添加描述提示组件
    private void AddTooltipEffect(GameObject cardObject)
    {
        CardDescriptionTooltip tooltip = cardObject.GetComponent<CardDescriptionTooltip>();
        if (tooltip == null)
        {
            tooltip = cardObject.AddComponent<CardDescriptionTooltip>();
        }
        
        // 应用全局设置
        tooltip.ApplySettings(globalSettings);
    }
    
    // 获取全局设置
    public CardInteractionSettings GetGlobalSettings()
    {
        return globalSettings;
    }
    
    // 更新全局设置
    public void UpdateGlobalSettings(CardInteractionSettings newSettings)
    {
        globalSettings = newSettings;
    }
    
    // 移除卡牌的所有交互组件
    public void RemoveCardInteraction(GameObject cardObject)
    {
        if (cardObject == null) return;
        
        // 移除各种交互组件
        if (cardObject.TryGetComponent<CardHoverEffect>(out CardHoverEffect hoverEffect))
        {
            Destroy(hoverEffect);
        }
        
        if (cardObject.TryGetComponent<CardClickEffect>(out CardClickEffect clickEffect))
        {
            Destroy(clickEffect);
        }
        
        if (cardObject.TryGetComponent<CardDescriptionTooltip>(out CardDescriptionTooltip tooltip))
        {
            Destroy(tooltip);
        }
    }
}
