using UnityEngine;
using DG.Tweening;
using TMPro;

// 卡牌描述提示组件（支持配置）
public class CardDescriptionTooltip : MonoBehaviour
{
    // 配置参数
    private Vector3 tooltipOffset = new Vector3(0, 1.5f, 0);
    private float fadeInDuration = 0.2f;
    private float fadeOutDuration = 0.15f;
    private Color backgroundColor = new Color(0, 0, 0, 0.8f);
    private Color textColor = Color.white;
    private float fontSize = 0.5f;
    
    // 组件引用
    private CardView cardView;
    private GameObject tooltipPanel;
    private TextMeshPro tooltipText;
    private SpriteRenderer backgroundRenderer;
    private CanvasGroup canvasGroup;
    
    private void Start()
    {
        cardView = GetComponent<CardView>();
        CreateTooltipPanel();
    }
    
    // 应用全局设置
    public void ApplySettings(CardInteractionSettings settings)
    {
        tooltipOffset = settings.tooltipOffset;
        fadeInDuration = settings.tooltipFadeInDuration;
        fadeOutDuration = settings.tooltipFadeOutDuration;
        backgroundColor = settings.tooltipBackgroundColor;
        textColor = settings.tooltipTextColor;
        fontSize = settings.tooltipFontSize;
        
        // 如果组件已创建，更新设置
        if (tooltipPanel != null)
        {
            tooltipPanel.transform.localPosition = tooltipOffset;
        }
        
        if (tooltipText != null)
        {
            tooltipText.color = textColor;
            tooltipText.fontSize = fontSize;
        }
        
        if (backgroundRenderer != null)
        {
            backgroundRenderer.color = backgroundColor;
        }
    }
    
    // 创建提示框面板
    private void CreateTooltipPanel()
    {
        // 创建主面板
        tooltipPanel = new GameObject("TooltipPanel");
        tooltipPanel.transform.SetParent(transform);
        tooltipPanel.transform.localPosition = tooltipOffset;
        
        // 添加CanvasGroup用于淡入淡出
        canvasGroup = tooltipPanel.AddComponent<CanvasGroup>();
        canvasGroup.alpha = 0f;
        
        // 创建背景
        GameObject background = new GameObject("Background");
        background.transform.SetParent(tooltipPanel.transform);
        background.transform.localPosition = Vector3.zero;
        
        backgroundRenderer = background.AddComponent<SpriteRenderer>();
        backgroundRenderer.color = backgroundColor;
        backgroundRenderer.sortingOrder = GetComponent<SpriteRenderer>().sortingOrder + 15;
        
        // 创建文本
        GameObject textObject = new GameObject("TooltipText");
        textObject.transform.SetParent(tooltipPanel.transform);
        textObject.transform.localPosition = Vector3.zero;
        
        tooltipText = textObject.AddComponent<TextMeshPro>();
        tooltipText.fontSize = fontSize;
        tooltipText.color = textColor;
        tooltipText.alignment = TextAlignmentOptions.Center;
        tooltipText.sortingOrder = backgroundRenderer.sortingOrder + 1;
        
        // 初始时隐藏
        tooltipPanel.SetActive(false);
    }
    
    // 鼠标进入
    private void OnMouseEnter()
    {
        ShowTooltip();
    }
    
    // 鼠标离开
    private void OnMouseExit()
    {
        HideTooltip();
    }
    
    // 显示提示框
    private void ShowTooltip()
    {
        if (cardView != null && cardView.card != null)
        {
            // 更新文本内容
            string tooltipContent = $"{cardView.card.Title}\n法力: {cardView.card.Mana}\n{cardView.card.Description}";
            tooltipText.text = tooltipContent;
            
            // 调整背景大小适应文本
            AdjustBackgroundSize();
        }
        
        // 显示面板
        tooltipPanel.SetActive(true);
        
        // 淡入动画
        canvasGroup.DOKill();
        canvasGroup.DOFade(1f, fadeInDuration).SetEase(Ease.OutQuart);
    }
    
    // 隐藏提示框
    private void HideTooltip()
    {
        if (tooltipPanel.activeInHierarchy)
        {
            // 淡出动画
            canvasGroup.DOKill();
            canvasGroup.DOFade(0f, fadeOutDuration).SetEase(Ease.OutQuart).OnComplete(() => {
                tooltipPanel.SetActive(false);
            });
        }
    }
    
    // 调整背景大小
    private void AdjustBackgroundSize()
    {
        if (tooltipText != null && backgroundRenderer != null)
        {
            // 获取文本边界
            Bounds textBounds = tooltipText.bounds;
            
            // 创建适合的背景精灵（简单的白色方块）
            Texture2D bgTexture = new Texture2D(1, 1);
            bgTexture.SetPixel(0, 0, Color.white);
            bgTexture.Apply();
            
            Sprite bgSprite = Sprite.Create(bgTexture, new Rect(0, 0, 1, 1), new Vector2(0.5f, 0.5f));
            backgroundRenderer.sprite = bgSprite;
            
            // 调整背景大小
            float padding = 0.2f;
            backgroundRenderer.transform.localScale = new Vector3(
                textBounds.size.x + padding,
                textBounds.size.y + padding,
                1f
            );
        }
    }
}
