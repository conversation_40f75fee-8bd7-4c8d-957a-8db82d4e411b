using UnityEngine;
using DG.Tweening;
using TMPro;

// 卡牌描述提示组件（支持配置）
public class CardDescriptionTooltip : MonoBehaviour
{
    // 配置参数
    private Vector3 tooltipOffset = new Vector3(0, 1.5f, 0);
    private float fadeInDuration = 0.2f;
    private float fadeOutDuration = 0.15f;
    private Color backgroundColor = new Color(0, 0, 0, 0.8f);
    private Color textColor = Color.white;
    private float fontSize = 0.5f;
    
    // 组件引用
    private CardView cardView;
    private GameObject tooltipPanel;
    private TextMeshPro tooltipText;
    private SpriteRenderer backgroundRenderer;
    private CanvasGroup canvasGroup;
    
    private void Awake()
    {
        // 在Awake中初始化，确保在其他方法调用前完成
        cardView = GetComponent<CardView>();
        InitializeTooltip();
    }

    private void Start()
    {
        Debug.Log($"CardDescriptionTooltip: Start() 在对象 {gameObject.name} 上被调用");
        // 确保初始化完成
        if (tooltipPanel == null)
        {
            InitializeTooltip();
        }
    }

    // 确保所有组件都正确初始化
    private void EnsureInitialized()
    {
        if (cardView == null)
        {
            cardView = GetComponent<CardView>();
        }

        if (tooltipPanel == null)
        {
            InitializeTooltip();
        }
    }

    // 初始化提示框（重命名并添加错误处理）
    private void InitializeTooltip()
    {
        try
        {
            CreateTooltipPanel();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"CardDescriptionTooltip: 初始化提示框失败: {e.Message}");
        }
    }
    
    // 应用全局设置
    public void ApplySettings(CardInteractionSettings settings)
    {
        tooltipOffset = settings.tooltipOffset;
        fadeInDuration = settings.tooltipFadeInDuration;
        fadeOutDuration = settings.tooltipFadeOutDuration;
        backgroundColor = settings.tooltipBackgroundColor;
        textColor = settings.tooltipTextColor;
        fontSize = settings.tooltipFontSize;
        
        // 如果组件已创建，更新设置
        if (tooltipPanel != null)
        {
            tooltipPanel.transform.localPosition = tooltipOffset;
        }
        
        if (tooltipText != null)
        {
            tooltipText.color = textColor;
            tooltipText.fontSize = fontSize;
        }
        
        if (backgroundRenderer != null)
        {
            backgroundRenderer.color = backgroundColor;
        }
    }
    
    // 创建提示框面板
    private void CreateTooltipPanel()
    {
        // 如果已经存在，先清理
        if (tooltipPanel != null)
        {
            DestroyImmediate(tooltipPanel);
        }

        Debug.Log("CardDescriptionTooltip: 开始创建提示框面板");

        // 创建主面板
        tooltipPanel = new GameObject("TooltipPanel");
        tooltipPanel.transform.SetParent(transform);
        tooltipPanel.transform.localPosition = tooltipOffset;

        // 添加CanvasGroup用于淡入淡出
        canvasGroup = tooltipPanel.AddComponent<CanvasGroup>();
        canvasGroup.alpha = 0f;

        // 创建背景
        GameObject background = new GameObject("Background");
        background.transform.SetParent(tooltipPanel.transform);
        background.transform.localPosition = Vector3.zero;

        backgroundRenderer = background.AddComponent<SpriteRenderer>();
        backgroundRenderer.color = backgroundColor;

        // 安全获取主SpriteRenderer（优先从CardView获取）
        SpriteRenderer mainRenderer = null;

        // 如果有CardView组件，尝试获取其imageSR
        if (cardView != null)
        {
            // 通过反射获取imageSR字段
            var field = cardView.GetType().GetField("imageSR", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                mainRenderer = field.GetValue(cardView) as SpriteRenderer;
            }
        }

        // 如果还是没找到，尝试常规方法
        if (mainRenderer == null)
        {
            mainRenderer = GetComponent<SpriteRenderer>();
            if (mainRenderer == null)
            {
                mainRenderer = GetComponentInChildren<SpriteRenderer>();
            }
        }

        if (mainRenderer != null)
        {
            backgroundRenderer.sortingOrder = mainRenderer.sortingOrder + 15;
        }
        else
        {
            backgroundRenderer.sortingOrder = 15;
            Debug.LogWarning($"CardDescriptionTooltip: 在对象 {gameObject.name} 中未找到SpriteRenderer，使用默认排序");
        }

        // 创建文本
        GameObject textObject = new GameObject("TooltipText");
        textObject.transform.SetParent(tooltipPanel.transform);
        textObject.transform.localPosition = Vector3.zero;

        tooltipText = textObject.AddComponent<TextMeshPro>();
        tooltipText.fontSize = fontSize;
        tooltipText.color = textColor;
        tooltipText.alignment = TextAlignmentOptions.Center;
        tooltipText.sortingOrder = backgroundRenderer.sortingOrder + 1;

        // 初始时隐藏
        tooltipPanel.SetActive(false);

        Debug.Log("CardDescriptionTooltip: 提示框面板创建完成");
    }
    
    // 鼠标悬停进入
    private void OnMouseEnter()
    {
        // 确保所有组件都已初始化
        EnsureInitialized();
        ShowTooltip();
    }

    // 鼠标悬停离开
    private void OnMouseExit()
    {
        HideTooltip();
    }

    // 鼠标点击（额外的触发方式）
    private void OnMouseDown()
    {
        Debug.Log($"CardDescriptionTooltip: OnMouseDown() 在对象 {gameObject.name} 上被调用");

        // 确保所有组件都已初始化
        EnsureInitialized();

        // 如果提示框已经显示，则隐藏；否则显示
        if (tooltipPanel != null && tooltipPanel.activeInHierarchy)
        {
            Debug.Log("CardDescriptionTooltip: 隐藏提示框");
            HideTooltip();
        }
        else
        {
            Debug.Log("CardDescriptionTooltip: 显示提示框");
            ShowTooltip();
        }
    }
    
    // 显示提示框
    private void ShowTooltip()
    {
        // 检查必要的组件是否存在
        if (tooltipPanel == null || canvasGroup == null || tooltipText == null)
        {
            Debug.LogWarning("CardDescriptionTooltip: 提示框组件未正确初始化");
            return;
        }

        // 检查卡牌数据是否存在
        if (cardView != null && cardView.card != null)
        {
            // 更新文本内容
            string tooltipContent = $"{cardView.card.Title}\n法力: {cardView.card.Mana}\n{cardView.card.Description}";
            tooltipText.text = tooltipContent;

            // 调整背景大小适应文本
            AdjustBackgroundSize();
        }
        else
        {
            // 如果没有卡牌数据，显示默认文本
            tooltipText.text = "卡牌信息加载中...";
            Debug.LogWarning("CardDescriptionTooltip: CardView或Card数据为空");
        }

        // 显示面板
        tooltipPanel.SetActive(true);

        // 淡入动画
        canvasGroup.DOKill();
        canvasGroup.DOFade(1f, fadeInDuration).SetEase(Ease.OutQuart);
    }
    
    // 隐藏提示框
    private void HideTooltip()
    {
        // 检查组件是否存在
        if (tooltipPanel != null && canvasGroup != null && tooltipPanel.activeInHierarchy)
        {
            // 淡出动画
            canvasGroup.DOKill();
            canvasGroup.DOFade(0f, fadeOutDuration).SetEase(Ease.OutQuart).OnComplete(() => {
                if (tooltipPanel != null) // 再次检查，防止在动画期间被销毁
                {
                    tooltipPanel.SetActive(false);
                }
            });
        }
    }
    
    // 调整背景大小
    private void AdjustBackgroundSize()
    {
        if (tooltipText != null && backgroundRenderer != null)
        {
            try
            {
                // 获取文本边界
                Bounds textBounds = tooltipText.bounds;

                // 检查文本边界是否有效
                if (textBounds.size.x <= 0 || textBounds.size.y <= 0)
                {
                    // 使用默认大小
                    backgroundRenderer.transform.localScale = new Vector3(2f, 1f, 1f);
                    return;
                }

                // 创建适合的背景精灵（简单的白色方块）
                Texture2D bgTexture = new Texture2D(1, 1);
                bgTexture.SetPixel(0, 0, Color.white);
                bgTexture.Apply();

                Sprite bgSprite = Sprite.Create(bgTexture, new Rect(0, 0, 1, 1), new Vector2(0.5f, 0.5f));
                backgroundRenderer.sprite = bgSprite;

                // 调整背景大小
                float padding = 0.2f;
                backgroundRenderer.transform.localScale = new Vector3(
                    textBounds.size.x + padding,
                    textBounds.size.y + padding,
                    1f
                );
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"CardDescriptionTooltip: 调整背景大小时出错: {e.Message}");
                // 使用默认大小
                backgroundRenderer.transform.localScale = new Vector3(2f, 1f, 1f);
            }
        }
    }
}
