using UnityEngine;
using DG.Tweening;
using TMPro;
using System.Collections.Generic;

// 卡牌悬停效果组件（支持配置）
public class CardHoverEffect : MonoBehaviour
{
    // 配置参数
    private float hoverYOffset = 0.3f;
    private float animationDuration = 0.2f;
    private int hoverSortingOrderBoost = 10;

    // 组件引用
    private SpriteRenderer spriteRenderer;
    private List<Renderer> allRenderers = new List<Renderer>();
    private List<int> originalSortingOrders = new List<int>();

    // 状态记录
    private Vector3 originalPosition;
    private bool isHovering = false;
    
    private void Start()
    {
        // 尝试获取SpriteRenderer组件
        spriteRenderer = GetComponent<SpriteRenderer>();
        if (spriteRenderer == null)
        {
            // 如果当前对象没有，尝试从子对象中查找
            spriteRenderer = GetComponentInChildren<SpriteRenderer>();
        }

        if (spriteRenderer == null)
        {
            Debug.LogWarning($"CardHoverEffect: 在对象 {gameObject.name} 中未找到SpriteRenderer组件");
        }

        RecordOriginalState();
    }
    
    // 应用全局设置
    public void ApplySettings(CardInteractionSettings settings)
    {
        hoverYOffset = settings.hoverYOffset;
        animationDuration = settings.hoverAnimationDuration;
        hoverSortingOrderBoost = settings.hoverSortingOrderBoost;
    }
    
    // 记录原始状态
    public void RecordOriginalState()
    {
        // 停止之前的记录协程
        StopAllCoroutines();
        // 延迟记录状态
        StartCoroutine(DelayedRecordState());
    }

    // 延迟记录状态协程
    private System.Collections.IEnumerator DelayedRecordState()
    {
        // 等待动画完成
        yield return new WaitForSeconds(0.3f);

        originalPosition = transform.position;

        // 收集所有渲染组件
        CollectAllRenderers();
    }

    // 收集所有渲染组件
    private void CollectAllRenderers()
    {
        allRenderers.Clear();
        originalSortingOrders.Clear();

        // 获取所有SpriteRenderer组件（包括子对象）
        SpriteRenderer[] spriteRenderers = GetComponentsInChildren<SpriteRenderer>();
        foreach (var sr in spriteRenderers)
        {
            allRenderers.Add(sr);
            originalSortingOrders.Add(sr.sortingOrder);
        }

        // 获取所有TextMeshPro组件（包括子对象）
        TextMeshPro[] textMeshPros = GetComponentsInChildren<TextMeshPro>();
        foreach (var tmp in textMeshPros)
        {
            allRenderers.Add(tmp.renderer);
            originalSortingOrders.Add(tmp.sortingOrder);
        }

        Debug.Log($"CardHoverEffect: 收集到 {allRenderers.Count} 个渲染组件");
    }
    
    // 鼠标进入
    private void OnMouseEnter()
    {
        if (!isHovering)
        {
            // 确保有有效的原始位置
            if (Vector3.Distance(originalPosition, Vector3.zero) < 0.1f)
            {
                originalPosition = transform.position;
            }

            isHovering = true;
            ApplyHoverEffect();
        }
    }

    // 鼠标离开
    private void OnMouseExit()
    {
        if (isHovering)
        {
            isHovering = false;
            RemoveHoverEffect();
        }
    }
    
    // 应用悬停效果
    private void ApplyHoverEffect()
    {
        // 验证原始位置是否合理
        if (Vector3.Distance(originalPosition, Vector3.zero) < 0.1f)
        {
            // 如果原始位置不合理，重新记录
            originalPosition = transform.position;
        }

        // 停止之前的动画
        transform.DOKill();

        // 提高所有渲染组件的层级
        for (int i = 0; i < allRenderers.Count; i++)
        {
            if (allRenderers[i] != null)
            {
                if (allRenderers[i] is SpriteRenderer sr)
                {
                    sr.sortingOrder = originalSortingOrders[i] + hoverSortingOrderBoost;
                }
                else if (allRenderers[i].GetComponent<TextMeshPro>() != null)
                {
                    var tmp = allRenderers[i].GetComponent<TextMeshPro>();
                    tmp.sortingOrder = originalSortingOrders[i] + hoverSortingOrderBoost;
                }
            }
        }

        // 上移动画（不放大）
        transform.DOMoveY(originalPosition.y + hoverYOffset, animationDuration).SetEase(Ease.OutQuart);
    }
    
    // 移除悬停效果
    private void RemoveHoverEffect()
    {
        // 验证原始位置是否合理
        if (Vector3.Distance(originalPosition, Vector3.zero) < 0.1f)
        {
            // 如果原始位置不合理，重新记录
            originalPosition = transform.position;
        }

        // 停止之前的动画
        transform.DOKill();

        // 恢复所有渲染组件的层级
        for (int i = 0; i < allRenderers.Count; i++)
        {
            if (allRenderers[i] != null && i < originalSortingOrders.Count)
            {
                if (allRenderers[i] is SpriteRenderer sr)
                {
                    sr.sortingOrder = originalSortingOrders[i];
                }
                else if (allRenderers[i].GetComponent<TextMeshPro>() != null)
                {
                    var tmp = allRenderers[i].GetComponent<TextMeshPro>();
                    tmp.sortingOrder = originalSortingOrders[i];
                }
            }
        }

        // 回到原位
        transform.DOMoveY(originalPosition.y, animationDuration).SetEase(Ease.OutQuart);
    }
    
    // 获取悬停状态
    public bool IsHovering()
    {
        return isHovering;
    }
}
