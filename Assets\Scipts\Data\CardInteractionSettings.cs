using UnityEngine;

// 卡牌交互配置数据
[System.Serializable]
public class CardInteractionSettings
{
    [Header("悬停效果设置")]
    public bool enableHover = true;                          // 启用悬停效果
    public float hoverYOffset = 0.3f;                        // 悬停上移距离
    public float hoverAnimationDuration = 0.2f;              // 悬停动画时长
    public int hoverSortingOrderBoost = 10;                  // 悬停层级提升
    
    [Header("点击效果设置")]
    public bool enableClick = true;                          // 启用点击效果
    public Color clickGlowColor = Color.yellow;              // 点击发光颜色
    public float clickGlowDuration = 0.5f;                   // 点击发光持续时间
    public float glowScale = 1.1f;                           // 发光边框缩放
    
    [Header("描述提示设置")]
    public bool enableTooltip = true;                        // 启用描述提示
    public Vector3 tooltipOffset = new Vector3(0, 1.5f, 0);  // 提示框偏移
    public float tooltipFadeInDuration = 0.2f;               // 提示框淡入时长
    public float tooltipFadeOutDuration = 0.15f;             // 提示框淡出时长
    public Color tooltipBackgroundColor = new Color(0, 0, 0, 0.8f); // 提示框背景色
    public Color tooltipTextColor = Color.white;             // 提示框文字颜色
    public float tooltipFontSize = 0.5f;                     // 提示框字体大小
}
