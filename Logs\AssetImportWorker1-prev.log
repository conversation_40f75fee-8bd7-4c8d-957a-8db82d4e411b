Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.60f1 (5f63fdee6d95) revision 6251517'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 16193 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\unity\unity editor\2022.3.60f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/unity/administrator/again
-logFile
Logs/AssetImportWorker1.log
-srvPort
50742
Successfully changed project path to: D:/unity/administrator/again
D:/unity/administrator/again
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [7732]  Target information:

Player connection [7732]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1152698756 [EditorId] 1152698756 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-8U99CKK) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [7732] Host joined multi-casting on [***********:54997]...
Player connection [7732] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
Refreshing native plugins compatible for Editor in 10.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.60f1 (5f63fdee6d95)
[Subsystems] Discovering subsystems at path D:/unity/unity editor/2022.3.60f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/unity/administrator/again/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x24c9)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.7261
Initialize mono
Mono path[0] = 'D:/unity/unity editor/2022.3.60f1/Editor/Data/Managed'
Mono path[1] = 'D:/unity/unity editor/2022.3.60f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/unity/unity editor/2022.3.60f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56120
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/unity/unity editor/2022.3.60f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004384 seconds.
- Loaded All Assemblies, in  0.336 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.264 seconds
Domain Reload Profiling: 599ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (130ms)
		LoadAssemblies (107ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (128ms)
			TypeCache.Refresh (127ms)
				TypeCache.ScanAssembly (112ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (264ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (215ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (149ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.714 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.600 seconds
Domain Reload Profiling: 1313ms
	BeginReloadAssembly (140ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (493ms)
		LoadAssemblies (349ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (184ms)
				TypeCache.ScanAssembly (163ms)
			ScanForSourceGeneratedMonoScriptInfo (22ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (600ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (436ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (62ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 2.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4169 Unused Serialized files (Serialized files now loaded: 0)
Unloading 35 unused Assets / (57.7 KB). Loaded Objects now: 4642.
Memory consumption went from 152.4 MB to 152.3 MB.
Total: 3.192200 ms (FindLiveObjects: 0.271900 ms CreateObjectMapping: 0.057700 ms MarkObjects: 2.722700 ms  DeleteObjects: 0.137500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 383379.194052 seconds.
  path: Assets/Scipts/Models/Card.cs
  artifactKey: Guid(7a0af508461911b489c9914d047d123f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scipts/Models/Card.cs using Guid(7a0af508461911b489c9914d047d123f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '1570a459b3beed4ed629ba1f8bd06d79') in 0.002910 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.057 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.609 seconds
Domain Reload Profiling: 1667ms
	BeginReloadAssembly (595ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (407ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (379ms)
		LoadAssemblies (449ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (45ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (12ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (610ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (426ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (314ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 4.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4142 Unused Serialized files (Serialized files now loaded: 0)
Unloading 23 unused Assets / (32.3 KB). Loaded Objects now: 4646.
Memory consumption went from 146.7 MB to 146.7 MB.
Total: 4.353400 ms (FindLiveObjects: 0.475700 ms CreateObjectMapping: 0.128100 ms MarkObjects: 3.645900 ms  DeleteObjects: 0.102400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/CardData.cs: ff29352d81ab3a40706c7a187282264f -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/Singleton.cs: 7d654a2b5b3a10da3e7d8472196e96d1 -> 90647cc400823b41df847a6cbfc29026
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/CardView.cs: 97ccb371022cdee3fad29848e30eb3d3 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CardViewCreator.cs: 5359b83fd4af1c72be519b1148b947d4 -> 
  custom:scripting/monoscript/fileName/Card.cs: 02b201958ea356700aca0ebbefbc1394 -> 
  custom:scripting/monoscript/fileName/HandView.cs: 49e3bb20ccd0100998a8aac5e75b9253 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TestSystem.cs: e290c9deefd04e8a648d4e2c5e036f71 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0dfbc2c81349b992f89d0f9a0a720377 -> 
========================================================================
Received Import Request.
  Time since last request: 9929.921545 seconds.
  path: Assets/Scipts/Models/Card.cs
  artifactKey: Guid(7a0af508461911b489c9914d047d123f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scipts/Models/Card.cs using Guid(7a0af508461911b489c9914d047d123f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'bafd694961822f9a020a7ad3de8422aa') in 0.003123 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.488 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.859 seconds
Domain Reload Profiling: 1346ms
	BeginReloadAssembly (167ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (250ms)
		LoadAssemblies (312ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (859ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (378ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (60ms)
			ProcessInitializeOnLoadAttributes (277ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4142 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (31.6 KB). Loaded Objects now: 4650.
Memory consumption went from 146.7 MB to 146.7 MB.
Total: 3.342400 ms (FindLiveObjects: 0.341400 ms CreateObjectMapping: 0.109900 ms MarkObjects: 2.845100 ms  DeleteObjects: 0.045200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/CardData.cs: ff29352d81ab3a40706c7a187282264f -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/Singleton.cs: 7d654a2b5b3a10da3e7d8472196e96d1 -> 90647cc400823b41df847a6cbfc29026
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/CardView.cs: 97ccb371022cdee3fad29848e30eb3d3 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CardViewCreator.cs: 5359b83fd4af1c72be519b1148b947d4 -> 
  custom:scripting/monoscript/fileName/Card.cs: da62cc4d0333d0d7798942472d99e053 -> 
  custom:scripting/monoscript/fileName/HandView.cs: 49e3bb20ccd0100998a8aac5e75b9253 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TestSystem.cs: e290c9deefd04e8a648d4e2c5e036f71 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 5e865202322b2d979ff495c3553fada9 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.505 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.829 seconds
Domain Reload Profiling: 1333ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (254ms)
		LoadAssemblies (329ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (28ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (829ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (379ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (61ms)
			ProcessInitializeOnLoadAttributes (277ms)
			ProcessInitializeOnLoadMethodAttributes (29ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4143 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (31.6 KB). Loaded Objects now: 4654.
Memory consumption went from 147.0 MB to 147.0 MB.
Total: 3.912000 ms (FindLiveObjects: 0.459500 ms CreateObjectMapping: 0.105900 ms MarkObjects: 3.296000 ms  DeleteObjects: 0.049400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/CardData.cs: ff29352d81ab3a40706c7a187282264f -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/Singleton.cs: 7d654a2b5b3a10da3e7d8472196e96d1 -> 90647cc400823b41df847a6cbfc29026
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/CardView.cs: 97ccb371022cdee3fad29848e30eb3d3 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CardViewCreator.cs: 5359b83fd4af1c72be519b1148b947d4 -> 
  custom:scripting/monoscript/fileName/Card.cs: 02b201958ea356700aca0ebbefbc1394 -> 
  custom:scripting/monoscript/fileName/HandView.cs: 49e3bb20ccd0100998a8aac5e75b9253 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TestSystem.cs: e290c9deefd04e8a648d4e2c5e036f71 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0dfbc2c81349b992f89d0f9a0a720377 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.497 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.000 seconds
Domain Reload Profiling: 1496ms
	BeginReloadAssembly (164ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (263ms)
		LoadAssemblies (331ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1001ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (457ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (346ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 4.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4143 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (31.6 KB). Loaded Objects now: 4658.
Memory consumption went from 147.0 MB to 147.0 MB.
Total: 5.811700 ms (FindLiveObjects: 0.907500 ms CreateObjectMapping: 0.159700 ms MarkObjects: 4.689300 ms  DeleteObjects: 0.053400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/CardData.cs: ff29352d81ab3a40706c7a187282264f -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/Singleton.cs: 7d654a2b5b3a10da3e7d8472196e96d1 -> 90647cc400823b41df847a6cbfc29026
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/CardView.cs: 97ccb371022cdee3fad29848e30eb3d3 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CardViewCreator.cs: 5359b83fd4af1c72be519b1148b947d4 -> 
  custom:scripting/monoscript/fileName/Card.cs: 02b201958ea356700aca0ebbefbc1394 -> 
  custom:scripting/monoscript/fileName/HandView.cs: 49e3bb20ccd0100998a8aac5e75b9253 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TestSystem.cs: e290c9deefd04e8a648d4e2c5e036f71 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0dfbc2c81349b992f89d0f9a0a720377 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.516 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.891 seconds
Domain Reload Profiling: 1406ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (282ms)
		LoadAssemblies (341ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (29ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (892ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (402ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (60ms)
			ProcessInitializeOnLoadAttributes (299ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 4.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4143 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (31.7 KB). Loaded Objects now: 4662.
Memory consumption went from 147.0 MB to 147.0 MB.
Total: 3.818800 ms (FindLiveObjects: 0.358700 ms CreateObjectMapping: 0.108200 ms MarkObjects: 3.299400 ms  DeleteObjects: 0.051700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/CardData.cs: ff29352d81ab3a40706c7a187282264f -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/Singleton.cs: 7d654a2b5b3a10da3e7d8472196e96d1 -> 90647cc400823b41df847a6cbfc29026
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/CardView.cs: 97ccb371022cdee3fad29848e30eb3d3 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CardViewCreator.cs: 5359b83fd4af1c72be519b1148b947d4 -> 
  custom:scripting/monoscript/fileName/Card.cs: 02b201958ea356700aca0ebbefbc1394 -> 
  custom:scripting/monoscript/fileName/HandView.cs: 49e3bb20ccd0100998a8aac5e75b9253 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TestSystem.cs: e290c9deefd04e8a648d4e2c5e036f71 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0dfbc2c81349b992f89d0f9a0a720377 -> 
========================================================================
Received Import Request.
  Time since last request: 993.492646 seconds.
  path: Assets/Scipts/Views/HandView.cs
  artifactKey: Guid(ffe2a2ad8e3a87546be647b0a04fa94b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scipts/Views/HandView.cs using Guid(ffe2a2ad8e3a87546be647b0a04fa94b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '4af1d3fe63fab8830ba3d6435f592e0d') in 0.004570 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.471 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.906 seconds
Domain Reload Profiling: 1376ms
	BeginReloadAssembly (159ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (245ms)
		LoadAssemblies (310ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (906ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (439ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (64ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 4.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4142 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (31.7 KB). Loaded Objects now: 4666.
Memory consumption went from 146.7 MB to 146.7 MB.
Total: 6.286400 ms (FindLiveObjects: 0.797000 ms CreateObjectMapping: 0.195700 ms MarkObjects: 5.200900 ms  DeleteObjects: 0.091800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/CardData.cs: ff29352d81ab3a40706c7a187282264f -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/Singleton.cs: 7d654a2b5b3a10da3e7d8472196e96d1 -> 90647cc400823b41df847a6cbfc29026
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/CardView.cs: 97ccb371022cdee3fad29848e30eb3d3 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CardViewCreator.cs: 5359b83fd4af1c72be519b1148b947d4 -> 
  custom:scripting/monoscript/fileName/Card.cs: 02b201958ea356700aca0ebbefbc1394 -> 
  custom:scripting/monoscript/fileName/HandView.cs: 49e3bb20ccd0100998a8aac5e75b9253 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TestSystem.cs: e290c9deefd04e8a648d4e2c5e036f71 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0dfbc2c81349b992f89d0f9a0a720377 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.493 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.948 seconds
Domain Reload Profiling: 1441ms
	BeginReloadAssembly (184ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (241ms)
		LoadAssemblies (332ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (949ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (469ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (346ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 4.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4143 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (31.6 KB). Loaded Objects now: 4670.
Memory consumption went from 147.0 MB to 147.0 MB.
Total: 4.007400 ms (FindLiveObjects: 0.334800 ms CreateObjectMapping: 0.117200 ms MarkObjects: 3.507300 ms  DeleteObjects: 0.046000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/CardData.cs: ff29352d81ab3a40706c7a187282264f -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/Singleton.cs: 7d654a2b5b3a10da3e7d8472196e96d1 -> 90647cc400823b41df847a6cbfc29026
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/CardView.cs: 97ccb371022cdee3fad29848e30eb3d3 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CardViewCreator.cs: 5359b83fd4af1c72be519b1148b947d4 -> 
  custom:scripting/monoscript/fileName/Card.cs: 02b201958ea356700aca0ebbefbc1394 -> 
  custom:scripting/monoscript/fileName/HandView.cs: 49e3bb20ccd0100998a8aac5e75b9253 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TestSystem.cs: e290c9deefd04e8a648d4e2c5e036f71 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0dfbc2c81349b992f89d0f9a0a720377 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.451 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.937 seconds
Domain Reload Profiling: 1387ms
	BeginReloadAssembly (140ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (244ms)
		LoadAssemblies (291ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (938ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (452ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (64ms)
			ProcessInitializeOnLoadAttributes (338ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 3.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4143 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (31.7 KB). Loaded Objects now: 4674.
Memory consumption went from 147.0 MB to 147.0 MB.
Total: 4.693600 ms (FindLiveObjects: 0.491400 ms CreateObjectMapping: 0.155600 ms MarkObjects: 3.991100 ms  DeleteObjects: 0.054500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/CardData.cs: ff29352d81ab3a40706c7a187282264f -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/Singleton.cs: 7d654a2b5b3a10da3e7d8472196e96d1 -> 90647cc400823b41df847a6cbfc29026
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/CardView.cs: 97ccb371022cdee3fad29848e30eb3d3 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CardViewCreator.cs: 5359b83fd4af1c72be519b1148b947d4 -> 
  custom:scripting/monoscript/fileName/Card.cs: 02b201958ea356700aca0ebbefbc1394 -> 
  custom:scripting/monoscript/fileName/HandView.cs: 49e3bb20ccd0100998a8aac5e75b9253 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TestSystem.cs: e290c9deefd04e8a648d4e2c5e036f71 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0dfbc2c81349b992f89d0f9a0a720377 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.501 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.961 seconds
Domain Reload Profiling: 1461ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (274ms)
		LoadAssemblies (337ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (20ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (961ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (439ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (323ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 5.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4143 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (31.6 KB). Loaded Objects now: 4678.
Memory consumption went from 147.0 MB to 147.0 MB.
Total: 5.023800 ms (FindLiveObjects: 0.388500 ms CreateObjectMapping: 0.119400 ms MarkObjects: 4.433800 ms  DeleteObjects: 0.081100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/CardData.cs: ff29352d81ab3a40706c7a187282264f -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/Singleton.cs: 7d654a2b5b3a10da3e7d8472196e96d1 -> 90647cc400823b41df847a6cbfc29026
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/CardView.cs: 97ccb371022cdee3fad29848e30eb3d3 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CardViewCreator.cs: 5359b83fd4af1c72be519b1148b947d4 -> 
  custom:scripting/monoscript/fileName/Card.cs: 02b201958ea356700aca0ebbefbc1394 -> 
  custom:scripting/monoscript/fileName/HandView.cs: 49e3bb20ccd0100998a8aac5e75b9253 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TestSystem.cs: e290c9deefd04e8a648d4e2c5e036f71 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0dfbc2c81349b992f89d0f9a0a720377 -> 
